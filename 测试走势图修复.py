#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试走势图修复效果
"""

import requests
import json
from datetime import datetime


def test_draw_history_api():
    """测试历史开奖API"""
    print("🔍 测试历史开奖API...")

    try:
        response = requests.get(
            "http://localhost:8000/api/draw/history?page=1&page_size=30")
        if response.status_code == 200:
            data = response.json()
            # 修复：API返回的data字段直接是列表，不是包含items的字典
            items = data.get('data', [])
            print(f"✅ API调用成功，获取到 {len(items)} 条历史记录")

            if items:
                print("📊 最新5期数据:")
                for i, item in enumerate(items[:5]):
                    expect = item.get('expect', 'N/A')
                    special_number = item.get('special_number', 'N/A')
                    draw_time = item.get('draw_time', 'N/A')
                    print(
                        f"  {i+1}. 期号: {expect}, 特码: {special_number}, 时间: {draw_time}")

                return items
            else:
                print("⚠️ 返回数据为空")
                return []
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return []


def test_trend_data_processing(history_data, test_number=2):
    """测试走势数据处理逻辑"""
    print(f"\n🎯 测试号码 {test_number} 的走势数据处理...")

    if not history_data:
        print("❌ 没有历史数据可供测试")
        return

    # 模拟前端的数据处理逻辑
    trend_data = []
    period_labels = []
    hit_count = 0

    # 按期号排序（从旧到新）
    sorted_draws = sorted(
        history_data, key=lambda x: int(x.get('expect', '0')))

    for draw in sorted_draws:
        special_number = draw.get('special_number')
        expect = draw.get('expect', 'N/A')

        if special_number is not None:
            is_hit = int(special_number) == test_number
            trend_data.append(1 if is_hit else 0)
            period_labels.append(expect)

            if is_hit:
                hit_count += 1

    total_periods = len(trend_data)
    hit_rate = (hit_count / total_periods * 100) if total_periods > 0 else 0

    # 计算当前遗漏
    current_missing = 0
    for i in range(len(trend_data) - 1, -1, -1):
        if trend_data[i] == 1:
            break
        current_missing += 1

    print(f"📈 走势数据统计:")
    print(f"  总期数: {total_periods}")
    print(f"  命中次数: {hit_count}")
    print(f"  命中率: {hit_rate:.1f}%")
    print(f"  当前遗漏: {current_missing} 期")

    # 显示最近10期的走势
    print(f"  最近10期走势: {trend_data[-10:]}")
    print(f"  对应期号: {period_labels[-10:]}")

    return {
        'total_periods': total_periods,
        'hit_count': hit_count,
        'hit_rate': hit_rate,
        'current_missing': current_missing,
        'trend_data': trend_data,
        'period_labels': period_labels
    }


def test_chart_data_format(trend_result):
    """测试图表数据格式"""
    print(f"\n📊 测试图表数据格式...")

    if not trend_result:
        print("❌ 没有走势数据可供测试")
        return

    trend_data = trend_result['trend_data']
    period_labels = trend_result['period_labels']

    # 生成图表数据
    hit_data = []
    miss_data = []

    for index, (period, is_hit) in enumerate(zip(period_labels, trend_data)):
        if is_hit:
            hit_data.append({
                'name': period,
                'value': [index, 1],
                'symbolSize': 15
            })
            miss_data.append({
                'name': period,
                'value': [index, 0],
                'symbolSize': 0
            })
        else:
            hit_data.append({
                'name': period,
                'value': [index, 1],
                'symbolSize': 0
            })
            miss_data.append({
                'name': period,
                'value': [index, 0],
                'symbolSize': 8
            })

    print(f"✅ 图表数据生成成功:")
    print(f"  开出数据点: {len([d for d in hit_data if d['symbolSize'] > 0])}")
    print(f"  未开出数据点: {len([d for d in miss_data if d['symbolSize'] > 0])}")

    # 显示前5个开出的数据点
    hit_points = [d for d in hit_data if d['symbolSize'] > 0]
    if hit_points:
        print(f"  开出期号示例: {[d['name'] for d in hit_points[:5]]}")

    return {
        'hit_data': hit_data,
        'miss_data': miss_data
    }


def test_echarts_config(chart_data, trend_result, test_number=2):
    """测试ECharts配置"""
    print(f"\n⚙️ 测试ECharts配置...")

    if not chart_data or not trend_result:
        print("❌ 缺少必要数据")
        return

    # 模拟ECharts配置
    config = {
        'title': {
            'text': f'号码 {test_number} 走势分析',
            'subtext': f'统计最近{trend_result["total_periods"]}期 | 命中率{trend_result["hit_rate"]:.1f}%'
        },
        'tooltip': {
            'trigger': 'axis',
            'formatter': '期号: {b}<br/>状态: {c}'
        },
        'legend': {
            'data': ['开出', '未开出']
        },
        'xAxis': {
            'type': 'category',
            'data': trend_result['period_labels']
        },
        'yAxis': {
            'type': 'value',
            'min': -0.2,
            'max': 1.2
        },
        'series': [
            {
                'name': '开出',
                'type': 'scatter',
                'data': chart_data['hit_data']
            },
            {
                'name': '未开出',
                'type': 'scatter',
                'data': chart_data['miss_data']
            }
        ]
    }

    print(f"✅ ECharts配置生成成功:")
    print(f"  标题: {config['title']['text']}")
    print(f"  副标题: {config['title']['subtext']}")
    print(f"  X轴数据点: {len(config['xAxis']['data'])}")
    print(f"  系列数量: {len(config['series'])}")

    return config


def test_frontend_integration():
    """测试前端集成"""
    print(f"\n🌐 测试前端集成...")

    try:
        response = requests.get("http://localhost:5181/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            print("💡 请在浏览器中:")
            print("   1. 打开统计页面")
            print("   2. 切换到'特码综合分析'标签")
            print("   3. 点击任意号码的'走势'按钮")
            print("   4. 查看走势图是否正常显示")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试走势图修复效果...")
    print("=" * 60)

    # 1. 测试API
    history_data = test_draw_history_api()

    # 2. 测试数据处理
    trend_result = test_trend_data_processing(history_data, test_number=2)

    # 3. 测试图表数据格式
    chart_data = test_chart_data_format(trend_result)

    # 4. 测试ECharts配置
    echarts_config = test_echarts_config(
        chart_data, trend_result, test_number=2)

    # 5. 测试前端集成
    frontend_ok = test_frontend_integration()

    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 走势图修复测试报告")
    print("=" * 60)

    tests = [
        ("API数据获取", bool(history_data)),
        ("走势数据处理", bool(trend_result)),
        ("图表数据格式", bool(chart_data)),
        ("ECharts配置", bool(echarts_config)),
        ("前端服务", frontend_ok)
    ]

    passed = sum(1 for _, result in tests if result)
    total = len(tests)

    print(f"测试结果: {passed}/{total} 通过")

    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")

    if passed == total:
        print(f"\n🎉 所有测试通过！走势图修复成功！")
        print("💡 修复要点:")
        print("   - 优化了数据获取逻辑，支持API和本地数据")
        print("   - 改进了图表配置，使用散点图显示走势")
        print("   - 增加了统计信息卡片，提供更多分析数据")
        print("   - 优化了错误处理和用户反馈")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")


if __name__ == "__main__":
    main()
