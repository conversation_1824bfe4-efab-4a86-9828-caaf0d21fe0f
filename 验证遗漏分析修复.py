#!/usr/bin/env python3
"""
验证遗漏分析修复
"""
import requests
import json

def verify_missing_analysis_fix():
    """验证遗漏分析修复"""
    print("🎉 验证遗漏分析修复")
    print("=" * 60)
    
    try:
        # 调用统计API
        response = requests.get("http://localhost:8000/api/draw/statistics", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'data' in data and 'missing' in data['data']:
                missing_data = data['data']['missing']
                
                print("✅ API响应正常")
                print(f"📊 遗漏数据结构: {list(missing_data.keys())}")
                
                # 验证当前遗漏数据
                current = missing_data.get('current', {})
                non_zero_current = sum(1 for v in current.values() if v > 0)
                total_current = sum(int(v) for v in current.values())
                
                print(f"📈 当前遗漏统计:")
                print(f"   - 非零值数量: {non_zero_current}/49")
                print(f"   - 总遗漏期数: {total_current}")
                print(f"   - 平均遗漏: {total_current/49:.1f}期")
                
                # 验证最大遗漏数据
                max_missing = missing_data.get('max', {})
                non_zero_max = sum(1 for v in max_missing.values() if v > 0)
                max_value = max(int(v) for v in max_missing.values())
                
                print(f"📈 最大遗漏统计:")
                print(f"   - 非零值数量: {non_zero_max}/49")
                print(f"   - 最大遗漏值: {max_value}期")
                
                # 验证最后出现数据
                last_appearance = missing_data.get('lastAppearance', {})
                non_null_last = sum(1 for v in last_appearance.values() if v is not None)
                
                print(f"📈 最后出现统计:")
                print(f"   - 非空值数量: {non_null_last}/49")
                
                # 显示部分具体数据
                print(f"\n🔢 部分号码的遗漏详情:")
                for i in range(1, 11):
                    key = str(i)
                    current_val = current.get(key, 0)
                    max_val = max_missing.get(key, 0)
                    last_val = last_appearance.get(key, 'None')
                    print(f"   号码{i:2d}: 当前{current_val:3d}期, 最大{max_val:3d}期, 最后出现{last_val}")
                
                # 验证修复成功
                if non_zero_current > 40:  # 大部分号码应该有遗漏值
                    print(f"\n🎉 修复验证成功!")
                    print(f"   ✅ 遗漏数据计算正常")
                    print(f"   ✅ 前端应该能正常显示图表")
                    print(f"   ✅ 不再显示'暂无数据'")
                else:
                    print(f"\n❌ 修复可能不完整")
                    print(f"   - 遗漏值数量偏少: {non_zero_current}/49")
                    
            else:
                print("❌ API响应中缺少遗漏数据")
                
        else:
            print(f"❌ API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")

def show_usage_instructions():
    """显示使用说明"""
    print(f"\n📖 前端使用说明")
    print("=" * 60)
    
    print("🎯 访问遗漏分析:")
    print("   1. 打开浏览器访问: http://localhost:3000/statistics")
    print("   2. 滚动到页面下方的'特码遗漏分析'部分")
    print("   3. 确认图表显示真实数据，不再是'暂无数据'")
    
    print(f"\n📊 遗漏分析功能:")
    print("   1. 号码视图:")
    print("      - 显示1-49号码的当前遗漏期数")
    print("      - 柱状图高度表示遗漏期数")
    print("      - 颜色深浅表示遗漏程度")
    
    print("   2. 波色视图:")
    print("      - 按红波、蓝波、绿波分组显示")
    print("      - 每组显示对应号码的遗漏情况")
    
    print("   3. 生肖视图:")
    print("      - 显示12个生肖的遗漏情况")
    print("      - 可切换柱状图和雷达图")
    
    print(f"\n🎮 交互功能:")
    print("   - 悬停查看详细信息")
    print("   - 点击图例控制显示/隐藏")
    print("   - 切换不同视图模式")
    print("   - 缩放和平移图表")

def main():
    """主函数"""
    print("🔍 遗漏分析修复验证")
    print("=" * 70)
    
    # 验证修复
    verify_missing_analysis_fix()
    
    # 显示使用说明
    show_usage_instructions()
    
    print(f"\n🎉 验证完成!")
    print("💡 现在可以刷新前端页面查看修复效果")

if __name__ == "__main__":
    main()
