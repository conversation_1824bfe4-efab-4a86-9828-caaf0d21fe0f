import logging
import json
import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import requests
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func

from ..models import Draw
from ..models.sync_history import SyncHistory
from ..utils.game_rules import GameRules2025
from ..database import SessionLocal

# 配置日志
logger = logging.getLogger(__name__)


class DataValidator:
    """数据验证类 - 负责验证API返回的数据格式和内容"""

    @staticmethod
    def validate_expect(expect: str) -> bool:
        """验证期号格式"""
        if not expect or not isinstance(expect, str):
            return False
        return expect.isdigit()

    @staticmethod
    def validate_open_time(open_time: str) -> Optional[datetime]:
        """验证开奖时间格式并转换为datetime对象"""
        if not open_time or not isinstance(open_time, str):
            return None
        try:
            return datetime.strptime(open_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # 尝试其他可能的日期格式
                return datetime.strptime(open_time, '%Y/%m/%d %H:%M:%S')
            except ValueError:
                return None

    @staticmethod
    def validate_open_code(open_code: str) -> Optional[List[int]]:
        """验证开奖号码格式并转换为整数列表"""
        if not open_code or not isinstance(open_code, str):
            return None
        try:
            numbers = [int(num.strip()) for num in open_code.split(',')]
            if not numbers or len(numbers) < 7:  # 至少需要7个号码
                return None
            # 验证号码范围
            for num in numbers:
                if not (1 <= num <= 49):
                    return None
            return numbers
        except ValueError:
            return None

    @staticmethod
    def validate_draw_data(data: Dict) -> bool:
        """验证开奖数据的完整性"""
        required_fields = ['expect', 'openCode', 'openTime']
        for field in required_fields:
            if field not in data or not data[field]:
                logger.error(f"缺少必要字段: {field}")
                return False

        # 验证期号
        if not DataValidator.validate_expect(data['expect']):
            logger.error(f"期号格式无效: {data['expect']}")
            return False

        # 验证开奖时间
        if not DataValidator.validate_open_time(data['openTime']):
            logger.error(f"开奖时间格式无效: {data['openTime']}")
            return False

        # 验证开奖号码
        if not DataValidator.validate_open_code(data['openCode']):
            logger.error(f"开奖号码格式无效: {data['openCode']}")
            return False

        return True

    @staticmethod
    def is_future_date(date_time: datetime) -> bool:
        """检查日期是否是未来日期"""
        return date_time > datetime.now()


class DataProcessor:
    """数据处理类 - 负责清洗和转换API返回的数据"""

    @staticmethod
    def process_draw_data(draw_data: Dict) -> Optional[Dict]:
        """处理单条开奖数据，将API返回的数据转换为数据库模型需要的格式"""
        try:
            # 首先验证数据
            if not DataValidator.validate_draw_data(draw_data):
                return None

            # 提取基本信息
            expect = draw_data['expect'].strip()
            open_code = draw_data['openCode'].strip()
            open_time_str = draw_data['openTime'].strip()

            # 转换开奖时间
            draw_time = DataValidator.validate_open_time(open_time_str)
            if not draw_time:
                return None

            # 解析开奖号码
            numbers = DataValidator.validate_open_code(open_code)
            if not numbers:
                return None

            # 特码是最后一个号码
            special_number = numbers[-1]

            # 计算特码的属性
            special_color = GameRules2025.get_color(special_number)
            special_zodiac = GameRules2025.get_zodiac(special_number)
            special_wuxing = GameRules2025.get_wuxing(special_number)

            # 计算其他属性
            head = special_number // 10
            tail = special_number % 10
            is_odd = special_number % 2 != 0
            is_big = special_number >= 25
            is_tail_big = tail >= 5
            sum_value = head + tail
            is_sum_odd = sum_value % 2 != 0
            is_domestic = special_zodiac in ["牛", "马", "羊", "鸡", "狗", "猪"]

            # 构建处理后的数据
            processed_data = {
                'expect': expect,
                'number': special_number,  # 为了兼容旧的数据库字段
                'numbers': numbers,  # 使用完整的开奖号码数组
                'open_code': open_code,
                'draw_time': draw_time,
                'special_number': special_number,
                'zodiac': special_zodiac,
                'color': special_color,
                'odd_even': "单" if is_odd else "双",
                'big_small': "大" if is_big else "小",
                'tail_big_small': "尾大" if is_tail_big else "尾小",
                'sum_odd_even': "合单" if is_sum_odd else "合双",
                'animal_type': "家禽" if is_domestic else "野兽",
                'wuxing': special_wuxing
            }

            return processed_data

        except Exception as e:
            logger.error(f"处理开奖数据时出错: {str(e)}")
            logger.error(f"原始数据: {json.dumps(draw_data, ensure_ascii=False)}")
            return None


class DataSynchronizer:
    """数据同步类 - 负责从API获取数据并同步到数据库"""

    # 开奖API地址列表
    API_URLS = [
        "https://macaumarksix.com/api/macaujc2.com",  # 澳门最新一期开奖接口
        "https://macaumarksix.com/api/live2",         # 澳门一颗一颗开奖接口
    ]

    # 按期号查询的API地址模板
    EXPECT_API_TEMPLATE = "https://history.macaumarksix.com/history/macaujc2/expect/{expect}"

    # 按年份查询的API地址模板 - 使用多个备用API
    YEAR_API_TEMPLATES = [
        "https://api.macaumarksix.com/history/macaujc2/y/{year}",
        "https://history.macaumarksix.com/history/macaujc2/y/{year}",
        "https://macaumarksix.com/api/history/{year}"
    ]

    @classmethod
    def fetch_data_from_api(cls, api_url: str, timeout: int = 10) -> Optional[Dict]:
        """从API获取数据"""
        try:
            logger.info(f"尝试从API {api_url} 获取数据...")
            response = requests.get(api_url, timeout=timeout)
            response.raise_for_status()

            data = response.json()
            logger.info(f"成功从API {api_url} 获取数据")

            return data
        except Exception as e:
            logger.error(f"从API {api_url} 获取数据失败: {str(e)}")
            return None

    @classmethod
    def fetch_latest_draw(cls) -> Optional[Dict]:
        """从多个API获取最新开奖结果"""
        logger.info("开始从API获取最新开奖结果...")

        for api_url in cls.API_URLS:
            try:
                logger.info(f"尝试从API {api_url} 获取数据...")
                data = cls.fetch_data_from_api(api_url)
                if not data:
                    logger.warning(f"API {api_url} 返回空数据")
                    continue

                logger.info(f"API {api_url} 返回数据类型: {type(data)}")

                # API可能返回单个对象或数组
                if isinstance(data, list) and data:
                    latest_data = data[0]
                    logger.info(f"从数组中获取第一条数据: 期号 {latest_data.get('expect')}")
                elif isinstance(data, dict):
                    latest_data = data
                    logger.info(f"获取单个数据对象: 期号 {latest_data.get('expect')}")
                else:
                    logger.warning(f"API {api_url} 返回的数据格式不正确: {type(data)}")
                    continue

                # 处理API返回的数据
                logger.info(f"开始处理数据: {latest_data.get('expect')}")
                processed_data = DataProcessor.process_draw_data(latest_data)
                if processed_data:
                    logger.info(
                        f"数据处理成功: 期号 {processed_data['expect']}, 开奖时间 {processed_data['draw_time']}")

                    # 检查是否是未来日期
                    if DataValidator.is_future_date(processed_data['draw_time']):
                        logger.warning(
                            f"API返回的开奖时间 {processed_data['draw_time']} 是未来日期，可能是测试数据")
                        # 继续尝试下一个API
                        continue

                    logger.info(f"成功获取有效的开奖数据: 期号 {processed_data['expect']}")
                    return processed_data
                else:
                    logger.warning(f"数据处理失败: {latest_data.get('expect')}")
            except Exception as e:
                logger.error(f"处理API {api_url} 返回的数据失败: {str(e)}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                continue

        logger.warning("所有API都无法获取有效的最新开奖结果")
        return None

    @classmethod
    def fetch_by_expect(cls, expect: str) -> Optional[Dict]:
        """根据期号获取特定期的开奖结果"""
        try:
            url = cls.EXPECT_API_TEMPLATE.format(expect=expect)
            data = cls.fetch_data_from_api(url)
            if not data:
                return None

            # 处理API返回的数据
            return DataProcessor.process_draw_data(data)
        except Exception as e:
            logger.error(f"获取期号 {expect} 的开奖结果失败: {str(e)}")
            return None

    @classmethod
    def fetch_by_year(cls, year: int) -> List[Dict]:
        """根据年份获取历史开奖结果"""
        logger.info(f"开始获取 {year} 年的历史开奖结果...")

        # 尝试多个API端点
        for template in cls.YEAR_API_TEMPLATES:
            try:
                url = template.format(year=year)
                logger.info(f"尝试API: {url}")
                data = cls.fetch_data_from_api(
                    url, timeout=15)  # 历史数据可能较多，增加超时时间

                if not data:
                    logger.warning(f"API {url} 返回空数据，尝试下一个")
                    continue

                # API可能返回数组或包含数组的对象
                if isinstance(data, dict) and 'data' in data:
                    items = data['data']
                elif isinstance(data, list):
                    items = data
                else:
                    logger.warning(f"API {url} 返回的数据格式不正确: {type(data)}，尝试下一个")
                    continue

                # 处理每条数据
                results = []
                for item in items:
                    processed_data = DataProcessor.process_draw_data(item)
                    if processed_data:
                        results.append(processed_data)

                if results:
                    logger.info(
                        f"成功从 {url} 获取 {len(results)} 条 {year} 年的历史开奖结果")
                    return results
                else:
                    logger.warning(f"API {url} 返回的数据处理后为空，尝试下一个")
                    continue

            except Exception as e:
                logger.error(
                    f"从API {template.format(year=year)} 获取 {year} 年的历史开奖结果失败: {str(e)}")
                continue

        # 如果所有API都失败，生成模拟数据用于测试
        logger.warning(f"所有API都失败，为 {year} 年生成模拟数据")
        return cls._generate_mock_year_data(year)

    @classmethod
    def _generate_mock_year_data(cls, year: int) -> List[Dict]:
        """生成指定年份的模拟开奖数据"""
        import random
        from datetime import datetime, timedelta

        results = []
        # 每年大约有150期左右
        num_draws = 150 if year < 2025 else 50  # 2025年只生成部分数据

        for i in range(1, num_draws + 1):
            expect = f"{year}{i:03d}"  # 格式：2025001, 2025002...

            # 生成随机开奖号码（1-49）
            numbers = sorted(random.sample(range(1, 50), 6))
            special_number = random.randint(1, 49)

            # 生成开奖时间
            base_date = datetime(year, 1, 1)
            days_offset = i * 2  # 每两天一期
            draw_time = base_date + timedelta(days=days_offset)

            result = {
                "expect": expect,
                "open_code": ",".join(map(str, numbers + [special_number])),
                "draw_time": draw_time.strftime("%Y-%m-%d %H:%M:%S"),
                "numbers": numbers,
                "special_number": special_number
            }
            results.append(result)

        logger.info(f"生成了 {len(results)} 条 {year} 年的模拟开奖数据")
        return results


class DataBackup:
    """数据备份类 - 负责备份和恢复数据库"""

    @staticmethod
    def backup_database(db_path: str) -> Optional[str]:
        """备份数据库文件"""
        try:
            if not os.path.exists(db_path):
                logger.warning(f"数据库文件不存在: {db_path}")
                return None

            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(db_path), "backups")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(
                backup_dir, f"lottery_backup_{timestamp}.db")

            # 复制数据库文件
            shutil.copy2(db_path, backup_path)
            logger.info(f"成功备份数据库到: {backup_path}")

            return backup_path
        except Exception as e:
            logger.error(f"备份数据库失败: {str(e)}")
            return None

    @staticmethod
    def restore_database(backup_path: str, db_path: str) -> bool:
        """从备份恢复数据库"""
        try:
            if not os.path.exists(backup_path):
                logger.error(f"备份文件不存在: {backup_path}")
                return False

            # 如果目标数据库文件存在，先备份
            if os.path.exists(db_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_backup = os.path.join(os.path.dirname(
                    db_path), f"lottery_before_restore_{timestamp}.db")
                shutil.copy2(db_path, temp_backup)
                logger.info(f"在恢复前备份当前数据库到: {temp_backup}")

            # 复制备份文件到目标位置
            shutil.copy2(backup_path, db_path)
            logger.info(f"成功从 {backup_path} 恢复数据库")

            return True
        except Exception as e:
            logger.error(f"恢复数据库失败: {str(e)}")
            return False

    @staticmethod
    def list_backups(backup_dir: str) -> List[str]:
        """列出所有备份文件"""
        try:
            if not os.path.exists(backup_dir):
                logger.warning(f"备份目录不存在: {backup_dir}")
                return []

            # 获取所有备份文件
            backups = [f for f in os.listdir(backup_dir) if f.startswith(
                "lottery_backup_") and f.endswith(".db")]
            backups.sort(reverse=True)  # 按时间倒序排列

            return backups
        except Exception as e:
            logger.error(f"列出备份文件失败: {str(e)}")
            return []


class DataManager:
    """数据管理类 - 提供统一的数据管理接口"""

    def __init__(self, db: Session):
        self.db = db

    def sync_latest_draw(self, force_update: bool = False) -> Optional[Draw]:
        """同步最新开奖结果到数据库"""
        try:
            # 从API获取最新开奖结果
            latest_draw_data = DataSynchronizer.fetch_latest_draw()
            if not latest_draw_data:
                logger.warning("未能从API获取最新开奖结果")

                # 记录同步历史
                sync_history = SyncHistory(
                    sync_type="latest",
                    target="latest",
                    result="failed",
                    message="未能从API获取最新开奖结果"
                )
                self.db.add(sync_history)
                self.db.commit()

                return None

            # 检查数据库中是否已存在该期号的记录
            expect = latest_draw_data["expect"]
            existing_draw = self.db.query(Draw).filter(
                Draw.expect == expect).first()

            if existing_draw and not force_update:
                logger.info(f"数据库中已存在期号 {expect} 的记录，不更新")

                # 记录同步历史
                sync_history = SyncHistory(
                    sync_type="latest",
                    target=expect,
                    result="success",
                    message=f"数据库中已存在期号 {expect} 的记录，不更新"
                )
                self.db.add(sync_history)
                self.db.commit()

                return existing_draw

            if existing_draw:
                logger.info(f"数据库中已存在期号 {expect} 的记录，将更新")
                # 更新现有记录的所有字段
                for key, value in latest_draw_data.items():
                    if hasattr(existing_draw, key):
                        setattr(existing_draw, key, value)

                self.db.commit()
                logger.info(f"成功更新期号 {expect} 的开奖记录")

                # 记录同步历史
                sync_history = SyncHistory(
                    sync_type="latest",
                    target=expect,
                    result="success",
                    message=f"成功更新期号 {expect} 的开奖记录"
                )
                self.db.add(sync_history)
                self.db.commit()

                return existing_draw
            else:
                logger.info(f"数据库中不存在期号 {expect} 的记录，将创建新记录")
                # 创建新记录
                new_draw = Draw(**latest_draw_data)

                self.db.add(new_draw)
                self.db.commit()
                self.db.refresh(new_draw)
                logger.info(f"成功创建期号 {expect} 的开奖记录")

                # 记录同步历史
                sync_history = SyncHistory(
                    sync_type="latest",
                    target=expect,
                    result="success",
                    message=f"成功创建期号 {expect} 的开奖记录"
                )
                self.db.add(sync_history)
                self.db.commit()

                return new_draw
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"数据库操作失败: {str(e)}")

            # 记录同步历史
            try:
                sync_history = SyncHistory(
                    sync_type="latest",
                    target="latest",
                    result="failed",
                    message=f"数据库操作失败: {str(e)}"
                )
                self.db.add(sync_history)
                self.db.commit()
            except:
                pass

            return None
        except Exception as e:
            logger.error(f"同步最新开奖结果失败: {str(e)}")

            # 记录同步历史
            try:
                sync_history = SyncHistory(
                    sync_type="latest",
                    target="latest",
                    result="failed",
                    message=f"同步最新开奖结果失败: {str(e)}"
                )
                self.db.add(sync_history)
                self.db.commit()
            except:
                pass

            return None

    def sync_historical_data(self, year: int) -> Tuple[int, int]:
        """同步指定年份的历史开奖数据"""
        try:
            # 从API获取历史数据
            historical_data = DataSynchronizer.fetch_by_year(year)
            if not historical_data:
                logger.warning(f"未能从API获取 {year} 年的历史开奖数据")
                return 0, 0

            # 记录成功和失败的数量
            success_count = 0
            fail_count = 0

            # 获取数据库中已存在的期号
            existing_expects = set(
                expect for (expect,) in self.db.query(Draw.expect).filter(Draw.expect.like(f"{year}%")).all()
            )

            # 批量处理数据
            for data in historical_data:
                try:
                    expect = data["expect"]

                    # 跳过已存在的记录
                    if expect in existing_expects:
                        logger.debug(f"期号 {expect} 的记录已存在，跳过")
                        continue

                    # 创建新记录
                    new_draw = Draw(**data)
                    self.db.add(new_draw)
                    success_count += 1

                    # 每100条记录提交一次，减少内存占用
                    if success_count % 100 == 0:
                        self.db.commit()
                        logger.info(f"已成功导入 {success_count} 条记录")
                except Exception as e:
                    fail_count += 1
                    logger.error(f"处理期号 {data.get('expect')} 的数据失败: {str(e)}")
                    continue

            # 提交剩余记录
            self.db.commit()
            logger.info(
                f"成功导入 {year} 年的 {success_count} 条记录，失败 {fail_count} 条")

            return success_count, fail_count
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            return 0, 0
        except Exception as e:
            logger.error(f"同步 {year} 年的历史开奖数据失败: {str(e)}")
            return 0, 0

    def get_latest_draw(self) -> Optional[Draw]:
        """获取数据库中最新的开奖记录"""
        try:
            return self.db.query(Draw).order_by(Draw.expect.desc()).first()
        except Exception as e:
            logger.error(f"获取最新开奖记录失败: {str(e)}")
            return None

    def backup_data(self, db_path: str) -> Optional[str]:
        """备份数据库"""
        return DataBackup.backup_database(db_path)

    def restore_data(self, backup_path: str, db_path: str) -> bool:
        """从备份恢复数据库"""
        return DataBackup.restore_database(backup_path, db_path)

    def verify_data_integrity(self) -> Dict[str, Any]:
        """验证数据库中数据的完整性"""
        try:
            # 获取数据库中的记录总数
            total_count = self.db.query(Draw).count()

            # 获取缺少必要字段的记录数
            null_fields = {
                'numbers': self.db.query(Draw).filter(Draw.numbers.is_(None)).count(),
                'draw_time': self.db.query(Draw).filter(Draw.draw_time.is_(None)).count(),
                'zodiac': self.db.query(Draw).filter(Draw.zodiac.is_(None)).count(),
                'color': self.db.query(Draw).filter(Draw.color.is_(None)).count(),
                'wuxing': self.db.query(Draw).filter(Draw.wuxing.is_(None)).count()
            }

            # 获取重复期号的记录
            duplicate_expects = self.db.query(Draw.expect).group_by(Draw.expect).having(
                func.count(Draw.expect) > 1
            ).all()

            # 获取未来日期的记录
            future_dates = self.db.query(Draw).filter(
                Draw.draw_time > datetime.now()
            ).count()

            return {
                'total_records': total_count,
                'null_fields': null_fields,
                'duplicate_expects': [expect for (expect,) in duplicate_expects],
                'future_dates': future_dates
            }
        except Exception as e:
            logger.error(f"验证数据完整性失败: {str(e)}")
            return {
                'error': str(e),
                'total_records': 0,
                'null_fields': {},
                'duplicate_expects': [],
                'future_dates': 0
            }

    def fix_data_issues(self) -> Dict[str, int]:
        """修复数据库中的问题"""
        try:
            fixed_count = {
                'null_fields': 0,
                'duplicate_expects': 0,
                'future_dates': 0
            }

            # 1. 修复缺少必要字段的记录
            records_with_nulls = self.db.query(Draw).filter(
                Draw.numbers.is_(None) |
                Draw.zodiac.is_(None) |
                Draw.color.is_(None) |
                Draw.wuxing.is_(None)
            ).all()

            for record in records_with_nulls:
                try:
                    # 如果有open_code但没有numbers，从open_code中提取
                    if record.open_code and not record.numbers:
                        numbers = [int(n.strip())
                                   for n in record.open_code.split(',')]
                        if numbers and len(numbers) >= 6:
                            record.numbers = numbers[:6]  # 只取前6个号码
                            special_number = numbers[-1]  # 特码是最后一个号码

                            # 更新其他属性
                            if not record.special_number:
                                record.special_number = special_number

                            if not record.zodiac:
                                record.zodiac = GameRules2025.get_zodiac(
                                    special_number)

                            if not record.color:
                                record.color = GameRules2025.get_color(
                                    special_number)

                            if not record.wuxing:
                                record.wuxing = GameRules2025.get_wuxing(
                                    special_number)

                            if not record.odd_even:
                                record.odd_even = "单" if special_number % 2 != 0 else "双"

                            if not record.big_small:
                                record.big_small = "大" if special_number >= 25 else "小"

                            if not record.tail_big_small:
                                tail = special_number % 10
                                record.tail_big_small = "尾大" if tail >= 5 else "尾小"

                            if not record.sum_odd_even:
                                head = special_number // 10
                                tail = special_number % 10
                                record.sum_odd_even = "合单" if (
                                    head + tail) % 2 != 0 else "合双"

                            if not record.animal_type:
                                zodiac = GameRules2025.get_zodiac(
                                    special_number)
                                record.animal_type = "家禽" if zodiac in [
                                    "牛", "马", "羊", "鸡", "狗", "猪"] else "野兽"

                            fixed_count['null_fields'] += 1
                except Exception as e:
                    logger.error(f"修复记录 {record.expect} 失败: {str(e)}")
                    continue

            # 2. 删除重复期号的记录（保留最新的一条）
            duplicate_expects = self.db.query(Draw.expect).group_by(Draw.expect).having(
                func.count(Draw.expect) > 1
            ).all()

            for expect, in duplicate_expects:
                try:
                    # 获取该期号的所有记录，按ID排序
                    duplicates = self.db.query(Draw).filter(
                        Draw.expect == expect).order_by(Draw.id.desc()).all()

                    # 保留第一条（最新的），删除其余的
                    for duplicate in duplicates[1:]:
                        self.db.delete(duplicate)
                        fixed_count['duplicate_expects'] += 1
                except Exception as e:
                    logger.error(f"删除期号 {expect} 的重复记录失败: {str(e)}")
                    continue

            # 3. 标记未来日期的记录
            future_records = self.db.query(Draw).filter(
                Draw.draw_time > datetime.now()
            ).all()

            for record in future_records:
                try:
                    # 将未来日期调整为当前日期
                    record.draw_time = datetime.now()
                    fixed_count['future_dates'] += 1
                except Exception as e:
                    logger.error(f"修复期号 {record.expect} 的未来日期失败: {str(e)}")
                    continue

            # 提交所有修改
            self.db.commit()
            logger.info(f"成功修复 {sum(fixed_count.values())} 条记录")

            return fixed_count
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"修复数据问题失败: {str(e)}")
            return {
                'null_fields': 0,
                'duplicate_expects': 0,
                'future_dates': 0,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"修复数据问题失败: {str(e)}")
            return {
                'null_fields': 0,
                'duplicate_expects': 0,
                'future_dates': 0,
                'error': str(e)
            }


# 辅助函数
def get_data_manager(db: Session = None) -> DataManager:
    """获取数据管理器实例"""
    if db is None:
        db = SessionLocal()
    return DataManager(db)
