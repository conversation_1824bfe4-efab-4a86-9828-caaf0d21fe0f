<template>
  <div class="chart-container">
    <div ref="chartRef" style="height: 400px"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { ZODIAC_MAPPING } from '@/config/settings'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  const option = {
    title: {
      text: '生肖走势分析'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: Object.keys(ZODIAC_MAPPING),
      type: 'scroll',
      orient: 'horizontal',
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map((_, index) => `第${index + 1}期`)
    },
    yAxis: {
      type: 'value'
    },
    series: generateSeries()
  }
  
  chart.setOption(option)
}

const generateSeries = () => {
  return Object.entries(ZODIAC_MAPPING).map(([zodiac, numbers]) => ({
    name: zodiac,
    type: 'line',
    smooth: true,
    data: calculateZodiacTrend(zodiac, numbers)
  }))
}

const calculateZodiacTrend = (zodiac, numbers) => {
  return props.data.map(period => {
    return period.filter(num => numbers.includes(num)).length
  })
}

watch(() => props.data, () => {
  initChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', () => {
    chart?.resize()
  })
})
</script> 