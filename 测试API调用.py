#!/usr/bin/env python3
"""
测试API调用
"""
import requests
import json

def test_api_call():
    """测试API调用"""
    print("=== 测试API调用 ===")
    
    try:
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        data = response.json()['data']
        
        basic_stats = data['basicStats']
        print(f"📊 API返回结果:")
        print(f"   总期数: {basic_stats['totalCount']}")
        print(f"   冷门号码:")
        for i, num in enumerate(basic_stats['coldNumbers'][:5]):
            print(f"     {i+1}. 号码{num['number']:02d}: {num['count']}次")
        
        # 检查numberFrequency
        number_freq = data['numberFrequency']
        zero_freq_numbers = [num for num, count in number_freq.items() if count == 0]
        print(f"\n📋 numberFrequency中0次的号码: {zero_freq_numbers}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试API调用...")
    
    if test_api_call():
        print("✅ API调用成功")
    else:
        print("❌ API调用失败")

if __name__ == "__main__":
    main()
