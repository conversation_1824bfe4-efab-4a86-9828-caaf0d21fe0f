from flask import Blueprint, jsonify, request, send_file
from models.prediction import db, DrawHistory
import logging
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta
import pandas as pd
import os
from sqlalchemy import and_

logger = logging.getLogger(__name__)
api_bp = Blueprint('api', __name__)


@api_bp.route('/historical-data')
def get_historical_data():
    logger.debug('Received request for historical data')
    try:
        # 获取查询参数
        limit = request.args.get('limit', type=int)
        page = request.args.get('page', type=int, default=1)
        page_size = request.args.get('pageSize', type=int, default=20)
        period = request.args.get('period', type=str)

        logger.debug(
            f'Query params: limit={limit}, page={page}, page_size={page_size}, period={period}')

        try:
            query = DrawHistory.query.order_by(DrawHistory.draw_time.desc())

            # 如果指定了期号，进行过滤
            if period:
                query = query.filter(DrawHistory.period.like(f'%{period}%'))

            # 如果指定了limit，使用limit
            if limit:
                records = query.limit(limit).all()
                total = len(records)
            else:
                # 否则使用分页
                pagination = query.paginate(
                    page=page, per_page=page_size, error_out=False)
                records = pagination.items
                total = pagination.total

            # 转换数据
            data = []
            for record in records:
                record_dict = record.to_dict()
                if record_dict:  # 只添加成功转换的记录
                    data.append(record_dict)

            logger.debug(f'Found {len(data)} records')

            response = {
                'code': 200,
                'message': 'success',
                'data': data
            }

            # 如果是分页请求，添加分页信息
            if not limit:
                response['pagination'] = {
                    'total': total,
                    'current': page,
                    'pageSize': page_size
                }

            return jsonify(response)

        except SQLAlchemyError as e:
            logger.error(f'Database error: {str(e)}')
            return jsonify({
                'code': 500,
                'message': 'Database error',
                'error': str(e)
            }), 500

    except Exception as e:
        logger.error(f'Server error: {str(e)}')
        return jsonify({
            'code': 500,
            'message': 'Server error',
            'error': str(e)
        }), 500


@api_bp.route('/predict', methods=['GET'])
def predict():
    try:
        data = {
            'code': 200,
            'message': 'success',
            'data': {
                'numbers': [1, 2, 3, 4, 5, 6],
                'confidence': 0.85
            }
        }
        return jsonify(data)
    except Exception as e:
        logger.error(f'Error: {str(e)}')
        return jsonify({'error': str(e)}), 500


@api_bp.route('/draw/latest', methods=['GET'])
def get_latest_draw():
    """获取最新开奖结果"""
    try:
        latest_draw = DrawHistory.query.order_by(
            DrawHistory.draw_time.desc()).first()
        if latest_draw:
            return jsonify({
                'code': 200,
                'message': 'success',
                'data': latest_draw.to_dict()
            })
        return jsonify({
            'code': 404,
            'message': 'No draw data found',
            'data': None
        }), 404
    except Exception as e:
        logger.error(f'Error getting latest draw: {str(e)}')
        return jsonify({
            'code': 500,
            'message': 'Server error',
            'error': str(e)
        }), 500


@api_bp.route('/draw/next', methods=['GET'])
def get_next_draw():
    """获取下期开奖信息"""
    try:
        latest_draw = DrawHistory.query.order_by(
            DrawHistory.draw_time.desc()).first()
        if latest_draw:
            # 假设每期间隔7天
            next_draw_time = latest_draw.draw_time + timedelta(days=7)
            next_period = str(int(latest_draw.period) + 1)

            return jsonify({
                'code': 200,
                'message': 'success',
                'data': {
                    'period': next_period,
                    'drawTime': next_draw_time.isoformat()
                }
            })
        return jsonify({
            'code': 404,
            'message': 'No draw data found',
            'data': None
        }), 404
    except Exception as e:
        logger.error(f'Error getting next draw: {str(e)}')
        return jsonify({
            'code': 500,
            'message': 'Server error',
            'error': str(e)
        }), 500


@api_bp.route('/draw/history', methods=['GET'])
def get_draw_history():
    """获取历史开奖记录"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('pageSize', 20, type=int)

        # 获取筛选参数
        start_period = request.args.get('startPeriod')
        end_period = request.args.get('endPeriod')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        special_number = request.args.get('specialNumber', type=int)
        special_zodiac = request.args.get('specialZodiac')
        special_color = request.args.get('specialColor')
        special_odd_even = request.args.get('specialOddEven')
        special_big_small = request.args.get('specialBigSmall')
        special_tail_big_small = request.args.get('specialTailBigSmall')
        special_sum_odd_even = request.args.get('specialSumOddEven')
        special_animal_type = request.args.get('specialAnimalType')
        special_wuxing = request.args.get('specialWuxing')

        # 构建查询
        query = DrawHistory.query

        # 应用筛选条件
        filters = []
        if start_period:
            filters.append(DrawHistory.period >= start_period)
        if end_period:
            filters.append(DrawHistory.period <= end_period)
        if start_date:
            filters.append(DrawHistory.draw_time >= f"{start_date} 00:00:00")
        if end_date:
            filters.append(DrawHistory.draw_time <= f"{end_date} 23:59:59")
        if special_number:
            filters.append(DrawHistory.special_number == special_number)

        # 应用所有筛选条件
        if filters:
            query = query.filter(and_(*filters))

        # 排序并分页
        query = query.order_by(DrawHistory.draw_time.desc())
        pagination = query.paginate(
            page=page, per_page=page_size, error_out=False)

        # 转换数据
        draws = [draw.to_dict() for draw in pagination.items]

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': draws,
            'total': pagination.total,
            'page': page,
            'pageSize': page_size
        })
    except Exception as e:
        logger.error(f'Error getting draw history: {str(e)}')
        return jsonify({
            'code': 500,
            'message': 'Server error',
            'error': str(e)
        }), 500


@api_bp.route('/draw/export', methods=['GET'])
def export_draw_history():
    """导出历史开奖记录"""
    try:
        # 获取筛选参数
        start_period = request.args.get('startPeriod')
        end_period = request.args.get('endPeriod')
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        special_number = request.args.get('specialNumber', type=int)
        special_zodiac = request.args.get('specialZodiac')
        special_color = request.args.get('specialColor')
        special_odd_even = request.args.get('specialOddEven')
        special_big_small = request.args.get('specialBigSmall')
        special_tail_big_small = request.args.get('specialTailBigSmall')
        special_sum_odd_even = request.args.get('specialSumOddEven')
        special_animal_type = request.args.get('specialAnimalType')
        special_wuxing = request.args.get('specialWuxing')

        # 构建查询
        query = DrawHistory.query

        # 应用筛选条件
        filters = []
        if start_period:
            filters.append(DrawHistory.period >= start_period)
        if end_period:
            filters.append(DrawHistory.period <= end_period)
        if start_date:
            filters.append(DrawHistory.draw_time >= f"{start_date} 00:00:00")
        if end_date:
            filters.append(DrawHistory.draw_time <= f"{end_date} 23:59:59")
        if special_number:
            filters.append(DrawHistory.special_number == special_number)

        # 应用所有筛选条件
        if filters:
            query = query.filter(and_(*filters))

        # 获取数据
        draws = query.order_by(DrawHistory.draw_time.desc()).all()

        # 转换为DataFrame
        data = []
        for draw in draws:
            draw_dict = draw.to_dict()
            if draw_dict:
                data.append(draw_dict)

        df = pd.DataFrame(data)

        # 确保临时目录存在
        os.makedirs('temp', exist_ok=True)

        # 生成文件名
        filename = f"lottery_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join('temp', filename)

        # 保存为Excel
        df.to_excel(filepath, index=False, engine='openpyxl')

        # 发送文件
        return send_file(
            filepath,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        logger.error(f'Error exporting draw history: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'error': str(e)
        }), 500
    finally:
        # 清理临时文件
        try:
            if 'filepath' in locals() and os.path.exists(filepath):
                os.remove(filepath)
        except Exception as e:
            logger.error(f'Error cleaning up temp file: {str(e)}')
