# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== 检查项目结构 ===" -ForegroundColor Green

# 检查必要的目录
$requiredDirs = @(
    "frontend/src",
    "frontend/src/api",
    "frontend/src/components",
    "frontend/src/views",
    "frontend/src/stores",
    "frontend/src/utils",
    "backend/api",
    "backend/models",
    "backend/utils",
    "backend/config",
    "data/db",
    "data/exports",
    "logs",
    "models",
    "scripts",
    "docker/frontend",
    "docker/backend"
)

foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "✓ $dir" -ForegroundColor Green
    } else {
        Write-Host "✗ $dir" -ForegroundColor Red
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "  已创建目录" -ForegroundColor Yellow
    }
}

# 检查必要的文件
$requiredFiles = @(
    "frontend/.env",
    "backend/.env",
    "docker/docker-compose.yml",
    "README.md"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
    }
}

Write-Host "`n=== 检查完成 ===" -ForegroundColor Green 