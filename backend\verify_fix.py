import sqlite3

# 直接连接数据库
conn = sqlite3.connect('marksix.db')
cursor = conn.cursor()

try:
    # 检查2025年的期号
    cursor.execute("""
        SELECT expect 
        FROM draws 
        WHERE expect LIKE '2025%'
        ORDER BY expect DESC
        LIMIT 20
    """)
    
    recent_draws = cursor.fetchall()
    print('2025年最新20期的期号:')
    for draw in recent_draws:
        print(f'  {draw[0]}')
    
    # 检查总数
    cursor.execute("""
        SELECT COUNT(*) 
        FROM draws 
        WHERE expect LIKE '2025%'
    """)
    
    total_2025 = cursor.fetchone()[0]
    print(f'\n2025年总期数: {total_2025}')
    
    # 检查期号范围
    cursor.execute("""
        SELECT MIN(expect), MAX(expect)
        FROM draws 
        WHERE expect LIKE '2025%'
    """)
    
    min_expect, max_expect = cursor.fetchone()
    print(f'期号范围: {min_expect} - {max_expect}')
    
    # 检查期号连续性
    cursor.execute("""
        SELECT expect 
        FROM draws 
        WHERE expect LIKE '2025%'
        ORDER BY expect
    """)
    
    all_expects = [row[0] for row in cursor.fetchall()]
    expect_nums = [int(expect[4:]) for expect in all_expects]
    
    if expect_nums:
        min_num = min(expect_nums)
        max_num = max(expect_nums)
        
        missing = []
        for num in range(min_num, max_num + 1):
            if num not in expect_nums:
                missing.append(f'2025{num:03d}')
        
        if missing:
            print(f'\n仍然缺失的期号: {missing}')
        else:
            print(f'\n期号已连续，从 2025{min_num:03d} 到 2025{max_num:03d}')

finally:
    conn.close()
