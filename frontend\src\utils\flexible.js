// 移动端适配
export function initFlexible() {
  const docEl = document.documentElement
  const dpr = window.devicePixelRatio || 1

  // 设置 data-dpr 属性
  docEl.setAttribute('data-dpr', dpr)

  // 设置 rem 基准值
  function setRemUnit() {
    const rem = docEl.clientWidth / 10
    docEl.style.fontSize = rem + 'px'
  }

  setRemUnit()

  // 监听窗口变化
  window.addEventListener('resize', setRemUnit)
  window.addEventListener('pageshow', function(e) {
    if (e.persisted) {
      setRemUnit()
    }
  })

  // 设置 viewport
  if (dpr >= 2) {
    const viewportMeta = document.createElement('meta')
    viewportMeta.setAttribute('name', 'viewport')
    viewportMeta.setAttribute('content', `width=device-width, initial-scale=${1/dpr}, maximum-scale=${1/dpr}, minimum-scale=${1/dpr}, user-scalable=no`)
    document.head.appendChild(viewportMeta)
  }
}

// 转换 px 到 rem
export function px2rem(px) {
  const ratio = 375 / 10 // 基准值：375px 宽度 = 10rem
  return `${px / ratio}rem`
}

// 判断是否为移动设备
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
} 