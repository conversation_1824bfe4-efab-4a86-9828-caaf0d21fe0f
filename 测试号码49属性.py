#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试号码49的属性计算是否正确
"""

def test_number_49_attributes():
    """测试号码49的所有属性"""
    print("🔍 测试号码49的属性计算...")
    
    number = 49
    
    # 基本属性
    print(f"📊 号码: {number}")
    
    # 1. 波色判断
    red_numbers = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]
    blue_numbers = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]
    green_numbers = [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
    
    if number in red_numbers:
        color = '红波'
    elif number in blue_numbers:
        color = '蓝波'
    elif number in green_numbers:
        color = '绿波'
    else:
        color = '未知'
    
    print(f"🌈 波色: {color}")
    
    # 2. 生肖判断
    zodiac_map = {
        1: '鼠', 2: '牛', 3: '虎', 4: '兔', 5: '龙', 6: '蛇',
        7: '马', 8: '羊', 9: '猴', 10: '鸡', 11: '狗', 12: '猪',
        13: '鼠', 14: '牛', 15: '虎', 16: '兔', 17: '龙', 18: '蛇',
        19: '马', 20: '羊', 21: '猴', 22: '鸡', 23: '狗', 24: '猪',
        25: '鼠', 26: '牛', 27: '虎', 28: '兔', 29: '龙', 30: '蛇',
        31: '马', 32: '羊', 33: '猴', 34: '鸡', 35: '狗', 36: '猪',
        37: '鼠', 38: '牛', 39: '虎', 40: '兔', 41: '龙', 42: '蛇',
        43: '马', 44: '羊', 45: '猴', 46: '鸡', 47: '狗', 48: '猪',
        49: '鼠'
    }
    
    zodiac = zodiac_map.get(number, '未知')
    print(f"🐲 生肖: {zodiac}")
    
    # 3. 五行判断
    wuxing_map = {
        3: "金", 4: "金", 11: "金", 12: "金", 25: "金", 26: "金", 33: "金", 34: "金", 41: "金", 42: "金",
        7: "木", 8: "木", 15: "木", 16: "木", 23: "木", 24: "木", 37: "木", 38: "木", 45: "木", 46: "木",
        13: "水", 14: "水", 21: "水", 22: "水", 29: "水", 30: "水", 43: "水", 44: "水",
        1: "火", 2: "火", 9: "火", 10: "火", 17: "火", 18: "火", 31: "火", 32: "火", 39: "火", 40: "火", 47: "火", 48: "火",
        5: "土", 6: "土", 19: "土", 20: "土", 27: "土", 28: "土", 35: "土", 36: "土", 49: "土"
    }
    
    wuxing = wuxing_map.get(number, '未知')
    print(f"🌍 五行: {wuxing}")
    
    # 4. 单双判断
    is_odd = number % 2 == 1
    odd_even = '单数' if is_odd else '双数'
    print(f"🔢 单双: {odd_even}")
    
    # 5. 大小判断
    is_big = number >= 25
    big_small = '大数' if is_big else '小数'
    print(f"📏 大小: {big_small}")
    
    # 6. 尾数分析
    tail_number = number % 10
    tail_is_odd = tail_number % 2 == 1
    tail_is_big = tail_number >= 5
    
    print(f"🎯 尾数: {tail_number}")
    print(f"🎯 尾数单双: 尾{'单' if tail_is_odd else '双'}")
    print(f"🎯 尾数大小: 尾{'大' if tail_is_big else '小'}")
    
    # 7. 合数分析
    sum_digits = sum(int(digit) for digit in str(number))
    sum_is_odd = sum_digits % 2 == 1
    sum_is_big = sum_digits >= 7
    
    print(f"➕ 合数: {sum_digits}")
    print(f"➕ 合数单双: 合{'单' if sum_is_odd else '双'}")
    print(f"➕ 合数大小: 合{'大' if sum_is_big else '小'}")
    
    return {
        'number': number,
        'color': color,
        'zodiac': zodiac,
        'wuxing': wuxing,
        'odd_even': odd_even,
        'big_small': big_small,
        'tail_number': tail_number,
        'tail_odd_even': '尾单' if tail_is_odd else '尾双',
        'tail_big_small': '尾大' if tail_is_big else '尾小',
        'sum_digits': sum_digits,
        'sum_odd_even': '合单' if sum_is_odd else '合双',
        'sum_big_small': '合大' if sum_is_big else '合小'
    }

def test_frontend_calculation():
    """测试前端计算逻辑"""
    print(f"\n🖥️ 测试前端计算逻辑...")
    
    number = 49
    
    # 模拟前端GameRules2025的计算
    print(f"📊 使用前端GameRules2025计算号码{number}:")
    
    # 波色
    green_numbers = [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
    color = '绿波' if number in green_numbers else '其他'
    print(f"   波色: {color}")
    
    # 生肖 (49 % 12 = 1, 对应牛，但实际应该是鼠)
    zodiac_index = (number - 1) % 12
    zodiac_names = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
    zodiac = zodiac_names[zodiac_index]
    print(f"   生肖: {zodiac} (计算: ({number}-1) % 12 = {zodiac_index})")
    
    # 五行
    wuxing_map = {49: "土"}
    wuxing = wuxing_map.get(number, '未知')
    print(f"   五行: {wuxing}")
    
    # 单双
    is_odd = number % 2 == 1
    print(f"   单双: {'单' if is_odd else '双'}")
    
    # 大小
    is_big = number >= 25
    print(f"   大小: {'大' if is_big else '小'}")
    
    # 尾数
    tail = number % 10
    tail_is_big = tail >= 5
    print(f"   尾数: {tail} ({'尾大' if tail_is_big else '尾小'})")
    
    # 合数
    sum_value = sum(int(d) for d in str(number))
    sum_is_odd = sum_value % 2 == 1
    sum_is_big = sum_value >= 7
    print(f"   合数: {sum_value} ({'合单' if sum_is_odd else '合双'}, {'合大' if sum_is_big else '合小'})")

def compare_with_user_report():
    """对比用户报告的数据"""
    print(f"\n📋 对比用户报告的数据...")
    
    user_report = {
        'number': 49,
        'zodiac': '🐍 蛇',
        'color': '绿波',
        'wuxing': '土',
        'odd_even': '单数',
        'big_small': '大数',
        'tail_number': 9,
        'tail_odd_even': '尾单',
        'tail_big_small': '尾大',
        'sum_digits': 13,
        'sum_odd_even': '合单',
        'sum_big_small': '合大'
    }
    
    calculated = test_number_49_attributes()
    
    print(f"🔍 数据对比结果:")
    
    # 检查每个属性
    checks = [
        ('生肖', user_report['zodiac'].replace('🐍 ', ''), calculated['zodiac']),
        ('波色', user_report['color'], calculated['color']),
        ('五行', user_report['wuxing'], calculated['wuxing']),
        ('单双', user_report['odd_even'], calculated['odd_even']),
        ('大小', user_report['big_small'], calculated['big_small']),
        ('尾数', user_report['tail_number'], calculated['tail_number']),
        ('尾数单双', user_report['tail_odd_even'], calculated['tail_odd_even']),
        ('尾数大小', user_report['tail_big_small'], calculated['tail_big_small']),
        ('合数', user_report['sum_digits'], calculated['sum_digits']),
        ('合数单双', user_report['sum_odd_even'], calculated['sum_odd_even']),
        ('合数大小', user_report['sum_big_small'], calculated['sum_big_small'])
    ]
    
    all_correct = True
    for attr_name, user_value, calc_value in checks:
        is_correct = user_value == calc_value
        status = "✅" if is_correct else "❌"
        print(f"   {status} {attr_name}: 用户报告={user_value}, 计算结果={calc_value}")
        if not is_correct:
            all_correct = False
    
    if all_correct:
        print(f"\n🎉 所有属性计算正确！")
    else:
        print(f"\n⚠️ 发现不一致的属性，需要修复")
    
    return all_correct

def main():
    """主测试函数"""
    print("🚀 开始测试号码49的属性计算...")
    print("=" * 60)
    
    # 1. 测试基本属性计算
    attributes = test_number_49_attributes()
    
    # 2. 测试前端计算逻辑
    test_frontend_calculation()
    
    # 3. 对比用户报告
    is_correct = compare_with_user_report()
    
    print("\n" + "=" * 60)
    print("📋 号码49属性测试报告")
    print("=" * 60)
    
    if is_correct:
        print("✅ 测试结果: 所有属性计算正确")
        print("💡 号码详情显示应该是准确的")
    else:
        print("❌ 测试结果: 发现属性计算不一致")
        print("💡 需要检查前端GameRules2025.js的实现")
    
    print(f"\n📊 号码49的正确属性:")
    for key, value in attributes.items():
        if key != 'number':
            print(f"   {key}: {value}")

if __name__ == "__main__":
    main()
