import requests
import json

# 测试年度API
year = 2025
url = f'https://api.macaumarksix.com/history/macaujc2/y/{year}'
print(f'测试API: {url}')

try:
    response = requests.get(url, timeout=10)
    print(f'状态码: {response.status_code}')
    print(f'响应头: {dict(response.headers)}')
    print(f'响应内容前500字符: {response.text[:500]}')
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f'数据类型: {type(data)}')
            if isinstance(data, list):
                print(f'数据长度: {len(data)}')
                if data:
                    print(f'第一条数据: {json.dumps(data[0], indent=2, ensure_ascii=False)}')
            elif isinstance(data, dict):
                print(f'数据键: {list(data.keys())}')
                if 'data' in data:
                    print(f'data字段长度: {len(data["data"])}')
        except Exception as e:
            print(f'JSON解析失败: {e}')
    else:
        print(f'请求失败，状态码: {response.status_code}')
        
except Exception as e:
    print(f'请求异常: {e}')
