/**
 * 统计分析报告生成器
 * 用于生成各种格式的统计分析报告
 */

import GameRules2025 from './GameRules2025.js'

export class ReportGenerator {
  constructor(data, config, filters) {
    this.data = data
    this.config = config
    this.filters = filters
    this.generatedAt = new Date()
  }

  /**
   * 生成完整的分析报告内容
   */
  async generateReport() {
    const sections = []

    // 根据配置生成不同的报告章节
    if (this.config.sections.includes('summary')) {
      sections.push(this.generateSummarySection())
    }
    if (this.config.sections.includes('frequency')) {
      sections.push(this.generateFrequencySection())
    }
    if (this.config.sections.includes('missing')) {
      sections.push(this.generateMissingSection())
    }
    if (this.config.sections.includes('pattern')) {
      sections.push(this.generatePatternSection())
    }
    if (this.config.sections.includes('trend')) {
      sections.push(this.generateTrendSection())
    }
    if (this.config.sections.includes('prediction')) {
      sections.push(this.generatePredictionSection())
    }
    if (this.config.sections.includes('charts')) {
      sections.push(this.generateChartsSection())
    }
    if (this.config.sections.includes('filtered')) {
      sections.push(this.generateFilteredSection())
    }

    return this.assembleReport(sections)
  }

  /**
   * 生成数据概览章节
   */
  generateSummarySection() {
    const totalNumbers = 49
    const analyzedNumbers = this.data.length
    const totalDraws = this.data.reduce((sum, item) => sum + item.count, 0)
    const avgFrequency = totalDraws / analyzedNumbers

    // 计算各类统计
    const hotNumbers = this.data.filter(item => this.getHotIndex(item.number) >= 60).length
    const coldNumbers = this.data.filter(item => this.getHotIndex(item.number) < 40).length
    const oddNumbers = this.data.filter(item => item.number % 2 === 1).length
    const evenNumbers = this.data.filter(item => item.number % 2 === 0).length
    const bigNumbers = this.data.filter(item => item.number >= 25).length
    const smallNumbers = this.data.filter(item => item.number < 25).length

    // 波色统计
    const colorStats = this.calculateColorStats()
    const zodiacStats = this.calculateZodiacStats()
    const elementStats = this.calculateElementStats()

    return {
      title: '📊 数据概览',
      content: `
        <div class="summary-section">
          <h2>📊 数据概览</h2>

          <div class="overview-grid">
            <div class="overview-card">
              <h3>🎯 基础统计</h3>
              <ul>
                <li>分析号码数量：${analyzedNumbers} / ${totalNumbers}</li>
                <li>总开奖次数：${totalDraws} 次</li>
                <li>平均出现频率：${avgFrequency.toFixed(2)} 次</li>
                <li>数据完整度：${((analyzedNumbers / totalNumbers) * 100).toFixed(1)}%</li>
              </ul>
            </div>

            <div class="overview-card">
              <h3>🔥 热度分布</h3>
              <ul>
                <li>热门号码：${hotNumbers} 个 (${((hotNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
                <li>冷门号码：${coldNumbers} 个 (${((coldNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
                <li>温号码：${analyzedNumbers - hotNumbers - coldNumbers} 个</li>
              </ul>
            </div>

            <div class="overview-card">
              <h3>🔢 属性分布</h3>
              <ul>
                <li>单数：${oddNumbers} 个 (${((oddNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
                <li>双数：${evenNumbers} 个 (${((evenNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
                <li>大数：${bigNumbers} 个 (${((bigNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
                <li>小数：${smallNumbers} 个 (${((smallNumbers / analyzedNumbers) * 100).toFixed(1)}%)</li>
              </ul>
            </div>

            <div class="overview-card">
              <h3>🌈 波色分布</h3>
              <ul>
                <li>红波：${colorStats.red.count} 个 (${colorStats.red.percentage}%)</li>
                <li>蓝波：${colorStats.blue.count} 个 (${colorStats.blue.percentage}%)</li>
                <li>绿波：${colorStats.green.count} 个 (${colorStats.green.percentage}%)</li>
              </ul>
            </div>
          </div>

          <div class="key-insights">
            <h3>🔍 关键洞察</h3>
            <ul>
              <li>${this.generateKeyInsight('frequency', avgFrequency)}</li>
              <li>${this.generateKeyInsight('balance', { odd: oddNumbers, even: evenNumbers })}</li>
              <li>${this.generateKeyInsight('color', colorStats)}</li>
              <li>${this.generateKeyInsight('hot', { hot: hotNumbers, cold: coldNumbers })}</li>
            </ul>
          </div>
        </div>
      `
    }
  }

  /**
   * 生成频率分析章节
   */
  generateFrequencySection() {
    const sortedByFreq = [...this.data].sort((a, b) => b.count - a.count)
    const topNumbers = sortedByFreq.slice(0, 10)
    const bottomNumbers = sortedByFreq.slice(-10).reverse()

    const frequencyRanges = this.calculateFrequencyRanges()

    return {
      title: '🔢 频率分析',
      content: `
        <div class="frequency-section">
          <h2>🔢 频率分析</h2>

          <div class="frequency-analysis">
            <div class="top-numbers">
              <h3>🏆 出现频率最高的号码 (TOP 10)</h3>
              <table class="analysis-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>号码</th>
                    <th>出现次数</th>
                    <th>生肖</th>
                    <th>波色</th>
                    <th>热度指数</th>
                  </tr>
                </thead>
                <tbody>
                  ${topNumbers.map((item, index) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td class="number-cell">${item.number}</td>
                      <td>${item.count}</td>
                      <td>${item.zodiac}</td>
                      <td class="color-${item.color}">${item.color}</td>
                      <td>${this.getHotIndex(item.number)}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="bottom-numbers">
              <h3>📉 出现频率最低的号码 (BOTTOM 10)</h3>
              <table class="analysis-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>号码</th>
                    <th>出现次数</th>
                    <th>生肖</th>
                    <th>波色</th>
                    <th>当前遗漏</th>
                  </tr>
                </thead>
                <tbody>
                  ${bottomNumbers.map((item, index) => `
                    <tr>
                      <td>${index + 1}</td>
                      <td class="number-cell">${item.number}</td>
                      <td>${item.count}</td>
                      <td>${item.zodiac}</td>
                      <td class="color-${item.color}">${item.color}</td>
                      <td>${item.missingCount}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>

            <div class="frequency-distribution">
              <h3>📊 频率分布统计</h3>
              <table class="analysis-table">
                <thead>
                  <tr>
                    <th>出现次数范围</th>
                    <th>号码数量</th>
                    <th>占比</th>
                    <th>代表号码</th>
                  </tr>
                </thead>
                <tbody>
                  ${frequencyRanges.map(range => `
                    <tr>
                      <td>${range.label}</td>
                      <td>${range.count}</td>
                      <td>${range.percentage}%</td>
                      <td>${range.examples.join(', ')}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      `
    }
  }

  /**
   * 生成遗漏分析章节
   */
  generateMissingSection() {
    const sortedByMissing = [...this.data].sort((a, b) => b.missingCount - a.missingCount)
    const highMissing = sortedByMissing.filter(item => item.missingCount > 20)
    const lowMissing = sortedByMissing.filter(item => item.missingCount <= 5)

    const missingRanges = this.calculateMissingRanges()

    return {
      title: '⏰ 遗漏分析',
      content: `
        <div class="missing-section">
          <h2>⏰ 遗漏分析</h2>

          <div class="missing-analysis">
            <div class="high-missing">
              <h3>🔴 高遗漏号码 (遗漏 > 20期)</h3>
              ${highMissing.length > 0 ? `
                <table class="analysis-table">
                  <thead>
                    <tr>
                      <th>号码</th>
                      <th>当前遗漏</th>
                      <th>最大遗漏</th>
                      <th>出现次数</th>
                      <th>回补指数</th>
                      <th>建议</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${highMissing.slice(0, 15).map(item => `
                      <tr>
                        <td class="number-cell">${item.number}</td>
                        <td class="missing-high">${item.missingCount}</td>
                        <td>${item.maxMissing}</td>
                        <td>${item.count}</td>
                        <td>${this.calculateReboundIndex(item)}</td>
                        <td>${this.getMissingAdvice(item)}</td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              ` : '<p>当前没有高遗漏号码</p>'}
            </div>

            <div class="missing-distribution">
              <h3>📊 遗漏分布统计</h3>
              <table class="analysis-table">
                <thead>
                  <tr>
                    <th>遗漏期数范围</th>
                    <th>号码数量</th>
                    <th>占比</th>
                    <th>风险等级</th>
                  </tr>
                </thead>
                <tbody>
                  ${missingRanges.map(range => `
                    <tr>
                      <td>${range.label}</td>
                      <td>${range.count}</td>
                      <td>${range.percentage}%</td>
                      <td class="risk-${range.risk}">${range.riskLabel}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      `
    }
  }

  /**
   * 生成模式分析章节
   */
  generatePatternSection() {
    const patterns = this.analyzePatterns()

    return {
      title: '🎯 模式分析',
      content: `
        <div class="pattern-section">
          <h2>🎯 模式分析</h2>

          <div class="pattern-analysis">
            <div class="number-patterns">
              <h3>🔢 号码模式</h3>
              <ul>
                <li>质数分布：${patterns.primes.count} 个质数，占 ${patterns.primes.percentage}%</li>
                <li>连号情况：发现 ${patterns.consecutive.count} 组连号</li>
                <li>重复尾数：${patterns.tailRepeat.count} 个号码有重复尾数</li>
                <li>数字根分布：${patterns.digitalRoot.description}</li>
              </ul>
            </div>

            <div class="attribute-patterns">
              <h3>🎨 属性模式</h3>
              <ul>
                <li>单双平衡度：${patterns.oddEvenBalance.description}</li>
                <li>大小平衡度：${patterns.bigSmallBalance.description}</li>
                <li>波色均衡性：${patterns.colorBalance.description}</li>
                <li>生肖分布：${patterns.zodiacDistribution.description}</li>
              </ul>
            </div>

            <div class="trend-patterns">
              <h3>📈 趋势模式</h3>
              <ul>
                <li>热号趋势：${patterns.hotTrend.description}</li>
                <li>冷号回补：${patterns.coldRebound.description}</li>
                <li>周期性特征：${patterns.cyclical.description}</li>
                <li>异常检测：${patterns.anomaly.description}</li>
              </ul>
            </div>
          </div>
        </div>
      `
    }
  }

  /**
   * 生成预测建议章节
   */
  generatePredictionSection() {
    const recommendations = this.generateRecommendations()

    return {
      title: '🔮 预测建议',
      content: `
        <div class="prediction-section">
          <h2>🔮 预测建议</h2>

          <div class="recommendations">
            <div class="hot-recommendations">
              <h3>🔥 热门推荐</h3>
              <p>基于当前热度指数和出现频率分析：</p>
              <ul>
                ${recommendations.hot.map(rec => `<li>${rec}</li>`).join('')}
              </ul>
            </div>

            <div class="rebound-recommendations">
              <h3>🔄 回补推荐</h3>
              <p>基于遗漏分析和回补概率：</p>
              <ul>
                ${recommendations.rebound.map(rec => `<li>${rec}</li>`).join('')}
              </ul>
            </div>

            <div class="balanced-recommendations">
              <h3>⚖️ 均衡推荐</h3>
              <p>基于属性平衡和稳定性分析：</p>
              <ul>
                ${recommendations.balanced.map(rec => `<li>${rec}</li>`).join('')}
              </ul>
            </div>

            <div class="risk-warning">
              <h3>⚠️ 风险提示</h3>
              <ul>
                <li>本分析基于历史数据，不构成投资建议</li>
                <li>彩票具有随机性，请理性参与</li>
                <li>建议结合多种分析方法综合判断</li>
                <li>请根据个人风险承受能力调整策略</li>
              </ul>
            </div>
          </div>
        </div>
      `
    }
  }

  /**
   * 组装完整报告
   */
  assembleReport(sections) {
    const header = this.generateReportHeader()
    const footer = this.generateReportFooter()
    const styles = this.generateReportStyles()

    const content = sections.map(section => section.content).join('\n')

    return `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.config.title}</title>
        ${styles}
      </head>
      <body>
        ${header}
        <div class="report-content">
          ${content}
        </div>
        ${footer}
      </body>
      </html>
    `
  }

  // 辅助方法
  getHotIndex(number) {
    // 简化的热度指数计算
    const item = this.data.find(d => d.number === number)
    if (!item) return 0

    const avgCount = this.data.reduce((sum, d) => sum + d.count, 0) / this.data.length
    const hotIndex = Math.min(100, Math.max(0, (item.count / avgCount) * 50))
    return Math.round(hotIndex)
  }

  calculateColorStats() {
    const stats = { red: { count: 0 }, blue: { count: 0 }, green: { count: 0 } }

    this.data.forEach(item => {
      if (item.color === '红波') stats.red.count++
      else if (item.color === '蓝波') stats.blue.count++
      else if (item.color === '绿波') stats.green.count++
    })

    const total = this.data.length
    stats.red.percentage = ((stats.red.count / total) * 100).toFixed(1)
    stats.blue.percentage = ((stats.blue.count / total) * 100).toFixed(1)
    stats.green.percentage = ((stats.green.count / total) * 100).toFixed(1)

    return stats
  }

  generateReportHeader() {
    return `
      <div class="report-header">
        <h1>${this.config.title}</h1>
        <div class="report-meta">
          <p>分析师：${this.config.analyst}</p>
          <p>生成时间：${this.generatedAt.toLocaleString()}</p>
          <p>数据范围：${this.data.length} 个号码</p>
        </div>
      </div>
    `
  }

  generateReportFooter() {
    return `
      <div class="report-footer">
        <p>本报告由特码统计分析系统自动生成</p>
        <p>生成时间：${this.generatedAt.toLocaleString()}</p>
      </div>
    `
  }

  generateReportStyles() {
    return `
      <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .report-header { text-align: center; margin-bottom: 30px; padding: 20px; background: #fff; border-radius: 8px; }
        .report-header h1 { color: #409EFF; margin: 0; }
        .report-meta { margin-top: 15px; color: #666; }
        .report-content { background: #fff; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .overview-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .overview-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #409EFF; }
        .overview-card h3 { margin-top: 0; color: #409EFF; }
        .analysis-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .analysis-table th, .analysis-table td { border: 1px solid #ddd; padding: 12px; text-align: center; }
        .analysis-table th { background: #f5f7fa; font-weight: bold; }
        .number-cell { font-weight: bold; color: #409EFF; }
        .color-红波 { color: #ff4d4f; }
        .color-蓝波 { color: #1890ff; }
        .color-绿波 { color: #52c41a; }
        .missing-high { color: #ff4d4f; font-weight: bold; }
        .risk-low { color: #52c41a; }
        .risk-medium { color: #faad14; }
        .risk-high { color: #ff4d4f; }
        .report-footer { text-align: center; color: #666; padding: 20px; }
        h2 { color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 10px; }
        h3 { color: #606266; }
        ul { line-height: 1.8; }
        .key-insights { background: #e6f7ff; padding: 20px; border-radius: 8px; margin-top: 20px; }
      </style>
    `
  }

  // 计算频率范围分布
  calculateFrequencyRanges() {
    const ranges = [
      { label: '0次', min: 0, max: 0, count: 0, examples: [] },
      { label: '1-2次', min: 1, max: 2, count: 0, examples: [] },
      { label: '3-4次', min: 3, max: 4, count: 0, examples: [] },
      { label: '5-6次', min: 5, max: 6, count: 0, examples: [] },
      { label: '7次以上', min: 7, max: Infinity, count: 0, examples: [] }
    ]

    this.data.forEach(item => {
      const range = ranges.find(r => item.count >= r.min && item.count <= r.max)
      if (range) {
        range.count++
        if (range.examples.length < 5) {
          range.examples.push(item.number)
        }
      }
    })

    const total = this.data.length
    ranges.forEach(range => {
      range.percentage = ((range.count / total) * 100).toFixed(1)
    })

    return ranges
  }

  // 计算遗漏范围分布
  calculateMissingRanges() {
    const ranges = [
      { label: '0期', min: 0, max: 0, count: 0, risk: 'low', riskLabel: '低风险' },
      { label: '1-5期', min: 1, max: 5, count: 0, risk: 'low', riskLabel: '低风险' },
      { label: '6-10期', min: 6, max: 10, count: 0, risk: 'low', riskLabel: '低风险' },
      { label: '11-20期', min: 11, max: 20, count: 0, risk: 'medium', riskLabel: '中风险' },
      { label: '21-50期', min: 21, max: 50, count: 0, risk: 'high', riskLabel: '高风险' },
      { label: '50期以上', min: 51, max: Infinity, count: 0, risk: 'high', riskLabel: '高风险' }
    ]

    this.data.forEach(item => {
      const range = ranges.find(r => item.missingCount >= r.min && item.missingCount <= r.max)
      if (range) {
        range.count++
      }
    })

    const total = this.data.length
    ranges.forEach(range => {
      range.percentage = ((range.count / total) * 100).toFixed(1)
    })

    return ranges
  }

  // 分析数据模式
  analyzePatterns() {
    const primes = this.data.filter(item => this.isPrime(item.number))
    const consecutive = this.findConsecutiveNumbers()
    const tailRepeat = this.analyzeTailRepeat()
    const digitalRoot = this.analyzeDigitalRoot()

    return {
      primes: {
        count: primes.length,
        percentage: ((primes.length / this.data.length) * 100).toFixed(1)
      },
      consecutive: {
        count: consecutive.length,
        description: `发现${consecutive.length}组连号模式`
      },
      tailRepeat: {
        count: tailRepeat.count,
        description: `${tailRepeat.count}个号码存在尾数重复`
      },
      digitalRoot: {
        description: digitalRoot.description
      },
      oddEvenBalance: {
        description: this.analyzeBalance('oddEven')
      },
      bigSmallBalance: {
        description: this.analyzeBalance('bigSmall')
      },
      colorBalance: {
        description: this.analyzeColorBalance()
      },
      zodiacDistribution: {
        description: this.analyzeZodiacDistribution()
      },
      hotTrend: {
        description: this.analyzeHotTrend()
      },
      coldRebound: {
        description: this.analyzeColdRebound()
      },
      cyclical: {
        description: this.analyzeCyclical()
      },
      anomaly: {
        description: this.analyzeAnomaly()
      }
    }
  }

  // 生成推荐建议
  generateRecommendations() {
    const hotNumbers = this.data.filter(item => this.getHotIndex(item.number) >= 60)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    const reboundNumbers = this.data.filter(item => item.missingCount >= 21 && item.missingCount <= 50)
      .sort((a, b) => b.missingCount - a.missingCount)
      .slice(0, 5)

    const balancedNumbers = this.data.filter(item => item.count >= 3 && item.count <= 4)
      .sort((a, b) => a.missingCount - b.missingCount)
      .slice(0, 5)

    return {
      hot: [
        `推荐关注热门号码：${hotNumbers.map(n => n.number).join(', ')}`,
        `这些号码近期出现频率较高，热度指数均超过60`,
        `建议采用追热策略，但注意控制风险`
      ],
      rebound: [
        `推荐关注回补号码：${reboundNumbers.map(n => n.number).join(', ')}`,
        `这些号码遗漏期数适中，具有回补潜力`,
        `建议结合历史回补周期进行判断`
      ],
      balanced: [
        `推荐关注稳定号码：${balancedNumbers.map(n => n.number).join(', ')}`,
        `这些号码出现频率稳定，风险相对较低`,
        `适合稳健型投注策略`
      ]
    }
  }

  // 生成关键洞察
  generateKeyInsight(type, data) {
    switch (type) {
      case 'frequency':
        return data > 3 ? '整体出现频率偏高，市场活跃' : '整体出现频率偏低，市场相对冷静'
      case 'balance':
        const diff = Math.abs(data.odd - data.even)
        return diff <= 2 ? '单双分布相对均衡' : `单双分布不均衡，${data.odd > data.even ? '单数' : '双数'}占优`
      case 'color':
        const maxColor = Object.keys(data).reduce((a, b) => data[a].count > data[b].count ? a : b)
        return `波色分布中${maxColor}相对活跃`
      case 'hot':
        return data.hot > data.cold ? '热号数量多于冷号，市场偏热' : '冷号数量多于热号，市场偏冷'
      default:
        return '数据分析中...'
    }
  }

  // 计算回补指数
  calculateReboundIndex(item) {
    const maxMissing = Math.max(...this.data.map(d => d.missingCount))
    const avgMissing = this.data.reduce((sum, d) => sum + d.missingCount, 0) / this.data.length

    let index = 0
    if (item.missingCount > avgMissing) {
      index = Math.min(100, (item.missingCount / maxMissing) * 100)
    }

    return Math.round(index)
  }

  // 获取遗漏建议
  getMissingAdvice(item) {
    if (item.missingCount > 50) return '高度关注'
    if (item.missingCount > 20) return '关注回补'
    if (item.missingCount > 10) return '适度关注'
    return '正常范围'
  }

  // 辅助分析方法
  isPrime(num) {
    if (num < 2) return false
    if (num === 2) return true
    if (num % 2 === 0) return false
    for (let i = 3; i <= Math.sqrt(num); i += 2) {
      if (num % i === 0) return false
    }
    return true
  }

  findConsecutiveNumbers() {
    const sorted = this.data.map(d => d.number).sort((a, b) => a - b)
    const consecutive = []
    let current = []

    for (let i = 0; i < sorted.length; i++) {
      if (i === 0 || sorted[i] === sorted[i-1] + 1) {
        current.push(sorted[i])
      } else {
        if (current.length >= 2) consecutive.push([...current])
        current = [sorted[i]]
      }
    }

    if (current.length >= 2) consecutive.push(current)
    return consecutive
  }

  analyzeTailRepeat() {
    const tails = {}
    this.data.forEach(item => {
      const tail = item.number % 10
      tails[tail] = (tails[tail] || 0) + 1
    })

    const repeatCount = Object.values(tails).filter(count => count > 1).length
    return { count: repeatCount }
  }

  analyzeDigitalRoot() {
    const roots = {}
    this.data.forEach(item => {
      let root = item.number
      while (root >= 10) {
        root = Math.floor(root / 10) + (root % 10)
      }
      roots[root] = (roots[root] || 0) + 1
    })

    const maxRoot = Object.keys(roots).reduce((a, b) => roots[a] > roots[b] ? a : b)
    return { description: `数字根${maxRoot}出现最频繁` }
  }

  analyzeBalance(type) {
    if (type === 'oddEven') {
      const odd = this.data.filter(item => item.number % 2 === 1).length
      const even = this.data.length - odd
      const diff = Math.abs(odd - even)
      return diff <= 2 ? '单双分布均衡' : `单双分布不均，差值${diff}`
    }
    return '分析中...'
  }

  analyzeColorBalance() {
    const colors = this.calculateColorStats()
    const values = Object.values(colors).map(c => c.count)
    const max = Math.max(...values)
    const min = Math.min(...values)
    return max - min <= 2 ? '波色分布相对均衡' : '波色分布不均衡'
  }

  analyzeZodiacDistribution() {
    const zodiacs = {}
    this.data.forEach(item => {
      zodiacs[item.zodiac] = (zodiacs[item.zodiac] || 0) + 1
    })
    const maxZodiac = Object.keys(zodiacs).reduce((a, b) => zodiacs[a] > zodiacs[b] ? a : b)
    return `生肖${maxZodiac}出现最频繁`
  }

  analyzeHotTrend() {
    const hotCount = this.data.filter(item => this.getHotIndex(item.number) >= 60).length
    return hotCount > 10 ? '热号趋势明显' : '热号趋势不明显'
  }

  analyzeColdRebound() {
    const coldCount = this.data.filter(item => this.getHotIndex(item.number) < 40).length
    return coldCount > 10 ? '存在较多冷号，回补机会增加' : '冷号数量适中'
  }

  analyzeCyclical() {
    return '需要更多历史数据进行周期性分析'
  }

  analyzeAnomaly() {
    const extremeHot = this.data.filter(item => item.count > 10).length
    const extremeCold = this.data.filter(item => item.count === 0).length

    if (extremeHot > 0 || extremeCold > 5) {
      return '检测到异常数据，建议进一步核实'
    }
    return '未检测到明显异常'
  }

  calculateZodiacStats() {
    const stats = {}
    this.data.forEach(item => {
      stats[item.zodiac] = (stats[item.zodiac] || 0) + 1
    })
    return stats
  }

  calculateElementStats() {
    const stats = {}
    this.data.forEach(item => {
      stats[item.element] = (stats[item.element] || 0) + 1
    })
    return stats
  }
}
