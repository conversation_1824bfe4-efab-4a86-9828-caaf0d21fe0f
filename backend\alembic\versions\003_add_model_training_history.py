"""添加模型训练历史表

Revision ID: 003
Revises: 002
Create Date: 2024-03-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # 创建模型训练历史表
    op.create_table(
        'model_training_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('training_time', sa.DateTime(), nullable=False),
        sa.Column('model_name', sa.String(length=50), nullable=False),
        sa.Column('parameters', postgresql.JSON(
            astext_type=sa.Text()), nullable=True),
        sa.Column('metrics', postgresql.JSON(
            astext_type=sa.Text()), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('data_range', sa.String(length=100), nullable=True),
        sa.Column('data_count', sa.Integer(), nullable=True),
        sa.Column('period_range', sa.String(length=100), nullable=True),
        sa.Column('custom_range', sa.String(length=100), nullable=True),
        sa.Column('recent_count', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )

    # 创建索引
    op.create_index(
        'ix_model_training_history_id',
        'model_training_history',
        ['id']
    )
    op.create_index(
        'ix_model_training_history_training_time',
        'model_training_history',
        ['training_time']
    )
    op.create_index(
        'ix_model_training_history_model_name',
        'model_training_history',
        ['model_name']
    )
    op.create_index(
        'ix_model_training_history_status',
        'model_training_history',
        ['status']
    )


def downgrade():
    # 删除索引
    op.drop_index('ix_model_training_history_status')
    op.drop_index('ix_model_training_history_model_name')
    op.drop_index('ix_model_training_history_training_time')
    op.drop_index('ix_model_training_history_id')

    # 删除表
    op.drop_table('model_training_history')
