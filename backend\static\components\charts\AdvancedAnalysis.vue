<template>
  <div class="advanced-analysis">
    <el-tabs v-model="activeTab">
      <!-- 相关性分析 -->
      <el-tab-pane label="相关性分析" name="correlation">
        <div class="chart-wrapper">
          <div ref="correlationChart" style="height: 500px"></div>
        </div>
      </el-tab-pane>
      
      <!-- 周期性分析 -->
      <el-tab-pane label="周期性分析" name="periodicity">
        <div class="chart-wrapper">
          <div ref="periodicityChart" style="height: 500px"></div>
        </div>
      </el-tab-pane>
      
      <!-- 预测准确率分析 -->
      <el-tab-pane label="准确率分析" name="accuracy">
        <div class="chart-wrapper">
          <div ref="accuracyChart" style="height: 500px"></div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  historicalData: {
    type: Array,
    required: true
  },
  predictionData: {
    type: Array,
    required: true
  }
})

const activeTab = ref('correlation')
const charts = ref({})

// 初始化相关性分析图表
const initCorrelationChart = () => {
  const chart = echarts.init(charts.value.correlationChart)
  const correlationData = calculateCorrelation(props.historicalData)
  
  const option = {
    title: {
      text: '号码相关性热力图'
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: 49}, (_, i) => i + 1)
    },
    yAxis: {
      type: 'category',
      data: Array.from({length: 49}, (_, i) => i + 1)
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%'
    },
    series: [{
      type: 'heatmap',
      data: correlationData,
      label: {
        show: false
      }
    }]
  }
  
  chart.setOption(option)
}

// 初始化周期性分析图表
const initPeriodicityChart = () => {
  const chart = echarts.init(charts.value.periodicityChart)
  const periodicityData = analyzePeriodicityPatterns(props.historicalData)
  
  const option = {
    title: {
      text: '号码周期性分析'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: periodicityData.periods
    },
    yAxis: {
      type: 'value',
      name: '出现频率'
    },
    series: periodicityData.series
  }
  
  chart.setOption(option)
}

// 初始化准确率分析图表
const initAccuracyChart = () => {
  const chart = echarts.init(charts.value.accuracyChart)
  const accuracyData = calculatePredictionAccuracy(
    props.historicalData,
    props.predictionData
  )
  
  const option = {
    title: {
      text: '预测准确率趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['整体准确率', '核心号码准确率', '属性准确率']
    },
    xAxis: {
      type: 'category',
      data: accuracyData.periods
    },
    yAxis: {
      type: 'value',
      name: '准确率',
      min: 0,
      max: 1,
      axisLabel: {
        formatter: '{value * 100}%'
      }
    },
    series: [
      {
        name: '整体准确率',
        type: 'line',
        data: accuracyData.overall
      },
      {
        name: '核心号码准确率',
        type: 'line',
        data: accuracyData.core
      },
      {
        name: '属性准确率',
        type: 'line',
        data: accuracyData.attributes
      }
    ]
  }
  
  chart.setOption(option)
}

// 监听数据变化
watch(() => props.historicalData, () => {
  initCharts()
}, { deep: true })

watch(() => props.predictionData, () => {
  initCharts()
}, { deep: true })

// 初始化所有图表
const initCharts = () => {
  initCorrelationChart()
  initPeriodicityChart()
  initAccuracyChart()
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', () => {
    Object.values(charts.value).forEach(chart => {
      chart?.resize()
    })
  })
})
</script>

<style scoped>
.advanced-analysis {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.chart-wrapper {
  margin-top: 20px;
}
</style> 