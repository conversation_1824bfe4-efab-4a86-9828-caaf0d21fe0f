from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 确保data目录存在
DATA_DIR = os.path.join(os.path.dirname(
    os.path.dirname(os.path.dirname(__file__))), "data")
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)
    logger.info(f"Created data directory at {DATA_DIR}")

# 更新数据库URL到正确的位置
SQLALCHEMY_DATABASE_URL = f"sqlite:///{DATA_DIR}/lottery.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session():
    """获取数据库会话生成器，用于定时任务"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_test_data():
    """初始化测试数据"""
    from ..models import Draw

    # 创建数据库表
    Base.metadata.create_all(bind=engine)

    # 初始化数据库会话
    db = SessionLocal()
    try:
        # 这里可以添加测试数据
        pass

    except Exception as e:
        logger.error(f"Error initializing test data: {str(e)}")
        db.rollback()
    finally:
        db.close()
