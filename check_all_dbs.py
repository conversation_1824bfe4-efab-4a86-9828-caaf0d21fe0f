import sqlite3
import os

# 检查所有可能的数据库文件
db_files = [
    'backend/marksix.db',
    'backend/data/lottery.db', 
    'backend/backend/marksix.db',
    'backend/instance/app.db',
    'backend/data/prediction.db'
]

for db_file in db_files:
    if os.path.exists(db_file):
        print(f'\n=== 检查数据库: {db_file} ===')
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 检查表
            cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            print(f'表: {table_names}')
            
            # 如果有draws表，检查数据
            if 'draws' in table_names:
                cursor.execute('SELECT COUNT(*) FROM draws')
                count = cursor.fetchone()[0]
                print(f'draws表记录数: {count}')
                
                if count > 0:
                    # 检查2025年数据
                    cursor.execute('SELECT COUNT(*) FROM draws WHERE expect LIKE "2025%"')
                    count_2025 = cursor.fetchone()[0]
                    print(f'2025年记录数: {count_2025}')
                    
                    # 显示最新几条记录
                    cursor.execute('SELECT expect, draw_time FROM draws ORDER BY draw_time DESC LIMIT 5')
                    recent = cursor.fetchall()
                    print('最新5条记录:')
                    for record in recent:
                        print(f'  期号: {record[0]}, 时间: {record[1]}')
            
            conn.close()
            
        except Exception as e:
            print(f'检查数据库 {db_file} 失败: {e}')
    else:
        print(f'数据库文件不存在: {db_file}')
