<template>
  <div class="analysis-page">
    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <h2>预测分析</h2>
          <div class="header-actions">
            <el-select v-model="timeRange" placeholder="选择时间范围" @change="handleTimeRangeChange">
              <el-option label="最近7天" value="7" />
              <el-option label="最近30天" value="30" />
              <el-option label="最近90天" value="90" />
              <el-option label="最近180天" value="180" />
              <el-option label="最近365天" value="365" />
            </el-select>
            <el-button type="primary" @click="refreshAnalysis" :loading="loading">
              刷新
            </el-button>
          </div>
        </div>
      </template>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="6">
        <el-card class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>总预测次数</span>
            </div>
          </template>
          <div class="stat-content">
            <span class="stat-value">{{ analysis?.statistics?.total_predictions || 0 }}</span>
            <span class="stat-label">次</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>命中次数</span>
            </div>
          </template>
          <div class="stat-content">
            <span class="stat-value">{{ analysis?.statistics?.hit_predictions || 0 }}</span>
            <span class="stat-label">次</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>平均准确率</span>
            </div>
          </template>
          <div class="stat-content">
            <span class="stat-value">{{ ((analysis?.statistics?.average_accuracy || 0) * 100).toFixed(2) }}</span>
            <span class="stat-label">%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <template #header>
            <div class="stat-header">
              <span>命中率</span>
            </div>
          </template>
          <div class="stat-content">
            <span class="stat-value">
              {{ analysis?.statistics?.total_predictions ? 
                ((analysis.statistics.hit_predictions / analysis.statistics.total_predictions) * 100).toFixed(2) : 
                '0.00' }}
            </span>
            <span class="stat-label">%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细分析 -->
    <el-row :gutter="20" class="mt-4">
      <!-- 模型性能分析 -->
      <el-col :span="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <h3>模型性能分析</h3>
            </div>
          </template>
          <div ref="modelPerformanceChart" style="height: 400px;"></div>
        </el-card>
      </el-col>

      <!-- 热门号码分析 -->
      <el-col :span="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <h3>热门号码分析</h3>
            </div>
          </template>
          <div ref="hotNumbersChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <!-- 生肖准确率分析 -->
      <el-col :span="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <h3>生肖准确率分析</h3>
            </div>
          </template>
          <div ref="zodiacAccuracyChart" style="height: 400px;"></div>
        </el-card>
      </el-col>

      <!-- 波色分布分析 -->
      <el-col :span="12">
        <el-card class="analysis-card">
          <template #header>
            <div class="card-header">
              <h3>波色分布分析</h3>
            </div>
          </template>
          <div ref="colorDistributionChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { getPredictionAnalysis } from '@/api/prediction'
import * as echarts from 'echarts'

// 状态变量
const loading = ref(false)
const timeRange = ref('30')
const analysis = ref(null)

// 图表实例
const charts = {
  modelPerformance: null,
  hotNumbers: null,
  zodiacAccuracy: null,
  colorDistribution: null
}

// 图表DOM引用
const modelPerformanceChart = ref(null)
const hotNumbersChart = ref(null)
const zodiacAccuracyChart = ref(null)
const colorDistributionChart = ref(null)

// 加载分析数据
async function loadAnalysis() {
  try {
    loading.value = true
    const result = await getPredictionAnalysis(parseInt(timeRange.value))
    analysis.value = result
    updateCharts()
  } catch (error) {
    ElMessage.error(error.message || '加载分析数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新分析
function refreshAnalysis() {
  loadAnalysis()
}

// 处理时间范围变化
function handleTimeRangeChange() {
  loadAnalysis()
}

// 初始化图表
function initCharts() {
  // 初始化模型性能图表
  charts.modelPerformance = echarts.init(modelPerformanceChart.value)
  charts.modelPerformance.setOption({
    title: {
      text: '模型性能对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      bottom: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['LSTM', 'RF', 'XGBoost']
    },
    yAxis: {
      type: 'value',
      name: '准确率',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: []
  })

  // 初始化热门号码图表
  charts.hotNumbers = echarts.init(hotNumbersChart.value)
  charts.hotNumbers.setOption({
    title: {
      text: '热门号码分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      name: '出现次数'
    },
    series: []
  })

  // 初始化生肖准确率图表
  charts.zodiacAccuracy = echarts.init(zodiacAccuracyChart.value)
  charts.zodiacAccuracy.setOption({
    title: {
      text: '生肖预测准确率',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: []
  })

  // 初始化波色分布图表
  charts.colorDistribution = echarts.init(colorDistributionChart.value)
  charts.colorDistribution.setOption({
    title: {
      text: '波色分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: []
  })
}

// 更新图表数据
function updateCharts() {
  if (!analysis.value) return

  // 更新模型性能图表
  const modelPerformanceData = analysis.value.model_performance.map(item => ({
    name: item.model,
    value: (item.average_confidence * 100).toFixed(2)
  }))

  charts.modelPerformance.setOption({
    series: [{
      name: '模型性能',
      type: 'bar',
      data: modelPerformanceData,
      label: {
        show: true,
        position: 'top',
        formatter: '{c}%'
      }
    }]
  })

  // 更新热门号码图表
  const hotNumbersData = analysis.value.hot_numbers.map(item => ({
    name: item.number,
    value: item.count
  }))

  charts.hotNumbers.setOption({
    xAxis: {
      data: hotNumbersData.map(item => item.name)
    },
    series: [{
      name: '出现次数',
      type: 'bar',
      data: hotNumbersData.map(item => item.value),
      label: {
        show: true,
        position: 'top'
      }
    }]
  })

  // 更新生肖准确率图表
  const zodiacData = analysis.value.zodiac_accuracy.map(item => ({
    name: item.zodiac,
    value: (item.accuracy * 100).toFixed(2)
  }))

  charts.zodiacAccuracy.setOption({
    series: [{
      name: '准确率',
      type: 'pie',
      radius: '50%',
      data: zodiacData,
      label: {
        formatter: '{b}: {c}%'
      }
    }]
  })

  // 更新波色分布图表
  const colorData = [
    { name: '红波', value: 0, itemStyle: { color: '#ff4d4f' } },
    { name: '蓝波', value: 0, itemStyle: { color: '#1890ff' } },
    { name: '绿波', value: 0, itemStyle: { color: '#52c41a' } }
  ]

  // 这里需要根据实际数据计算波色分布
  // TODO: 从分析数据中获取波色分布数据

  charts.colorDistribution.setOption({
    series: [{
      name: '波色分布',
      type: 'pie',
      radius: '50%',
      data: colorData,
      label: {
        formatter: '{b}: {c}次 ({d}%)'
      }
    }]
  })
}

// 处理窗口大小变化
function handleResize() {
  Object.values(charts).forEach(chart => {
    chart?.resize()
  })
}

// 生命周期钩子
onMounted(() => {
  initCharts()
  loadAnalysis()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  Object.values(charts).forEach(chart => {
    chart?.dispose()
  })
})
</script>

<style scoped>
.analysis-page {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stat-card {
  height: 160px;
}

.stat-header {
  text-align: center;
  font-size: 16px;
  color: #606266;
}

.stat-content {
  display: flex;
  justify-content: center;
  align-items: baseline;
  margin-top: 20px;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  margin-left: 5px;
  font-size: 14px;
  color: #909399;
}

.analysis-card {
  margin-bottom: 20px;
}

.mt-4 {
  margin-top: 20px;
}
</style> 