<template>
  <div class="advanced-prediction-page">
    <div class="page-header">
      <h1>高级预测系统</h1>
      <div class="system-status">
        <el-tag :type="modelStatus.models_loaded ? 'success' : 'warning'" effect="dark" size="large">
          <el-icon><CircleCheck v-if="modelStatus.models_loaded" /><Warning v-else /></el-icon>
          {{ modelStatus.models_loaded ? '模型已就绪' : '模型需要训练' }}
        </el-tag>
        <el-button @click="fetchModelStatus" :loading="loading" circle>
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 模型训练卡片 -->
    <el-card class="mb-4 model-card">
      <template #header>
        <div class="card-header">
          <h2>高级模型训练</h2>
          <div class="header-actions">
            <el-button type="primary" @click="showTrainingDialog" :loading="trainingLoading">
              <el-icon><Operation /></el-icon> 训练模型
            </el-button>
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="stat-panel">
            <div class="stat-title">模型状态</div>
            <div class="stat-content">
              <el-progress
                type="dashboard"
                :percentage="modelStatus.models_loaded ? 100 : 30"
                :color="modelStatus.models_loaded ? '#67C23A' : '#E6A23C'"
                :status="modelStatus.models_loaded ? 'success' : 'warning'"
              >
                <template #default>
                  <div class="progress-content">
                    <span class="progress-value">{{ modelStatus.models_loaded ? '已就绪' : '需要训练' }}</span>
                    <span class="progress-label">状态</span>
                  </div>
                </template>
              </el-progress>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="stat-panel">
            <div class="stat-title">模型权重</div>
            <div class="stat-content model-weights" v-if="modelStatus.model_weights">
              <div v-for="(weight, model) in modelStatus.model_weights" :key="model" class="weight-item">
                <span class="model-name">{{ getModelDisplayName(model) }}:</span>
                <el-progress
                  :percentage="weight * 100"
                  :color="getWeightColor(weight)"
                  :format="format => `${(weight * 100).toFixed(1)}%`"
                />
              </div>
            </div>
            <div class="stat-content" v-else>
              <el-empty description="暂无模型权重信息" :image-size="100" />
            </div>
          </div>
        </el-col>
      </el-row>

      <el-divider content-position="center">设备信息</el-divider>

      <el-descriptions :column="2" border size="large">
        <el-descriptions-item label="计算设备">
          <el-tag size="large" effect="plain" type="info">
            <el-icon><Cpu /></el-icon>
            {{ modelStatus.device || '未知' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="模型加载状态">
          <el-tag size="large" effect="plain" :type="modelStatus.models_loaded ? 'success' : 'warning'">
            <el-icon v-if="modelStatus.models_loaded"><CircleCheck /></el-icon>
            <el-icon v-else><Warning /></el-icon>
            {{ modelStatus.models_loaded ? '已加载' : '未加载' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 高级预测卡片 -->
    <el-card class="prediction-card">
      <template #header>
        <div class="card-header">
          <h2>高级预测</h2>
          <div class="header-actions">
            <el-button type="success" @click="showPredictionDialog" :loading="predicting" :disabled="!modelStatus.models_loaded">
              <el-icon><Search /></el-icon> 生成预测
            </el-button>
          </div>
        </div>
      </template>

      <template v-if="currentPrediction">
        <el-row :gutter="20" class="mb-4">
          <el-col :span="24">
            <el-alert
              title="预测结果已生成"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>
                  <strong>预测期号: {{ formatPeriodNumber(currentPrediction.prediction.expect) || getNextDrawPeriod() }}</strong><br/>
                  预测时间: {{ formatDateTime(new Date()) }}
                </p>
              </template>
            </el-alert>
          </el-col>
        </el-row>

        <el-tabs type="border-card">
          <el-tab-pane label="号码预测">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(numbers, key) in getNumberPredictions()" :key="key">
                <el-card shadow="hover" class="number-prediction-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>{{ getNumberRangeTitle(key) }}</span>
                    </div>
                  </template>
                  <div class="prediction-numbers">
                    <div
                      v-for="(number, index) in numbers"
                      :key="index"
                      class="number-circle"
                      :class="getBallColor(number)"
                      :title="getNumberColor(number)"
                    >
                      {{ number }}
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="属性预测">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(value, key) in currentPrediction.prediction.attribute_predictions" :key="key">
                <el-card shadow="hover" class="attribute-prediction-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>{{ getAttributeDisplayName(key) }}</span>
                    </div>
                  </template>
                  <div class="attribute-value">
                    <el-tag size="large" effect="dark" :type="getAttributeTagType(key, value)">
                      {{ value }}
                    </el-tag>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="生肖预测">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(zodiacs, key) in getZodiacPredictions()" :key="key">
                <el-card shadow="hover" class="zodiac-prediction-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>{{ getZodiacRangeTitle(key) }}</span>
                    </div>
                  </template>
                  <div class="zodiac-list">
                    <el-tag
                      v-for="(zodiac, index) in zodiacs"
                      :key="index"
                      class="zodiac-tag"
                      effect="dark"
                      :type="getZodiacTagType(index)"
                    >
                      {{ zodiac }}
                    </el-tag>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="策略建议">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-card shadow="hover" class="strategy-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>期号 {{ formatPeriodNumber(currentPrediction.prediction.expect) || getNextDrawPeriod() }} 的策略建议</span>
                    </div>
                  </template>
                  <div class="strategy-content">
                    <pre>{{ currentPrediction.prediction.strategy || '暂无策略建议' }}</pre>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="置信度分析">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover" class="confidence-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>综合置信度 (期号: {{ formatPeriodNumber(currentPrediction.prediction.expect) || getNextDrawPeriod() }})</span>
                    </div>
                  </template>
                  <div class="confidence-chart">
                    <el-progress
                      type="dashboard"
                      :percentage="Math.round((currentPrediction.prediction.confidence_scores?.combined || 0) * 100)"
                      :color="getConfidenceColor(currentPrediction.prediction.confidence_scores?.combined || 0)"
                    >
                      <template #default="{ percentage }">
                        <div class="dashboard-content">
                          <span class="percentage">{{ percentage }}%</span>
                          <span class="label">综合置信度</span>
                        </div>
                      </template>
                    </el-progress>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover" class="confidence-card">
                  <template #header>
                    <div class="prediction-header">
                      <span>各模型置信度</span>
                    </div>
                  </template>
                  <div class="confidence-details">
                    <div class="confidence-item" v-for="(value, key) in currentPrediction.prediction.confidence_scores" :key="key" v-if="key !== 'combined'">
                      <span class="confidence-label">{{ getModelDisplayName(key) }}:</span>
                      <el-progress
                        :percentage="value * 100"
                        :color="getConfidenceColor(value)"
                        :format="format => `${(value * 100).toFixed(1)}%`"
                      />
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </template>

      <el-empty v-else description="暂无预测结果，请点击'生成预测'按钮" />
    </el-card>

    <!-- 训练模型对话框 -->
    <el-dialog
      v-model="trainingDialogVisible"
      title="训练高级预测模型"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="trainingForm" label-width="120px">
        <el-form-item label="使用历史数据">
          <el-switch v-model="trainingForm.use_historical_data" />
        </el-form-item>
        <el-form-item label="自定义数据" v-if="!trainingForm.use_historical_data">
          <el-input
            v-model="customDataInput"
            type="textarea"
            :rows="4"
            placeholder="请输入自定义数据，多个数字用逗号分隔，例如：1,2,3,4,5"
          />
        </el-form-item>
        <el-form-item label="模型参数">
          <el-collapse>
            <el-collapse-item title="LSTM参数" name="lstm">
              <el-form-item label="隐藏层大小" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.lstm.hidden_size" :min="64" :max="512" :step="32" />
              </el-form-item>
              <el-form-item label="训练轮数" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.lstm.epochs" :min="50" :max="500" :step="10" />
              </el-form-item>
              <el-form-item label="批次大小" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.lstm.batch_size" :min="8" :max="64" :step="8" />
              </el-form-item>
              <el-form-item label="学习率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.lstm.learning_rate" :min="0.0001" :max="0.01" :step="0.0001" :precision="4" />
              </el-form-item>
              <el-form-item label="早停耐心" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.lstm.patience" :min="5" :max="50" :step="5" />
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="随机森林参数" name="rf">
              <el-form-item label="树的数量" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.rf.n_estimators" :min="50" :max="500" :step="10" />
              </el-form-item>
              <el-form-item label="最大深度" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.rf.max_depth" :min="5" :max="30" :step="1" />
              </el-form-item>
              <el-form-item label="最小分裂样本数" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.rf.min_samples_split" :min="2" :max="20" :step="1" />
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="XGBoost参数" name="xgboost">
              <el-form-item label="树的数量" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.xgboost.n_estimators" :min="50" :max="500" :step="10" />
              </el-form-item>
              <el-form-item label="最大深度" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.xgboost.max_depth" :min="3" :max="20" :step="1" />
              </el-form-item>
              <el-form-item label="学习率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.xgboost.learning_rate" :min="0.01" :max="0.5" :step="0.01" :precision="2" />
              </el-form-item>
              <el-form-item label="子采样率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.xgboost.subsample" :min="0.5" :max="1.0" :step="0.05" :precision="2" />
              </el-form-item>
              <el-form-item label="特征采样率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.xgboost.colsample_bytree" :min="0.5" :max="1.0" :step="0.05" :precision="2" />
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="GBDT参数" name="gbdt">
              <el-form-item label="树的数量" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.gbdt.n_estimators" :min="50" :max="500" :step="10" />
              </el-form-item>
              <el-form-item label="最大深度" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.gbdt.max_depth" :min="3" :max="20" :step="1" />
              </el-form-item>
              <el-form-item label="学习率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.gbdt.learning_rate" :min="0.01" :max="0.5" :step="0.01" :precision="2" />
              </el-form-item>
              <el-form-item label="子采样率" label-width="100px">
                <el-input-number v-model="trainingForm.parameters.gbdt.subsample" :min="0.5" :max="1.0" :step="0.05" :precision="2" />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="trainingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="trainModel" :loading="trainingLoading">开始训练</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 生成预测对话框 -->
    <el-dialog
      v-model="predictionDialogVisible"
      title="生成高级预测"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="predictionForm" label-width="120px">
        <el-form-item label="预测期号">
          <el-input
            v-model="predictionForm.expect"
            placeholder="例如: 2025193期 (留空则预测下一期)"
            clearable
          >
            <template #prepend>
              <el-icon><Calendar /></el-icon>
            </template>
            <template #append>
              <el-popover
                placement="top"
                :width="300"
                trigger="hover"
              >
                <template #default>
                  <div>
                    <p><strong>期号格式说明：</strong></p>
                    <p>标准格式为：<code>YYYY + WW + D + "期"</code></p>
                    <ul>
                      <li>YYYY: 年份，如 2025</li>
                      <li>WW: 周数，如 19</li>
                      <li>D: 开奖日期序号，1=周二，2=周四，3=周六</li>
                    </ul>
                    <p>例如：<code>2025193期</code> 表示 2025年第19周周六开奖</p>
                    <p>如果留空，将默认预测下一期</p>
                  </div>
                </template>
                <template #reference>
                  <el-icon><InfoFilled /></el-icon>
                </template>
              </el-popover>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="使用历史数据">
          <el-switch v-model="predictionForm.use_historical_data" />
        </el-form-item>
        <el-form-item label="自定义数据" v-if="!predictionForm.use_historical_data">
          <el-input
            v-model="customPredictionDataInput"
            type="textarea"
            :rows="4"
            placeholder="请输入自定义数据，多个数字用逗号分隔，例如：1,2,3,4,5"
          />
        </el-form-item>
        <el-form-item label="预测范围大小">
          <el-select v-model="predictionForm.range_size">
            <el-option :value="5" label="5个号码" />
            <el-option :value="10" label="10个号码" />
            <el-option :value="15" label="15个号码" />
            <el-option :value="20" label="20个号码" />
            <el-option :value="30" label="30个号码" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="predictionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="generatePrediction" :loading="predicting">生成预测</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleCheck, Warning, Refresh, Operation, Search, Cpu, Calendar, DataLine, Timer, InfoFilled } from '@element-plus/icons-vue'
import { getAdvancedPrediction, trainAdvancedModel, trainAdvancedModelLongTimeout, getModelStatus } from '@/api/advanced-prediction'

// 状态变量
const loading = ref(false)
const trainingLoading = ref(false)
const predicting = ref(false)
const modelStatus = ref({
  models_loaded: false,
  model_weights: {},
  device: ''
})
const currentPrediction = ref(null)

// 对话框状态
const trainingDialogVisible = ref(false)
const predictionDialogVisible = ref(false)

// 表单数据
const trainingForm = ref({
  use_historical_data: true,
  custom_data: [],
  parameters: {
    lstm: {
      hidden_size: 256,       // 增加隐藏层大小
      epochs: 200,           // 增加训练轮数
      batch_size: 16,        // 减小批量大小以提高学习精度
      learning_rate: 0.001,  // 降低学习率以避免过拟合
      patience: 20           // 增加早停耐心参数
    },
    rf: {
      n_estimators: 200,     // 增加树的数量
      max_depth: 15,         // 增加树的深度
      min_samples_split: 5,  // 添加最小分裂样本数
      random_state: 42
    },
    xgboost: {
      n_estimators: 200,     // 增加树的数量
      max_depth: 8,          // 适当增加深度
      learning_rate: 0.05,   // 降低学习率
      subsample: 0.8,        // 添加子采样率
      colsample_bytree: 0.8, // 添加特征采样率
      random_state: 42
    },
    gbdt: {                  // 添加GBDT模型参数
      n_estimators: 200,
      max_depth: 6,
      learning_rate: 0.05,
      subsample: 0.8,
      random_state: 42
    }
  }
})

const predictionForm = ref({
  use_historical_data: true,
  custom_data: [],
  range_size: 30,
  expect: '' // 新增期号字段，默认为空
})

// 自定义数据输入
const customDataInput = ref('')
const customPredictionDataInput = ref('')

// 生命周期钩子
onMounted(() => {
  fetchModelStatus()
})

// 方法
const fetchModelStatus = async () => {
  loading.value = true
  try {
    const response = await getModelStatus()
    if (response.status === 'success') {
      modelStatus.value = response
    } else {
      ElMessage.warning('获取模型状态失败')
    }
  } catch (error) {
    console.error('获取模型状态出错:', error)
    ElMessage.error('获取模型状态出错')
  } finally {
    loading.value = false
  }
}

const showTrainingDialog = () => {
  trainingDialogVisible.value = true
}

const showPredictionDialog = () => {
  predictionDialogVisible.value = true
}

const trainModel = async () => {
  // 处理自定义数据
  if (!trainingForm.value.use_historical_data && customDataInput.value) {
    trainingForm.value.custom_data = customDataInput.value
      .split(',')
      .map(item => parseInt(item.trim()))
      .filter(item => !isNaN(item))
  } else {
    trainingForm.value.custom_data = []
  }

  trainingLoading.value = true
  try {
    // 使用长超时版本的训练函数
    ElMessage.info('模型训练已启动，这可能需要几分钟时间，请耐心等待...')
    const response = await trainAdvancedModelLongTimeout(trainingForm.value)
    if (response.status === 'success') {
      ElMessage.success('模型训练成功')
      // 显示训练结果详情
      if (response.metrics) {
        const metrics = response.metrics
        const metricsMsg = `
          训练数据量: ${response.data_count || 'N/A'}
          综合精度: ${(metrics.combined_score * 100).toFixed(2)}%
          随机森林精度: ${(metrics.rf_score * 100).toFixed(2)}%
          XGBoost精度: ${(metrics.xgb_score * 100).toFixed(2)}%
          GBDT精度: ${(metrics.gbdt_score * 100).toFixed(2)}%
          LSTM精度: ${(metrics.lstm_score * 100).toFixed(2)}%
        `
        ElMessageBox.alert(metricsMsg, '训练结果详情', {
          dangerouslyUseHTMLString: true
        })
      }
      trainingDialogVisible.value = false
      fetchModelStatus() // 刷新模型状态
    } else {
      ElMessage.warning(response.message || '模型训练失败')
    }
  } catch (error) {
    console.error('模型训练出错:', error)
    ElMessage.error('模型训练出错: ' + (error.message || '未知错误'))
  } finally {
    trainingLoading.value = false
  }
}

const generatePrediction = async () => {
  // 处理自定义数据
  if (!predictionForm.value.use_historical_data && customPredictionDataInput.value) {
    predictionForm.value.custom_data = customPredictionDataInput.value
      .split(',')
      .map(item => parseInt(item.trim()))
      .filter(item => !isNaN(item))
  } else {
    predictionForm.value.custom_data = []
  }

  // 如果期号为空，使用下一期期号
  if (!predictionForm.value.expect) {
    predictionForm.value.expect = getNextDrawPeriod()
    ElMessage.info(`预测期号未指定，将预测下一期: ${predictionForm.value.expect}`)
  } else {
    // 确保期号格式正确，添加“期”字
    predictionForm.value.expect = formatPeriodNumber(predictionForm.value.expect)
  }

  predicting.value = true
  try {
    const response = await getAdvancedPrediction(predictionForm.value)
    if (response.status === 'success') {
      // 将期号保存到预测结果中，便于显示
      response.prediction.expect = predictionForm.value.expect
      currentPrediction.value = response
      ElMessage.success(`期号 ${predictionForm.value.expect} 的预测生成成功`)
      predictionDialogVisible.value = false

      // 重置表单期号，下次默认预测下一期
      predictionForm.value.expect = ''
    } else {
      ElMessage.warning(response.message || '预测生成失败')
    }
  } catch (error) {
    console.error('预测生成出错:', error)
    ElMessage.error('预测生成出错: ' + (error.message || '未知错误'))
  } finally {
    predicting.value = false
  }
}

// 辅助函数
const getModelDisplayName = (model) => {
  const modelNames = {
    'lstm': 'LSTM模型',
    'rf': '随机森林模型',
    'xgboost': 'XGBoost模型',
    'gbdt': 'GBDT模型'
  }
  return modelNames[model] || model
}

// 获取下一期期号
const getNextDrawPeriod = () => {
  // 获取当前日期
  const now = new Date()
  const year = now.getFullYear()

  // 计算当前是一年中的第几周
  const startOfYear = new Date(year, 0, 1)
  const millisecondsPerDay = 24 * 60 * 60 * 1000
  const dayOfYear = Math.floor((now - startOfYear) / millisecondsPerDay)
  const weekNumber = Math.ceil((dayOfYear + startOfYear.getDay() + 1) / 7)

  // 计算下一期期号
  // 假设每周二、四、六开奖，根据当前日期计算下一期
  const dayOfWeek = now.getDay() // 0是周日，1是周一，以此类推

  let drawSequence
  if (dayOfWeek === 0 || dayOfWeek === 1) { // 周日或周一，下一期是周二
    drawSequence = '1'
  } else if (dayOfWeek === 2 || dayOfWeek === 3) { // 周二或周三，下一期是周四
    drawSequence = '2'
  } else { // 周四、周五或周六，下一期是周六
    drawSequence = '3'
  }

  // 计算期号
  // 期号格式为: 年份 + 周数 + 开奖日期序号 + "期"
  // 例如: 2025193期 (其中2025是年份，19是周数，3是开奖日期序号)
  const periodNumber = `${year}${weekNumber.toString().padStart(2, '0')}${drawSequence}期`

  return periodNumber
}

// 格式化期号，确保有“期”字
const formatPeriodNumber = (periodNumber) => {
  if (!periodNumber) return ''

  // 如果已经有“期”字，直接返回
  if (periodNumber.endsWith('期')) {
    return periodNumber
  }

  // 否则添加“期”字
  return `${periodNumber}期`
}

const getWeightColor = (weight) => {
  if (weight >= 0.3) return '#67C23A' // 绿色
  if (weight >= 0.2) return '#E6A23C' // 黄色
  return '#F56C6C' // 红色
}

// 使用已存在的 formatDateTime 函数

const getConfidenceColor = (confidence) => {
  if (confidence >= 0.7) return '#67C23A' // 绿色
  if (confidence >= 0.4) return '#E6A23C' // 黄色
  return '#F56C6C' // 红色
}

const getBallColor = (number) => {
  // 根据号码返回对应的颜色类名
  const colors = ['red', 'blue', 'green']
  return colors[number % 3]
}

const getNumberColor = (number) => {
  // 根据号码返回对应的颜色名称
  const colors = ['红色', '蓝色', '绿色']
  return colors[number % 3]
}

const getAttributeDisplayName = (key) => {
  const attributeNames = {
    'special_odd_even': '单双',
    'special_big_small': '大小',
    'special_color': '颜色',
    'special_animal_type': '生肖类型',
    'special_element': '五行',
    'special_tail_big_small': '尾数大小',
    'special_sum_odd_even': '和值单双'
  }
  return attributeNames[key] || key
}

const getAttributeTagType = (key, value) => {
  // 根据属性类型和值返回对应的标签类型
  if (key === 'special_color') {
    if (value === '红') return 'danger'
    if (value === '蓝') return 'primary'
    if (value === '绿') return 'success'
  }
  if (value.includes('单')) return 'danger'
  if (value.includes('双')) return 'primary'
  if (value.includes('大')) return 'warning'
  if (value.includes('小')) return 'info'
  return 'default'
}

const getZodiacTagType = (index) => {
  // 根据索引返回不同的标签类型，使生肖标签颜色多样化
  const types = ['success', 'warning', 'danger', 'info', 'primary']
  return types[index % types.length]
}

const getNumberPredictions = () => {
  if (!currentPrediction.value) return {}

  const result = {}
  const prediction = currentPrediction.value.prediction

  if (prediction.special_numbers_5) result.special_numbers_5 = prediction.special_numbers_5
  if (prediction.special_numbers_10) result.special_numbers_10 = prediction.special_numbers_10
  if (prediction.special_numbers_15) result.special_numbers_15 = prediction.special_numbers_15
  if (prediction.special_numbers_20) result.special_numbers_20 = prediction.special_numbers_20
  if (prediction.special_numbers_30) result.special_numbers_30 = prediction.special_numbers_30

  return result
}

const getZodiacPredictions = () => {
  if (!currentPrediction.value) return {}

  const result = {}
  const prediction = currentPrediction.value.prediction

  if (prediction.zodiac_3) result.zodiac_3 = prediction.zodiac_3
  if (prediction.zodiac_5) result.zodiac_5 = prediction.zodiac_5
  if (prediction.zodiac_7) result.zodiac_7 = prediction.zodiac_7

  return result
}

const getNumberRangeTitle = (key) => {
  const titles = {
    'special_numbers_5': '前5个推荐号码',
    'special_numbers_10': '前10个推荐号码',
    'special_numbers_15': '前15个推荐号码',
    'special_numbers_20': '前20个推荐号码',
    'special_numbers_30': '前30个推荐号码'
  }
  return titles[key] || key
}

const getZodiacRangeTitle = (key) => {
  const titles = {
    'zodiac_3': '前3个推荐生肖',
    'zodiac_5': '前5个推荐生肖',
    'zodiac_7': '前7个推荐生肖'
  }
  return titles[key] || key
}

const formatDateTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
</script>

<style scoped>
.advanced-prediction-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.mb-4 {
  margin-bottom: 20px;
}

.stat-panel {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f8f9fa;
  height: 100%;
}

.stat-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #606266;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  font-size: 18px;
  font-weight: bold;
}

.progress-label {
  font-size: 14px;
  color: #909399;
}

.model-weights {
  width: 100%;
}

.weight-item {
  margin-bottom: 10px;
}

.model-name {
  display: inline-block;
  width: 120px;
  margin-right: 10px;
  text-align: right;
}

.prediction-numbers {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

.number-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.number-circle.red {
  background-color: #F56C6C;
}

.number-circle.blue {
  background-color: #409EFF;
}

.number-circle.green {
  background-color: #67C23A;
}

.number-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.attribute-value {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.zodiac-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

.zodiac-tag {
  padding: 8px 15px;
  font-size: 16px;
}

.confidence-chart {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.percentage {
  font-size: 24px;
  font-weight: bold;
}

.label {
  font-size: 14px;
  color: #909399;
}

.confidence-details {
  padding: 0 20px;
}

.confidence-item {
  margin-bottom: 15px;
}

.confidence-label {
  display: inline-block;
  width: 120px;
  margin-right: 10px;
  font-weight: bold;
}

.number-prediction-card,
.attribute-prediction-card,
.zodiac-prediction-card,
.confidence-card,
.strategy-card {
  height: 100%;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.strategy-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
}
</style>