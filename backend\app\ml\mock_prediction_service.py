from typing import Dict, List, Any
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
import json
import random
import logging
from datetime import datetime
from ..models.prediction import ModelTrainingHistory
from ..database import SessionLocal
from ..utils.game_rules import GameRules2025

# 配置logger
logger = logging.getLogger(__name__)


class MockPredictionService:
    def __init__(self):
        self.scaler = StandardScaler()
        self.sequence_length = 10
        self.zodiac_map = {
            1: "鼠", 2: "牛", 3: "虎", 4: "兔", 5: "龙", 6: "蛇",
            7: "马", 8: "羊", 9: "猴", 10: "鸡", 11: "狗", 12: "猪"
        }
        self.color_map = {
            "红波": [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
            "蓝波": [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
            "绿波": [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        }
        self.game_rules = GameRules2025()

    def _preprocess_data(self, data: List[int]) -> np.ndarray:
        """预处理数据，增强特征工程"""
        try:
            df = pd.DataFrame(data, columns=['number'])
            features = []

            # 基本特征
            features.append(df['number'])
            features.append(df['number'].rolling(5).mean())
            features.append(df['number'].rolling(5).std())

            # 高级特征
            features.append(df['number'].rolling(10).mean())
            features.append(df['number'].rolling(10).std())
            features.append(df['number'].diff())
            features.append(df['number'].pct_change())

            # 周期性特征
            features.append(np.sin(2 * np.pi * df['number'] / 49))
            features.append(np.cos(2 * np.pi * df['number'] / 49))

            # 合并特征
            X = pd.concat(features, axis=1)
            X.columns = ['number', 'rolling_mean_5', 'rolling_std_5',
                         'rolling_mean_10', 'rolling_std_10', 'diff',
                         'pct_change', 'sin_cycle', 'cos_cycle']
            X = X.fillna(method='bfill')

            return X.values
        except Exception as e:
            logger.error(f"Error in data preprocessing: {str(e)}")
            raise

    def train_models(self, historical_data: List[int], params: Dict[str, Any]) -> Dict[str, Any]:
        """模拟训练模型"""
        # 处理历史数据
        if not historical_data or len(historical_data) < 10:
            logger.warning("Historical data is insufficient for training")
            historical_data = list(range(1, 50))  # 使用模拟数据

        # 保存训练历史
        training_history = ModelTrainingHistory(
            training_time=datetime.now(),
            model_name="mock_model",
            parameters=json.dumps(params),
            metrics=json.dumps({
                "mock_score": 0.8 + random.random() * 0.1
            }),
            status="success"
        )

        with SessionLocal() as db:
            db.add(training_history)
            db.commit()

        return {
            "status": "success",
            "metrics": {
                "mock_score": 0.8 + random.random() * 0.1
            }
        }

    def predict(self, historical_data: List[int], range_size: int = 30) -> Dict[str, Any]:
        """生成模拟预测"""
        if len(historical_data) < self.sequence_length:
            logger.warning("历史数据不足，使用默认值")
            # 如果数据不足，使用模拟数据
            historical_data = list(range(1, 50)) * 2

        # 简单统计分析
        mean_value = np.mean(historical_data[-10:])
        std_value = np.std(historical_data[-10:])

        # 模拟预测 - 使用平均值和标准差
        ensemble_pred = int(round(mean_value))
        # 添加一些随机性，使用标准差
        ensemble_pred = max(
            1, min(49, ensemble_pred + int(random.normalvariate(0, max(1, std_value/3)))))

        # 生成预测范围 - 随机选择一些数字，但偏向历史平均值附近
        all_numbers = list(range(1, 50))
        weights = [1/(abs(x - ensemble_pred) + 1) for x in all_numbers]

        # 确保生成足够多的不重复数字
        predictions = []
        while len(predictions) < range_size:
            # 随机选择一个数字，权重偏向历史平均值附近
            num = random.choices(all_numbers, weights=weights, k=1)[0]
            if num not in predictions:
                predictions.append(num)

        # 按照与预测值的距离排序
        predictions = sorted(predictions, key=lambda x: abs(x - ensemble_pred))

        # 计算属性预测
        attributes = self._calculate_attributes(ensemble_pred)

        # 生成生肖预测
        zodiac_predictions = self._predict_zodiacs(predictions)

        # 计算置信度
        confidence_scores = {
            "lstm": 0.7 + random.random() * 0.2,
            "rf": 0.65 + random.random() * 0.2,
            "xgboost": 0.68 + random.random() * 0.2,
            "combined": 0.7 + random.random() * 0.2
        }

        return {
            "special_numbers_5": predictions[:5],
            "special_numbers_10": predictions[:10],
            "special_numbers_15": predictions[:15],
            "special_numbers_20": predictions[:20],
            "special_numbers_30": predictions[:30],
            "attribute_predictions": attributes,
            "zodiac_3": zodiac_predictions[:3],
            "zodiac_5": zodiac_predictions[:5],
            "zodiac_7": zodiac_predictions[:7],
            "strategy": self._generate_strategy(predictions[:5], attributes, confidence_scores),
            "confidence_scores": confidence_scores
        }

    def _calculate_attributes(self, number: int) -> Dict[str, str]:
        """计算号码属性"""
        attributes = {
            "special_odd_even": "单" if number % 2 else "双",
            "special_big_small": "大" if number > 24 else "小",
            "special_color": next(color for color, nums in self.color_map.items() if number in nums),
            "special_animal_type": "家禽" if self._get_zodiac(number) in ["鸡", "狗", "猪"] else "野兽",
            "special_element": self._get_element(number),
            "special_tail_big_small": "尾大" if number % 10 > 4 else "尾小",
            "special_sum_odd_even": "合单" if sum(int(d) for d in str(number)) % 2 else "合双"
        }
        return attributes

    def _predict_zodiacs(self, numbers: List[int]) -> List[str]:
        """预测生肖"""
        return [self._get_zodiac(num) for num in numbers[:7]]

    def _get_zodiac(self, number: int) -> str:
        """获取生肖"""
        return self.zodiac_map[((number - 1) % 12) + 1]

    def _get_element(self, number: int) -> str:
        """获取五行属性"""
        elements = ["金", "木", "水", "火", "土"]
        return elements[(number - 1) % 5]

    def _generate_strategy(self, numbers: List[int], attributes: Dict[str, str],
                           confidence: Dict[str, float]) -> str:
        """生成策略建议"""
        avg_confidence = sum(confidence.values()) / len(confidence)
        strategy = f"根据历史数据分析和模型预测，建议关注以下几个方面：\n"
        strategy += f"1. 重点关注号码：{', '.join(map(str, numbers))}\n"
        strategy += f"2. {attributes['special_odd_even']}数、{attributes['special_big_small']}数走势强劲\n"
        strategy += f"3. {attributes['special_color']}、{attributes['special_element']}属性值得关注\n"

        if avg_confidence > 0.7:
            strategy += "4. 当前预测置信度较高，可以适当参考\n"
        else:
            strategy += "4. 当前预测置信度中等，建议谨慎参考\n"

        return strategy

    def evaluate_prediction(self, prediction: Dict[str, Any], actual_result: int) -> float:
        """评估预测结果，增加更细致的评估指标"""
        try:
            # 基础准确度评估
            accuracy = 0.0
            if actual_result in prediction["special_numbers_5"]:
                accuracy = 1.0
            elif actual_result in prediction["special_numbers_10"]:
                accuracy = 0.8
            elif actual_result in prediction["special_numbers_15"]:
                accuracy = 0.6
            elif actual_result in prediction["special_numbers_20"]:
                accuracy = 0.4
            elif actual_result in prediction["special_numbers_30"]:
                accuracy = 0.2

            # 属性预测评估
            attr_predictions = prediction["attribute_predictions"]
            actual_attrs = self._calculate_attributes(actual_result)
            attr_accuracy = sum(1 for k, v in attr_predictions.items(
            ) if v == actual_attrs.get(k, None)) / len(attr_predictions)

            # 生肖预测评估
            actual_zodiac = self._get_zodiac(actual_result)
            zodiac_accuracy = 1.0 if actual_zodiac in prediction["zodiac_3"] else (
                0.6 if actual_zodiac in prediction["zodiac_5"] else (
                    0.3 if actual_zodiac in prediction["zodiac_7"] else 0.0
                ))

            # 综合评估（权重可调整）
            final_accuracy = 0.6 * accuracy + 0.2 * attr_accuracy + 0.2 * zodiac_accuracy
            return round(final_accuracy, 4)

        except Exception as e:
            logger.error(f"Error in prediction evaluation: {str(e)}")
            return 0.0
