<template>
  <div class="analysis-page">
    <el-card class="analysis-card">
      <template #header>
        <div class="card-header">
          <h2>预测分析</h2>
          <div class="header-actions">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </div>
        </div>
      </template>

      <!-- 总体统计 -->
      <div class="statistics-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card shadow="hover" class="statistic-card">
              <template #header>
                <div class="statistic-header">
                  <span>总预测次数</span>
                  <el-icon><DataLine /></el-icon>
                </div>
              </template>
              <div class="statistic-value">{{ statistics.totalPredictions }}</div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistic-card">
              <template #header>
                <div class="statistic-header">
                  <span>平均准确率</span>
                  <el-icon><TrendCharts /></el-icon>
                </div>
              </template>
              <div class="statistic-value">{{ (statistics.averageAccuracy * 100).toFixed(2) }}%</div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistic-card">
              <template #header>
                <div class="statistic-header">
                  <span>最高准确率</span>
                  <el-icon><TopRight /></el-icon>
                </div>
              </template>
              <div class="statistic-value">{{ (statistics.highestAccuracy * 100).toFixed(2) }}%</div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="statistic-card">
              <template #header>
                <div class="statistic-header">
                  <span>命中率趋势</span>
                  <el-icon><Histogram /></el-icon>
                </div>
              </template>
              <div class="statistic-value" :class="statistics.trend >= 0 ? 'positive' : 'negative'">
                {{ statistics.trend >= 0 ? '+' : '' }}{{ (statistics.trend * 100).toFixed(2) }}%
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 模型性能对比 -->
      <div class="model-comparison">
        <h3>模型性能对比</h3>
        <el-table :data="modelPerformance" style="width: 100%">
          <el-table-column prop="model" label="模型" width="180" />
          <el-table-column prop="accuracy" label="准确率" width="180">
            <template #default="scope">
              {{ (scope.row.accuracy * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="mae" label="平均绝对误差" width="180">
            <template #default="scope">
              {{ scope.row.mae.toFixed(4) }}
            </template>
          </el-table-column>
          <el-table-column prop="rmse" label="均方根误差">
            <template #default="scope">
              {{ scope.row.rmse.toFixed(4) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 预测分布分析 -->
      <div class="prediction-distribution">
        <h3>预测分布分析</h3>
        <el-tabs type="border-card">
          <!-- 号码分布 -->
          <el-tab-pane label="号码分布">
            <div class="number-distribution">
              <div class="chart-container">
                <!-- 这里可以添加号码分布图表 -->
              </div>
              <div class="distribution-stats">
                <h4>热门号码 (Top 5)</h4>
                <el-table :data="hotNumbers" style="width: 100%">
                  <el-table-column prop="number" label="号码" width="100" />
                  <el-table-column prop="frequency" label="出现频率">
                    <template #default="scope">
                      {{ (scope.row.frequency * 100).toFixed(2) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 生肖分布 -->
          <el-tab-pane label="生肖分布">
            <div class="zodiac-distribution">
              <div class="chart-container">
                <!-- 这里可以添加生肖分布图表 -->
              </div>
              <div class="distribution-stats">
                <h4>生肖命中率</h4>
                <el-table :data="zodiacAccuracy" style="width: 100%">
                  <el-table-column prop="zodiac" label="生肖" width="100" />
                  <el-table-column prop="accuracy" label="命中率">
                    <template #default="scope">
                      {{ (scope.row.accuracy * 100).toFixed(2) }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>

          <!-- 属性分布 -->
          <el-tab-pane label="属性分布">
            <div class="attribute-distribution">
              <el-row :gutter="20">
                <el-col :span="8">
                  <h4>单双分布</h4>
                  <div class="chart-container">
                    <!-- 这里可以添加单双分布图表 -->
                  </div>
                </el-col>
                <el-col :span="8">
                  <h4>大小分布</h4>
                  <div class="chart-container">
                    <!-- 这里可以添加大小分布图表 -->
                  </div>
                </el-col>
                <el-col :span="8">
                  <h4>波色分布</h4>
                  <div class="chart-container">
                    <!-- 这里可以添加波色分布图表 -->
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 准确率趋势 -->
      <div class="accuracy-trend">
        <h3>准确率趋势</h3>
        <div class="chart-container">
          <!-- 这里可以添加准确率趋势图表 -->
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DataLine, TrendCharts, TopRight, Histogram } from '@element-plus/icons-vue'
import { getPredictionAnalysis } from '@/api/prediction'

// 状态变量
const dateRange = ref(null)
const statistics = ref({
  totalPredictions: 0,
  averageAccuracy: 0,
  highestAccuracy: 0,
  trend: 0
})
const modelPerformance = ref([])
const hotNumbers = ref([])
const zodiacAccuracy = ref([])

// 加载数据
const loadData = async () => {
  try {
    const response = await getPredictionAnalysis({
      start_date: dateRange.value?.[0],
      end_date: dateRange.value?.[1]
    })
    
    // 更新统计数据
    statistics.value = response.data.statistics
    modelPerformance.value = response.data.model_performance
    hotNumbers.value = response.data.hot_numbers
    zodiacAccuracy.value = response.data.zodiac_accuracy
    
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  }
}

// 日期变化处理
const handleDateChange = () => {
  loadData()
}

// 生命周期钩子
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.analysis-page {
  padding: 20px;
}

.analysis-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.statistics-section {
  margin-bottom: 30px;
}

.statistic-card {
  .statistic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
  
  .statistic-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-color-primary);
    text-align: center;
    margin-top: 10px;
    
    &.positive {
      color: var(--el-color-success);
    }
    
    &.negative {
      color: var(--el-color-danger);
    }
  }
}

.model-comparison,
.prediction-distribution,
.accuracy-trend {
  margin-top: 30px;
  
  h3 {
    margin-bottom: 20px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
}

.chart-container {
  height: 300px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  margin-bottom: 20px;
}

.distribution-stats {
  h4 {
    margin-bottom: 15px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
}

.attribute-distribution {
  h4 {
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .chart-container {
    height: 200px;
  }
}
</style> 