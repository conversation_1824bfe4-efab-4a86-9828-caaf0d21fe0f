from abc import ABC, abstractmethod
import numpy as np
from typing import Dict, List, Any, <PERSON>ple
import joblib
import os


class BaseModel(ABC):
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.model = None
        self.model_path = f"models/{model_name}.joblib"

    @abstractmethod
    def preprocess_data(self, data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """预处理数据"""
        pass

    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray, params: Dict[str, Any] = None) -> Dict[str, float]:
        """训练模型"""
        pass

    @abstractmethod
    def predict(self, X: np.ndarray) -> Dict[str, Any]:
        """预测结果"""
        pass

    def save_model(self):
        """保存模型"""
        os.makedirs("models", exist_ok=True)
        joblib.dump(self.model, self.model_path)

    def load_model(self):
        """加载模型"""
        if os.path.exists(self.model_path):
            self.model = joblib.load(self.model_path)
            return True
        return False

    def calculate_confidence(self, X: np.ndarray) -> float:
        """计算预测置信度"""
        pass
