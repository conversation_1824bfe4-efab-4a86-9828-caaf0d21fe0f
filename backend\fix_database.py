import sqlite3
import json
from datetime import datetime
import random
import os


def fix_database():
    """修复数据库结构问题"""
    # 尝试多个可能的数据库位置，优先使用data/lottery.db
    db_paths = [
        'data/lottery.db',
        'marksix.db',
        'backend/marksix.db',
        'instance/app.db'
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            print(f"找到数据库文件: {path}")
            break

    if not db_path:
        # 如果没有找到数据库，创建一个新的
        db_path = 'marksix.db'
        print(f"创建新数据库: {db_path}")

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # 检查draws表是否存在
        cursor.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='draws'")
        if not cursor.fetchone():
            print("创建draws表...")
            cursor.execute("""
            CREATE TABLE draws (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expect VARCHAR NOT NULL UNIQUE,
                numbers TEXT,
                draw_time DATETIME NOT NULL,
                open_code TEXT NOT NULL,
                special_number INTEGER,
                zodiac TEXT,
                color TEXT,
                odd_even TEXT,
                big_small TEXT,
                tail_big_small TEXT,
                sum_odd_even TEXT,
                animal_type TEXT,
                wuxing TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """)
            cursor.execute("CREATE INDEX ix_draws_expect ON draws (expect)")
        else:
            # 检查draws表是否有numbers列
            cursor.execute("PRAGMA table_info(draws)")
            draws_columns = [column[1] for column in cursor.fetchall()]
            print(f"draws表现有列: {draws_columns}")

            # 如果没有numbers列，添加它
            if 'numbers' not in draws_columns:
                print("添加numbers列到draws表...")
                cursor.execute("ALTER TABLE draws ADD COLUMN numbers TEXT")

            # 如果没有open_code列，添加它
            if 'open_code' not in draws_columns:
                print("添加open_code列到draws表...")
                cursor.execute("ALTER TABLE draws ADD COLUMN open_code TEXT")

        # 检查predictions表是否存在
        cursor.execute(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='predictions'")
        if not cursor.fetchone():
            print("创建predictions表...")
            cursor.execute("""
            CREATE TABLE predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expect VARCHAR NOT NULL,
                predicted_numbers TEXT,
                confidence REAL,
                model_version VARCHAR,
                prediction_time DATETIME,
                is_correct INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """)
        else:
            # 检查predictions表是否有predicted_numbers列
            cursor.execute("PRAGMA table_info(predictions)")
            pred_columns = [column[1] for column in cursor.fetchall()]
            print(f"predictions表现有列: {pred_columns}")

            if 'predicted_numbers' not in pred_columns:
                print("添加predicted_numbers列到predictions表...")
                cursor.execute(
                    "ALTER TABLE predictions ADD COLUMN predicted_numbers TEXT")

            if 'confidence' not in pred_columns:
                print("添加confidence列到predictions表...")
                cursor.execute(
                    "ALTER TABLE predictions ADD COLUMN confidence REAL")

            if 'model_version' not in pred_columns:
                print("添加model_version列到predictions表...")
                cursor.execute(
                    "ALTER TABLE predictions ADD COLUMN model_version VARCHAR")

            if 'prediction_time' not in pred_columns:
                print("添加prediction_time列到predictions表...")
                cursor.execute(
                    "ALTER TABLE predictions ADD COLUMN prediction_time DATETIME")

            if 'is_correct' not in pred_columns:
                print("添加is_correct列到predictions表...")
                cursor.execute(
                    "ALTER TABLE predictions ADD COLUMN is_correct INTEGER DEFAULT 0")

        # 更新现有数据，从special_number等字段构造numbers
        cursor.execute(
            "SELECT id, expect, special_number FROM draws WHERE (numbers IS NULL OR numbers = '') AND special_number IS NOT NULL")
        rows = cursor.fetchall()

        for row_id, expect, special_number in rows:
            try:
                # 生成示例开奖号码（实际应该从真实数据获取）
                # 这里我们创建一个包含特码的6位数组
                numbers = []

                # 生成5个随机数（1-49，排除特码）
                available_numbers = [i for i in range(
                    1, 50) if i != special_number]
                random_numbers = random.sample(available_numbers, 5)

                # 将特码插入到随机位置
                insert_pos = random.randint(0, 5)
                numbers = random_numbers[:insert_pos] + \
                    [special_number] + random_numbers[insert_pos:]
                numbers = numbers[:6]  # 确保只有6个数字

                numbers_json = json.dumps(numbers)
                open_code = ','.join(
                    [f"{n:02d}" for n in numbers]) + f"+{special_number:02d}"

                cursor.execute("UPDATE draws SET numbers = ?, open_code = ? WHERE id = ?",
                               (numbers_json, open_code, row_id))
                print(f"更新期号 {expect} 的numbers字段: {numbers_json}")

            except Exception as e:
                print(f"处理期号 {expect} 时出错: {e}")

        conn.commit()
        print("数据库修复完成！")

        # 验证修复结果
        cursor.execute(
            "SELECT expect, numbers, special_number FROM draws LIMIT 3")
        results = cursor.fetchall()
        print("\n验证结果:")
        for expect, numbers, special_number in results:
            print(f"期号: {expect}, 号码: {numbers}, 特码: {special_number}")

    except Exception as e:
        print(f"修复数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()


if __name__ == "__main__":
    fix_database()
