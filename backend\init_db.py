import logging
import os
from datetime import datetime
from app.database import engine, Base
from app.database.enhanced_init_db import init_database

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'init_main_{datetime.now().strftime("%Y%m%d")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def main():
    logger.info("开始初始化数据库...")

    try:
        # 确保日志目录存在
        log_dir = os.path.join(os.path.dirname(__file__), "logs")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            logger.info(f"Created logs directory at {log_dir}")

        # 初始化数据
        logger.info("开始导入历史数据...")
        init_database()

        logger.info("数据库初始化完成！")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
