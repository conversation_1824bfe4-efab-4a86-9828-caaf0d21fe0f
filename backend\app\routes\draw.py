from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime
from app.db.session import get_db
from app.crud import draw as crud_draw
from app.schemas import draw as draw_schemas
from app.utils.statistics import calculate_statistics

router = APIRouter()


@router.get("/latest", response_model=draw_schemas.Draw)
def get_latest_draw(db: Session = Depends(get_db)):
    draw = crud_draw.get_latest_draw(db)
    if not draw:
        raise HTTPException(status_code=404, detail="No draw found")
    return draw


@router.get("/history", response_model=draw_schemas.DrawList)
def get_history_draws(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    number: Optional[int] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None
):
    filters = {
        "start_period": start_period,
        "end_period": end_period,
        "start_date": start_date,
        "end_date": end_date,
        "number": number,
        "zodiac": zodiac,
        "color": color,
        "odd_even": odd_even,
        "big_small": big_small,
        "tail_big_small": tail_big_small,
        "sum_odd_even": sum_odd_even,
        "animal_type": animal_type,
        "wuxing": wuxing
    }

    draws = crud_draw.get_history_draws(
        db,
        skip=(page - 1) * page_size,
        limit=page_size,
        filters=filters
    )

    total = crud_draw.count_history_draws(db, filters)

    return {
        "data": draws,
        "total": total,
        "page": page,
        "page_size": page_size
    }


@router.get("/statistics", response_model=draw_schemas.Statistics)
def get_statistics(
    db: Session = Depends(get_db),
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
):
    filters = {
        "start_date": start_date,
        "end_date": end_date
    }

    draws = crud_draw.get_filtered_draws(db, filters)
    statistics = calculate_statistics(draws)

    return statistics


@router.get("/export")
def export_draws(
    db: Session = Depends(get_db),
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    number: Optional[int] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None
):
    filters = {
        "start_period": start_period,
        "end_period": end_period,
        "start_date": start_date,
        "end_date": end_date,
        "number": number,
        "zodiac": zodiac,
        "color": color,
        "odd_even": odd_even,
        "big_small": big_small,
        "tail_big_small": tail_big_small,
        "sum_odd_even": sum_odd_even,
        "animal_type": animal_type,
        "wuxing": wuxing
    }

    return crud_draw.export_draws(db, filters)
