<template>
  <div class="visualization">
    <el-tabs v-model="activeTab">
      <!-- 号码分布热力图 -->
      <el-tab-pane label="号码分布" name="distribution">
        <div ref="heatmapChart" style="height: 400px"></div>
      </el-tab-pane>
      
      <!-- 生肖走势图 -->
      <el-tab-pane label="生肖走势" name="zodiac">
        <div ref="zodiacChart" style="height: 400px"></div>
      </el-tab-pane>
      
      <!-- 预测准确率统计 -->
      <el-tab-pane label="准确率统计" name="accuracy">
        <div ref="accuracyChart" style="height: 400px"></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'DataVisualization',
  
  setup() {
    const activeTab = ref('distribution')
    const charts = ref({})
    
    const initHeatmap = (container) => {
      const chart = echarts.init(container)
      const option = {
        title: { text: '号码分布热力图' },
        tooltip: { position: 'top' },
        grid: { top: '10%' },
        xAxis: { type: 'category', data: Array.from({length: 7}, (_, i) => i + 1) },
        yAxis: { type: 'category', data: Array.from({length: 7}, (_, i) => i + 1) },
        visualMap: {
          min: 0,
          max: 100,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '0%'
        },
        series: [{
          type: 'heatmap',
          data: generateHeatmapData(),
          label: { show: true }
        }]
      }
      chart.setOption(option)
      charts.value.heatmap = chart
    }
    
    onMounted(() => {
      initHeatmap(document.querySelector('#heatmapChart'))
    })
    
    return {
      activeTab,
      charts
    }
  }
}
</script>

<style scoped>
.visualization {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}
</style> 