from datetime import datetime
import logging
import requests
from typing import List, Dict
import json
import os
import time
from .config import Base, engine, SessionLocal
from ..models.draw import Draw

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from ..utils.game_rules import GameRules2025

def get_zodiac(number: int) -> str:
    """根据号码获取生肖"""
    return GameRules2025.get_zodiac(number)

def get_color(number: int) -> str:
    """根据号码获取波色"""
    return GameRules2025.get_color(number).replace('波', '')

def fetch_historical_data(year: int, max_retries: int = 3) -> List[Dict]:
    """从API获取历史数据，支持重试机制"""
    url = f"https://api.macaumarksix.com/history/macaujc2/y/{year}"
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('result') and isinstance(data.get('data'), list):
                    logger.info(f"成功获取{year}年数据，共{len(data['data'])}条记录")
                    return data['data']
                else:
                    logger.error(f"获取{year}年数据格式错误: {data}")
            else:
                logger.error(f"获取{year}年数据失败: HTTP {response.status_code}")
            if attempt < max_retries - 1:
                logger.info(f"将在3秒后进行第{attempt + 2}次尝试...")
                time.sleep(3)
        except requests.exceptions.Timeout:
            logger.error(f"请求{year}年数据超时")
        except requests.exceptions.RequestException as e:
            logger.error(f"请求{year}年数据网络错误: {str(e)}")
        except Exception as e:
            logger.error(f"请求{year}年数据出错: {str(e)}")
        if attempt < max_retries - 1:
            continue
    return []

def process_draw_data(draw_data: Dict) -> Dict:
    """处理单条开奖数据"""
    try:
        # 验证必填字段
        required_fields = ['openCode', 'expect', 'openTime']
        for field in required_fields:
            if not draw_data.get(field):
                logger.error(f"Missing or empty required field: {field} in draw_data")
                return None

        open_code = draw_data['openCode'].strip()
        expect = draw_data['expect'].strip()
        open_time = draw_data['openTime'].strip()

        # 验证期号格式
        if not expect.isdigit():
            logger.error(f"Invalid expect format: {expect}")
            return None

        # 验证开奖时间格式
        try:
            draw_time = datetime.strptime(open_time, '%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            logger.error(f"Invalid openTime format: {open_time}, error: {str(e)}")
            return None

        # 解析开奖号码
        try:
            numbers = [int(num.strip()) for num in open_code.split(',')]
            if len(numbers) < 1:
                logger.error(f"No valid numbers found in openCode: {open_code}")
                return None
            special_number = numbers[-1]  # 最后一个号码是特码
            
            # 验证号码范围
            if not (1 <= special_number <= 49):
                logger.error(f"Special number {special_number} out of valid range (1-49)")
                return None
        except (ValueError, IndexError) as e:
            logger.error(f"Error parsing numbers from openCode '{open_code}': {str(e)}")
            return None

        # 计算生肖和波色
        zodiac = get_zodiac(special_number)
        color = get_color(special_number)
        if not zodiac or not color:
            logger.error(f"Failed to get zodiac or color for number {special_number}")
            return None

        head = special_number // 10
        tail = special_number % 10

        processed_data = {
            'expect': expect,
            'open_code': open_code,
            'draw_time': draw_time,
            'zodiac': zodiac,
            'color': color,
            'special_number': special_number,
            'number': special_number,
            'odd_even': "单" if special_number % 2 else "双",
            'big_small': "大" if special_number > 24 else "小",
            'tail_big_small': "尾大" if tail >= 5 else "尾小",
            'sum_odd_even': "合单" if (head + tail) % 2 else "合双",
            'animal_type': "家禽" if zodiac in ["牛", "马", "羊", "鸡", "狗", "猪"] else "野兽",
            'wuxing': GameRules2025.get_wuxing(special_number)
        }

        return processed_data

    except Exception as e:
        logger.error(f"Error processing draw data: {str(e)}")
        logger.error(f"Raw draw data: {json.dumps(draw_data, ensure_ascii=False)}")
        return None

def init_database():
    """初始化数据库，获取真实历史数据"""
    try:
        # 确保数据库目录存在
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"Created data directory at {data_dir}")

        # 备份现有数据库（如果存在）
        db_path = os.path.join(data_dir, "lottery.db")
        if os.path.exists(db_path):
            backup_path = os.path.join(data_dir, f"lottery_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
            import shutil
            shutil.copy2(db_path, backup_path)
            logger.info(f"Created database backup at {backup_path}")

        # 删除旧的数据库表并创建新表
        logger.info("Dropping all tables...")
        Base.metadata.drop_all(bind=engine)
        logger.info("Creating new tables...")
        Base.metadata.create_all(bind=engine)

        db = SessionLocal()
        logger.info("Initializing database with real historical data...")

        # 获取2020-2025年的数据
        total_records = 0
        years = range(2020, 2026)  # 获取2020-2025年的数据
        existing_expects = set()  # 用于跟踪已存在的期号

        for year in years:
            try:
                logger.info(f"Fetching data for year {year}...")
                historical_data = fetch_historical_data(year)
                if not historical_data:
                    logger.warning(f"No data found for year {year}")
                    continue

                year_records = []
                for draw_data in historical_data:
                    try:
                        processed_data = process_draw_data(draw_data)
                        if processed_data and processed_data['expect'] not in existing_expects:
                            draw = Draw(**processed_data)
                            year_records.append(draw)
                            existing_expects.add(processed_data['expect'])
                            total_records += 1
                            if total_records % 100 == 0:
                                logger.info(f"Processed {total_records} records...")
                    except Exception as e:
                        logger.error(f"Error processing draw {draw_data.get('expect')}: {str(e)}")
                        continue

                if year_records:
                    try:
                        db.add_all(year_records)
                        db.commit()
                        logger.info(f"Committed data for year {year}")
                    except Exception as e:
                        logger.error(f"Error committing data for year {year}: {str(e)}")
                        db.rollback()
                        # 尝试逐条插入
                        for draw in year_records:
                            try:
                                db.add(draw)
                                db.commit()
                            except Exception as inner_e:
                                logger.error(f"Error adding single record {draw.expect}: {str(inner_e)}")
                                db.rollback()
                                continue
            except Exception as year_e:
                logger.error(f"Error processing year {year}: {str(year_e)}")
                continue

        logger.info(f"Successfully initialized database with {total_records} historical records")
        db.close()

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        if 'db' in locals():
            db.close()
        raise