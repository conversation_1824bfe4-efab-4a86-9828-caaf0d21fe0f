# 数字竞猜游戏系统优化方案

## 1. 概述

本文档提供了对2025年度数字竞猜游戏系统的优化方案，重点关注数据库初始化、预测功能和统计分析这三个核心模块。通过分析现有代码，我们发现了一些可以改进的地方，以提高系统的稳定性、性能和预测准确度。

## 2. 数据库初始化优化

### 2.1 问题分析

通过代码审查，发现数据库初始化过程中存在以下问题：

1. 错误处理机制不完善，部分异常捕获后没有合理的恢复策略
2. 数据库备份机制可能导致磁盘空间占用过大
3. 批量导入数据时，单次提交记录数过多可能导致内存压力
4. 数据验证逻辑分散，缺乏统一的验证流程

### 2.2 优化方案

#### 2.2.1 改进错误处理机制

```python
# 在 enhanced_init_db.py 中改进错误处理
def init_database():
    """初始化数据库，获取真实历史数据"""
    try:
        # 确保数据目录存在
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"Created data directory at {data_dir}")
        
        # 备份现有数据库
        db_path = os.path.join(data_dir, "lottery.db")
        backup_path = DatabaseInitializer.backup_database(db_path)
        
        # 创建数据库表
        if not DatabaseInitializer.create_tables():
            logger.error("Failed to create database tables, aborting initialization")
            return False
        
        # 获取历史数据
        total_records = 0
        years = range(2020, 2026)  # 获取2020-2025年的数据
        all_data = []
        
        for year in years:
            try:
                logger.info(f"Fetching data for year {year}...")
                historical_data = DataFetcher.fetch_historical_data(year)
                if historical_data:
                    all_data.extend(historical_data)
                    logger.info(f"Fetched {len(historical_data)} records for year {year}")
            except Exception as e:
                logger.error(f"Error fetching data for year {year}: {str(e)}")
                # 继续处理其他年份，不中断整个过程
        
        # 导入数据
        if all_data:
            total_records, failed_records = DatabaseInitializer.import_data(all_data)
            success_rate = (total_records - failed_records) / total_records * 100 if total_records > 0 else 0
            logger.info(f"Database initialization completed: {total_records} records imported, {failed_records} failed, success rate: {success_rate:.2f}%")
            return True
        else:
            logger.error("No data fetched, database initialization failed")
            return False
    
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        return False
```

#### 2.2.2 优化数据库备份策略

```python
# 在 DatabaseInitializer 类中添加备份清理功能
@staticmethod
def cleanup_old_backups(backup_dir: str, max_backups: int = 5):
    """清理旧的数据库备份，只保留最近的几个"""
    try:
        backup_files = [f for f in os.listdir(backup_dir) if f.startswith("lottery_backup_") and f.endswith(".db")]
        if len(backup_files) <= max_backups:
            return
        
        # 按修改时间排序
        backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(backup_dir, x)), reverse=True)
        
        # 删除旧的备份
        for old_backup in backup_files[max_backups:]:
            try:
                os.remove(os.path.join(backup_dir, old_backup))
                logger.info(f"Removed old backup: {old_backup}")
            except Exception as e:
                logger.error(f"Failed to remove old backup {old_backup}: {str(e)}")
    
    except Exception as e:
        logger.error(f"Error cleaning up old backups: {str(e)}")
```

#### 2.2.3 优化批量导入策略

```python
# 修改 import_data 方法，减小批量提交大小
@staticmethod
def import_data(data_list: List[Dict]) -> Tuple[int, int]:
    """导入数据到数据库"""
    db = SessionLocal()
    total_records = 0
    failed_records = 0
    existing_expects = set()
    batch_size = 50  # 减小批量提交大小
    
    try:
        batch = []
        for draw_data in data_list:
            try:
                processed_data = DataProcessor.process_draw_data(draw_data)
                if processed_data and processed_data['expect'] not in existing_expects:
                    draw = Draw(**processed_data)
                    batch.append(draw)
                    existing_expects.add(processed_data['expect'])
                    total_records += 1
                    
                    # 每batch_size条记录提交一次
                    if len(batch) >= batch_size:
                        db.add_all(batch)
                        db.commit()
                        logger.info(f"Committed {total_records} records")
                        batch = []
            
            except Exception as e:
                logger.error(f"Error processing draw {draw_data.get('expect')}: {str(e)}")
                failed_records += 1
                continue
        
        # 提交剩余记录
        if batch:
            db.add_all(batch)
            db.commit()
            logger.info(f"Committed final batch, total {total_records} records")
        
        logger.info(f"Successfully imported {total_records} records, {failed_records} failed")
        
    except SQLAlchemyError as e:
        logger.error(f"Database error during import: {str(e)}")
        db.rollback()
        raise
    
    finally:
        db.close()
    
    return total_records, failed_records
```

## 3. 预测功能优化

### 3.1 问题分析

预测功能存在以下问题：

1. 模型加载失败时缺乏合适的回退策略
2. 预测过程中的异常处理不完善
3. 模型评估指标不够全面
4. 模型训练参数固定，缺乏自适应调整

### 3.2 优化方案

#### 3.2.1 增强模型加载机制

```python
# 在 enhanced_prediction_service.py 中改进模型加载
def _load_models(self):
    """加载模型，增加回退机制"""
    models_loaded = 0
    expected_models = 3  # 期望加载的模型数量
    
    try:
        # 加载随机森林模型
        rf_path = os.path.join(self.models_dir, "rf_model.joblib")
        if os.path.exists(rf_path):
            try:
                self.rf_model = joblib.load(rf_path)
                logger.info("随机森林模型已加载")
                models_loaded += 1
            except Exception as e:
                logger.error(f"加载随机森林模型失败: {str(e)}")
        
        # 加载XGBoost模型
        xgb_path = os.path.join(self.models_dir, "xgb_model.joblib")
        if os.path.exists(xgb_path):
            try:
                self.xgb_model = joblib.load(xgb_path)
                logger.info("XGBoost模型已加载")
                models_loaded += 1
            except Exception as e:
                logger.error(f"加载XGBoost模型失败: {str(e)}")
        
        # 加载LSTM模型
        lstm_path = os.path.join(self.models_dir, "lstm_best.pth")
        if os.path.exists(lstm_path):
            try:
                # 需要先创建模型实例
                input_size = 17  # 特征维度，与_preprocess_data中的特征数量一致
                self.lstm_model = LSTM(
                    input_size=input_size,
                    hidden_size=128,
                    num_layers=2,
                    dropout=0.2
                ).to(self.device)
                self.lstm_model.load_state_dict(torch.load(lstm_path, map_location=self.device))
                self.lstm_model.eval()
                logger.info("LSTM模型已加载")
                models_loaded += 1
            except Exception as e:
                logger.error(f"加载LSTM模型失败: {str(e)}")
        
        # 检查加载情况并返回
        if models_loaded == expected_models:
            logger.info("所有模型加载成功")
            return True
        elif models_loaded > 0:
            logger.warning(f"部分模型加载成功 ({models_loaded}/{expected_models})")
            return True
        else:
            logger.error("所有模型加载失败")
            return False
    
    except Exception as e:
        logger.error(f"加载模型过程中发生未预期错误: {str(e)}")
        return False
```

#### 3.2.2 改进预测过程的异常处理

```python
def predict(self, historical_data: List[int], range_size: int = 30) -> Dict[str, Any]:
    """生成预测，增强异常处理"""
    try:
        if len(historical_data) < self.sequence_length:
            logger.warning(f"历史数据不足，仅有{len(historical_data)}条记录，需要至少{self.sequence_length}条")
            # 使用数据填充策略而不是直接失败
            if len(historical_data) > 0:
                # 复制现有数据进行填充
                padding = historical_data * (self.sequence_length // len(historical_data) + 1)
                historical_data = padding[:self.sequence_length]
                logger.info(f"已使用数据填充策略扩展历史数据至{len(historical_data)}条")
            else:
                # 如果完全没有数据，使用默认值
                historical_data = list(range(1, self.sequence_length + 1))
                logger.warning("使用默认数据进行预测，结果可能不准确")
        
        # 加载模型（如果尚未加载）
        if self.lstm_model is None or self.rf_model is None or self.xgb_model is None:
            if not self._load_models():
                logger.warning("模型加载失败，将使用简化预测策略")
                return self._fallback_prediction(historical_data, range_size)
        
        # 正常预测流程...
        
    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        # 使用回退策略
        return self._fallback_prediction(historical_data, range_size)

def _fallback_prediction(self, historical_data: List[int], range_size: int = 30) -> Dict[str, Any]:
    """简化的回退预测策略，当主要预测方法失败时使用"""
    logger.info("使用回退预测策略")
    
    # 使用简单统计方法进行预测
    recent_data = historical_data[-10:] if len(historical_data) >= 10 else historical_data
    
    # 计算基本统计量
    mean_value = sum(recent_data) / len(recent_data) if recent_data else 25
    
    # 生成预测范围
    import random
    predictions = set()
    while len(predictions) < min(range_size, 49):
        # 生成围绕均值的随机数
        num = max(1, min(49, int(mean_value + random.normalvariate(0, 7))))
        predictions.add(num)
    
    predictions = sorted(list(predictions))
    
    # 计算属性和生肖
    from ..utils.game_rules import GameRules2025
    game_rules = GameRules2025()
    
    special_number = predictions[0] if predictions else 1
    attributes = game_rules.get_number_attributes(special_number)
    
    # 生成生肖预测
    zodiac_predictions = [game_rules.get_zodiac(num) for num in predictions[:7]]
    zodiac_predictions = list(set(zodiac_predictions))  # 去重
    
    return {
        "special_numbers_5": predictions[:5],
        "special_numbers_10": predictions[:10],
        "special_numbers_15": predictions[:15],
        "special_numbers_20": predictions[:20],
        "special_numbers_30": predictions[:30],
        "attribute_predictions": attributes,
        "zodiac_3": zodiac_predictions[:3],
        "zodiac_5": zodiac_predictions[:5],
        "zodiac_7": zodiac_predictions[:7],
        "strategy": "使用回退预测策略生成的结果，仅供参考",
        "confidence_scores": {
            "fallback": 0.5,
            "combined": 0.5
        }
    }
```

#### 3.2.3 增强模型评估指标

```python
def evaluate_prediction(self, prediction: Dict[str, Any], actual_result: int) -> Dict[str, Any]:
    """评估预测结果，增加更多评估指标"""
    try:
        # 基本命中评估
        hit = actual_result in prediction["special_numbers_5"]
        hit_top10 = actual_result in prediction["special_numbers_10"]
        hit_top20 = actual_result in prediction["special_numbers_20"]
        
        # 计算预测号码与实际结果的距离
        distances = [abs(num - actual_result) for num in prediction["special_numbers_5"]]
        min_distance = min(distances) if distances else 49
        avg_distance = sum(distances) / len(distances) if distances else 49
        
        # 属性预测评估
        from ..utils.game_rules import GameRules2025
        game_rules = GameRules2025()
        actual_attributes = game_rules.get_number_attributes(actual_result)
        
        attributes_correct = 0
        total_attributes = 0
        for key, value in prediction["attribute_predictions"].items():
            if key in actual_attributes and actual_attributes[key] == value:
                attributes_correct += 1
            total_attributes += 1
        
        attributes_accuracy = attributes_correct / total_attributes if total_attributes > 0 else 0
        
        # 生肖预测评估
        actual_zodiac = game_rules.get_zodiac(actual_result)
        zodiac_hit = actual_zodiac in prediction["zodiac_3"]
        
        # 综合评分
        # 权重可以根据业务需求调整
        weights = {
            "hit": 0.5,  # 命中权重最高
            "hit_top10": 0.2,
            "hit_top20": 0.1,
            "min_distance": 0.05,  # 距离越小越好
            "attributes": 0.1,
            "zodiac": 0.05
        }
        
        # 归一化最小距离 (1-49范围映射到0-1，并反转使得距离小的得分高)
        normalized_distance = max(0, 1 - (min_distance / 49))
        
        # 计算综合得分
        score = (
            (1.0 if hit else 0.0) * weights["hit"] +
            (1.0 if hit_top10 else 0.0) * weights["hit_top10"] +
            (1.0 if hit_top20 else 0.0) * weights["hit_top20"] +
            normalized_distance * weights["min_distance"] +
            attributes_accuracy * weights["attributes"] +
            (1.0 if zodiac_hit else 0.0) * weights["zodiac"]
        )
        
        return {
            "hit": hit,
            "hit_top10": hit_top10,
            "hit_top20": hit_top20,
            "min_distance": min_distance,
            "avg_distance": avg_distance,
            "attributes_accuracy": attributes_accuracy,
            "zodiac_hit": zodiac_hit,
            "score": score,
            "actual_result": actual_result
        }
    
    except Exception as e:
        logger.error(f"评估预测结果时发生错误: {str(e)}")
        return {
            "hit": False,
            "score": 0.0,
            "error": str(e),
            "actual_result": actual_result
        }
```

## 4. 统计分析优化

### 4.1 问题分析

统计分析模块存在以下问题：

1. 大量数据处理时性能不佳
2. 缺乏缓存机制，重复计算浪费资源
3. 统计指标不够全面
4. 错误处理机制不完善

### 4.2 优化方案

#### 4.2.1 增加缓存机制

```python
# 在 enhanced_statistics.py 中添加缓存机制
from functools import lru_cache
import hashlib
import pickle
import os

class EnhancedStatistics:
    """增强版统计分析类"""
    
    def __init__(self):
        self.game_rules = GameRules2025()
        self.cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "cache")
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
    
    def _get_cache_key(self, draws):
        """生成缓存键"""
        # 使用期号和开奖时间生成唯一标识
        data = [(draw.expect, draw.draw_time.isoformat() if draw.draw_time else None) for draw in draws]
        return hashlib.md5(pickle.dumps(data)).hexdigest()
    
    def _get_cached_result(self, cache_key):
        """获取缓存结果"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    result = pickle.load(f)
                logger.info(f"Using cached statistics result for {cache_key}")
                return result
            except Exception as e:
                logger.error(f"Error loading cached result: {str(e)}")
        return None
    
    def _save_to_cache(self, cache_key, result):
        """保存结果到缓存"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
            logger.info(f"Saved statistics result to cache {cache_key}")
        except Exception as e:
            logger.error(f"Error saving result to cache: {str(e)}")
    
    def calculate_statistics(self, draws: List[Draw]) -> Dict[str, Any]:
        """计算统计数据，使用缓存机制"""
        try:
            if not draws:
                return self._get_empty_statistics()
            
            # 检查缓存
            cache_key = self._get_cache_key(draws)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # 计算统计数据...
            # [原有计算逻辑]
            
            # 保存到缓存
            self._save_to_cache(cache_key, result)
            
            return result
        
        except Exception as e:
            logger.error(f"Error calculating statistics: {str(e)}")
            return self._get_empty_statistics()
```

#### 4.2.2 优化性能关键代码

```python
# 优化数据处理性能
def _calculate_zodiac_trend(self, draws: List[Draw]) -> List[Dict[str, Any]]:
    """计算生肖走势，优化性能"""
    try:
        if not draws:
            return []
        
        # 使用pandas进行高效数据处理
        import pandas as pd
        import numpy as np
        
        # 转换为DataFrame
        data = [{
            'expect': draw.expect,
            'draw_time': draw.draw_time,
            'special_number': int(draw.special_number) if draw.special_number else None,
            'zodiac': draw.zodiac
        } for draw in draws if draw.special_number and draw.zodiac]
        
        df = pd.DataFrame(data)
        if df.empty:
            return []
        
        # 确保数据类型正确
        df['special_number'] = df['special_number'].astype('Int64')
        df['expect'] = df['expect'].astype(str)
        
        # 按期号排序
        df = df.sort_values('expect')
        
        # 获取所有生肖
        all_zodiacs = list(self.game_rules.ZODIAC_MAP.values())
        
        # 计算每个生肖的出现次数
        result = []
        window_size = 10  # 滑动窗口大小
        
        # 使用滑动窗口计算趋势
        for i in range(0, len(df), window_size // 2):  # 窗口重叠一半
            window = df.iloc[max(0, i-window_size+1):i+1]
            if len(window) < 3:  # 至少需要3个数据点
                continue
            
            # 计算窗口内每个生肖的出现次数
            zodiac_counts = {zodiac: 0 for zodiac in all_zodiacs}
            for z in window['zodiac']:
                if z in zodiac_counts:
                    zodiac_counts[z] += 1
            
            # 计算窗口的中心点期号和时间
            center_idx = len(window) // 2
            center_expect = window.iloc[center_idx]['expect']
            center_time = window.iloc[center_idx]['draw_time']
            
            result.append({
                'expect': center_expect,
                'time': center_time.isoformat() if center_time else None,
                'counts': zodiac_counts
            })
        
        return result
    
    except Exception as e:
        logger.error(f"Error calculating zodiac trend: {str(e)}")
        return []
```

#### 4.2.3 增加新的统计指标

```python
# 添加新的统计指标
def _calculate_pattern_analysis(self, draws: List[Draw]) -> Dict[str, float]:
    """计算号码模式分析"""
    try:
        if not draws or len(draws) < 10:
            return {}
        
        # 按期号排序
        sorted_draws = sorted(draws, key=lambda x: x.expect)
        
        # 提取特码序列
        numbers = [int(draw.special_number) for draw in sorted_draws 
                  if draw.special_number and draw.special_number.isdigit()]
        
        if len(numbers) < 10:
            return {}
        
        # 计算连续上升/下降模式
        up_count = 0
        down_count = 0
        zigzag_count = 0
        repeat_count = 0
        
        for i in range(1, len(numbers)):
            if numbers[i] > numbers[i-1]:
                up_count += 1
            elif numbers[i] < numbers[i-1]:
                down_count += 1
            else:
                repeat_count += 1
        
        # 计算锯齿模式 (上升后下降或下降后上升)
        for i in range(2, len(numbers)):
            if (numbers[i-2] < numbers[i-1] and numbers[i-1] > numbers[i]) or \
               (numbers[i-2] > numbers[i-1] and numbers[i-1] < numbers[i]):
                zigzag_count += 1
        
        # 计算奇偶交替模式
        odd_even_alternating = 0
        for i in range(1, len(numbers)):
            if (numbers[i] % 2) != (numbers[i-1] % 2):
                odd_even_alternating += 1
        
        # 计算大小交替模式
        big_small_alternating = 0
        for i in range(1, len(numbers)):
            is_big_current = numbers[i] > 24
            is_big_prev = numbers[i-1] > 24
            if is_big_current != is_big_prev:
                big_small_alternating += 1
        
        # 归一化为概率
        total = len(numbers) - 1
        return {
            "up_trend": up_count / total if total > 0 else 0,
            "down_trend": down_count / total if total > 0 else 0,
            "repeat": repeat_count / total if total > 0 else 0,
            "zigzag": zigzag_count / (total - 1) if total > 1 else 0,
            "odd_even_alternating": odd_even_alternating / total if total > 0 else 0,
            "big_small_alternating": big_small_alternating / total if total > 0 else 0
        }
    
    except Exception as e:
        logger.error(f"Error calculating pattern analysis: {str(e)}")
        return {}
```

## 5. 实施计划

### 5.1 优先级排序

1. 数据库初始化优化 - 高优先级
   - 改进错误处理机制
   - 优化批量导入策略
   - 优化数据库备份策略

2. 预测功能优化 - 高优先级
   - 增强模型加载机制
   - 改进预测过程的异常处理
   - 增强模型评估指标

3. 统计分析优化 - 中优先级
   - 增加缓存机制
   - 优化性能关键代码
   - 增加新的统计指标

### 5.2 测试计划

1. 单元测试
   - 为每个优化的函数编写单元测试
   - 测试异常处理和边界情况

2. 集成测试
   - 测试数据库初始化完整流程