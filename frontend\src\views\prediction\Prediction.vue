<template>
  <div class="prediction-page">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :span="6" v-for="(stat, index) in dashboardStats" :key="index">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon">
            <el-icon :size="36" :color="stat.color"><component :is="stat.icon" /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="prediction-card">
      <template #header>
        <div class="card-header">
          <h2>智能预测</h2>
          <div class="header-actions">
            <el-select v-model="selectedModel" placeholder="选择预测模型" style="width: 150px; margin-right: 10px;">
              <el-option v-for="item in predictionModels" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" @click="startPrediction" :loading="isPredicting">
              开始预测
            </el-button>
            <el-button type="success" @click="showTrainingDialog = true" :disabled="isTraining">
              <el-icon><Cpu /></el-icon> 训练模型
            </el-button>
            <el-button type="info" @click="refreshPrediction" :loading="isPredicting" icon="Refresh">
              刷新预测
            </el-button>
            <el-button type="text" @click="showAdvancedOptions = !showAdvancedOptions">
              {{ showAdvancedOptions ? '隐藏高级选项' : '显示高级选项' }}
            </el-button>
          </div>
        </div>
      </template>

      <!-- 高级选项 -->
      <el-collapse-transition>
        <div v-if="showAdvancedOptions" class="advanced-options">
          <el-divider content-position="left">高级预测选项</el-divider>
          <el-form :model="advancedOptions" label-width="120px" size="small">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据范围">
                  <el-select v-model="advancedOptions.dataRange" style="width: 100%">
                    <el-option label="全部数据" value="all" />
                    <el-option label="最近 30 期" value="recent" />
                    <el-option label="自定义范围" value="custom" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="advancedOptions.dataRange === 'custom'">
                <el-form-item label="自定义期数">
                  <el-input-number v-model="advancedOptions.customDataCount" :min="10" :max="100" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预测策略">
                  <el-select v-model="advancedOptions.strategy" style="width: 100%">
                    <el-option label="平衡策略" value="balanced" />
                    <el-option label="激进策略" value="aggressive" />
                    <el-option label="保守策略" value="conservative" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="置信度阈值">
                  <el-slider v-model="advancedOptions.confidenceThreshold" :min="50" :max="95" :format-tooltip="value => `${value}%`" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="特殊过滤条件">
                  <el-checkbox-group v-model="advancedOptions.filters">
                    <el-checkbox label="odd_even">单双平衡</el-checkbox>
                    <el-checkbox label="big_small">大小平衡</el-checkbox>
                    <el-checkbox label="zodiac">生肖分布</el-checkbox>
                    <el-checkbox label="color">波色平衡</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-transition>

      <!-- 预测结果 -->
      <transition name="fade">
        <div v-if="predictionResult" class="prediction-result">
          <!-- ... existing code ... -->
        </div>
      </transition>

      <!-- 空状态 -->
      <transition name="fade">
        <el-empty
          v-if="!predictionResult"
          description='点击"开始预测"按钮生成预测结果'
        />
      </transition>
    </el-card>

    <!-- 预测准确度统计 -->
    <el-card class="accuracy-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h2>预测准确度统计</h2>
          <div class="header-actions">
            <el-button type="primary" @click="calculateAccuracyStats" :loading="isCalculatingStats" icon="DataAnalysis">
              分析数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计图表 -->
      <div v-loading="isCalculatingStats">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <h3>总体准确度</h3>
              <div class="stat-value">
                <el-progress type="dashboard" :percentage="Math.round(overallAccuracy * 100)" :width="120">
                  <template #default="{ percentage }">
                    <span class="progress-value">{{ percentage }}%</span>
                  </template>
                </el-progress>
              </div>
              <div class="stat-desc">基于 {{ evaluatedPredictions }} 条记录</div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="stat-card">
              <h3>按模型类型</h3>
              <div class="model-accuracy-chart" ref="modelAccuracyChart" style="height: 200px;"></div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="stat-card">
              <h3>趋势分析</h3>
              <div class="trend-chart" ref="trendChart" style="height: 200px;"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 历史预测记录 -->
    <el-card class="history-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h2>历史预测记录</h2>
          <div class="header-actions">
            <el-button type="primary" @click="loadPredictionHistory" :loading="isLoadingHistory" icon="Refresh">
              刷新历史
            </el-button>
          </div>
        </div>
      </template>

      <!-- 历史记录表格 -->
      <el-table
        v-loading="isLoadingHistory"
        :data="predictionHistory"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
      >
        <el-table-column prop="expect" label="期号" width="120" />
        <el-table-column prop="prediction_time" label="预测时间" width="180">
          <template #default="{row}">
            {{ formatDateTime(row.prediction_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="model" label="预测模型" width="120">
          <template #default="{row}">
            <el-tag :type="getModelTagType(row.model)" size="small">
              {{ getModelName(row.model) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="special_numbers_5" label="预测号码" min-width="180">
          <template #default="{row}">
            <div class="number-balls">
              <span
                v-for="(num, index) in row.special_numbers_5"
                :key="index"
                class="number-ball"
                :style="{'--index': index}"
              >
                {{ num }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="accuracy" label="准确度" width="100">
          <template #default="{row}">
            <el-progress
              v-if="row.accuracy !== null && row.accuracy !== undefined"
              :percentage="Math.round(row.accuracy * 100)"
              :format="format => `${format}%`"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{row}">
            <el-button type="primary" size="small" @click="viewPrediction(row)" plain>
              查看
            </el-button>
            <el-button type="success" size="small" @click="evaluatePrediction(row)" plain>
              评估
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalPredictions"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>

  <!-- 模型训练对话框 -->
  <el-dialog
    v-model="showTrainingDialog"
    title="模型训练"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="!isTraining"
  >
    <el-form :model="trainingOptions" label-width="120px">
      <el-form-item label="训练数据范围">
        <el-select v-model="trainingOptions.dataRange" style="width: 100%">
          <el-option label="最近30天" value="30" />
          <el-option label="最近60天" value="60" />
          <el-option label="最近90天" value="90" />
          <el-option label="全部历史数据" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="强制重新训练">
        <el-switch v-model="trainingOptions.forceRetrain" />
      </el-form-item>
      <el-form-item label="训练模型类型">
        <el-checkbox-group v-model="trainingOptions.modelTypes">
          <el-checkbox label="frequency">频率模型</el-checkbox>
          <el-checkbox label="pattern">模式模型</el-checkbox>
          <el-checkbox label="trend">趋势模型</el-checkbox>
          <el-checkbox label="combined">组合模型</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 训练进度 -->
    <div v-if="isTraining" class="training-progress">
      <p class="training-message">{{ trainingMessage }}</p>
      <el-progress
        :percentage="trainingProgress"
        :status="trainingStatus"
        :stroke-width="15"
        :format="format => `${format}% - ${trainingStage}`"
      />
      <div class="progress-info">
        <span>预计剩余时间: {{ remainingTime }}</span>
        <el-button size="small" type="danger" @click="cancelTraining">取消</el-button>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showTrainingDialog = false" :disabled="isTraining">取消</el-button>
        <el-button type="primary" @click="trainModel" :loading="isTraining">
          开始训练
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { predictNext, getNextExpect, safeApiCall, getPredictionHistory, trainModelAsync, getTrainingStatus } from '@/api/prediction'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { Refresh, Search, TrendCharts, Warning, SuccessFilled, CircleCheck, Calendar, Cpu } from '@element-plus/icons-vue'

// 状态变量
const isPredicting = ref(false)
const predictionResult = ref(null)

// 预测模型选项
const selectedModel = ref('frequency')
const predictionModels = [
  { value: 'frequency', label: '频率模型' },
  { value: 'pattern', label: '模式模型' },
  { value: 'trend', label: '趋势模型' },
  { value: 'combined', label: '组合模型' }
]

// 高级选项
const showAdvancedOptions = ref(false)
const advancedOptions = ref({
  dataRange: 'all',
  customDataCount: 30,
  strategy: 'balanced',
  confidenceThreshold: 70,
  filters: ['odd_even', 'big_small']
})

// 模型训练相关
const isTraining = ref(false)
const trainingProgress = ref(0)
const trainingStage = ref('准备中')
const remainingTime = ref('计算中...')
const trainingStatus = ref('')
const trainingMessage = ref('正在准备训练环境...')
const currentTrainingTaskId = ref(null)
const trainingIntervalId = ref(null)
const showTrainingDialog = ref(false)
const trainingOptions = ref({
  dataRange: '30',
  forceRetrain: false,
  modelTypes: ['frequency', 'combined']
})

// 历史记录相关
const isLoadingHistory = ref(false)
const predictionHistory = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPredictions = ref(0)

// 准确度统计相关
const isCalculatingStats = ref(false)
const overallAccuracy = ref(0)
const evaluatedPredictions = ref(0)
const modelAccuracyChart = ref(null)
const trendChart = ref(null)
const modelAccuracyData = ref({
  frequency: { total: 0, hits: 0 },
  pattern: { total: 0, hits: 0 },
  trend: { total: 0, hits: 0 },
  combined: { total: 0, hits: 0 }
})
const trendData = ref([])

/**
 * 开始预测
 */
const startPrediction = async () => {
  await generatePrediction()
}

/**
 * 刷新预测
 */
const refreshPrediction = async () => {
  ElMessage.info('正在刷新预测结果...')
  await generatePrediction()
}

/**
 * 开始模型训练
 */
const trainModel = async () => {
  try {
    isTraining.value = true
    trainingProgress.value = 0
    trainingStage.value = '准备中'
    trainingStatus.value = ''
    trainingMessage.value = '正在准备训练环境...'

    // 准备训练参数
    const params = {
      model_types: trainingOptions.value.modelTypes,
      force_retrain: trainingOptions.value.forceRetrain,
      data_range: trainingOptions.value.dataRange
    }

    // 发送训练请求
    const response = await safeApiCall(trainModelAsync, params)

    if (response.error) {
      throw new Error(response.message || '启动模型训练失败')
    }

    // 获取任务ID并开始监控进度
    currentTrainingTaskId.value = response.task_id
    trainingMessage.value = '模型训练已启动，正在处理数据...'

    // 启动进度监控
    startTrainingProgressMonitor()

    ElMessage.success('模型训练已启动')
  } catch (error) {
    console.error('启动模型训练失败:', error)
    ElMessage.error('启动模型训练失败: ' + error.message)
    isTraining.value = false
  }
}

/**
 * 监控训练进度
 */
const startTrainingProgressMonitor = () => {
  // 清除可能存在的旧定时器
  if (trainingIntervalId.value) {
    clearInterval(trainingIntervalId.value)
  }

  // 设置新的定时器，每2秒检查一次进度
  trainingIntervalId.value = setInterval(async () => {
    try {
      if (!currentTrainingTaskId.value) {
        clearInterval(trainingIntervalId.value)
        return
      }

      const response = await safeApiCall(getTrainingStatus, currentTrainingTaskId.value)

      if (response.error) {
        throw new Error(response.message || '获取训练状态失败')
      }

      // 更新训练状态
      trainingProgress.value = response.progress || 0
      trainingStage.value = response.stage || '训练中'
      remainingTime.value = response.estimated_time || '计算中...'

      // 设置进度条状态
      if (response.status === 'warning') {
        trainingStatus.value = 'warning'
      } else if (response.status === 'error') {
        trainingStatus.value = 'exception'
      } else if (response.progress >= 100) {
        trainingStatus.value = 'success'
      } else {
        trainingStatus.value = ''
      }

      // 更新训练消息
      if (response.message) {
        trainingMessage.value = response.message
      }

      // 检查是否完成
      if (response.status === 'completed') {
        clearInterval(trainingIntervalId.value)
        trainingProgress.value = 100
        trainingStage.value = '已完成'
        trainingStatus.value = 'success'
        trainingMessage.value = '模型训练已成功完成！'

        // 延迟关闭训练进度显示
        setTimeout(() => {
          isTraining.value = false
          currentTrainingTaskId.value = null
          ElMessage.success('模型训练已完成')
        }, 3000)
      } else if (response.status === 'failed') {
        clearInterval(trainingIntervalId.value)
        trainingStage.value = '训练失败'
        trainingStatus.value = 'exception'
        trainingMessage.value = `训练失败: ${response.error_message || '未知错误'}`

        // 延迟关闭训练进度显示
        setTimeout(() => {
          isTraining.value = false
          currentTrainingTaskId.value = null
          ElMessage.error('模型训练失败')
        }, 5000)
      }
    } catch (error) {
      console.error('监控训练进度失败:', error)
      trainingMessage.value = `监控错误: ${error.message}`
    }
  }, 2000)
}

/**
 * 取消训练
 */
const cancelTraining = async () => {
  try {
    if (!currentTrainingTaskId.value) return

    const confirmed = await ElMessageBox.confirm(
      '确定要取消当前的模型训练吗？已完成的部分将会丢失。',
      '取消训练',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '继续训练',
        type: 'warning'
      }
    ).catch(() => false)

    if (confirmed) {
      trainingMessage.value = '正在取消训练...';
      trainingStatus.value = 'warning';

      // 发送取消请求
      const response = await safeApiCall(
        async () => {
          // 这里应该调用实际的取消API
          return { success: true, message: '训练已取消' }
        }
      )

      if (response.error) {
        throw new Error(response.message || '取消训练失败')
      }

      // 清除定时器并重置状态
      clearInterval(trainingIntervalId.value)
      isTraining.value = false
      currentTrainingTaskId.value = null

      ElMessage.info('模型训练已取消')
    }
  } catch (error) {
    console.error('取消训练失败:', error)
    ElMessage.error('取消训练失败: ' + error.message)
  }
}

/**
 * 生成预测
 */
const generatePrediction = async () => {
  try {
    // 添加加载动画
    const loadingMessage = ElMessage({
      message: '正在生成智能预测结果...',
      duration: 0,
      showClose: false,
      type: 'info',
      iconClass: 'el-icon-loading'
    })
    isPredicting.value = true

    // 获取下一期期号
    const expectResponse = await safeApiCall(getNextExpect)
    if (expectResponse.error) {
      throw new Error(expectResponse.message || '获取期号失败')
    }

    // 使用获取到的期号，如果获取失败则生成一个随机期号作为备用
    const randomParam = Math.floor(Math.random() * 1000)
    const expect = expectResponse.expect || `20250101${randomParam}`
    console.log('使用期号:', expect) // 调试信息

    // 记录预测参数
    console.log(`预测模型: ${selectedModel.value}`) // 调试信息
    console.log('高级选项:', advancedOptions.value) // 调试信息

    // 直接调用API进行预测
    const response = await safeApiCall(predictNext, expect, selectedModel.value, advancedOptions.value)

    // 调试日志
    console.log('预测结果:', response)

    if (response.error) {
      throw new Error(response.message || '预测失败')
    }

    // 确保response.data存在
    const responseData = response.data || response

    // 格式化结果
    predictionResult.value = {
      expect: expect,
      prediction_time: new Date(),
      // 添加所使用的模型
      model: selectedModel.value,
      ...(typeof responseData === 'object' ? responseData : {}),
      // 确保置信度存在
      confidence_scores: responseData.confidence_scores || {},
      // 确保属性预测存在
      attribute_predictions: responseData.attribute_predictions || {},
      // 确保生肖预测存在
      zodiac_3: responseData.zodiac_3 || [],
      zodiac_5: responseData.zodiac_5 || [],
      zodiac_7: responseData.zodiac_7 || [],
      // 确保策略存在
      strategy: responseData.strategy || ''
    }

    // 关闭加载消息
    loadingMessage.close()

    // 更新顶部统计卡片
    updateDashboardStats()

    ElMessage.success('预测结果已生成')

    // 刷新历史记录
    await loadPredictionHistory()
  } catch (error) {
    console.error('预测失败:', error)
    ElMessage.error('预测失败: ' + error.message)
  } finally {
    isPredicting.value = false
  }
}

// 工具函数
const formatDateTime = (datetime) => {
  if (!datetime) return '未知时间'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取模型名称
const getModelName = (modelType) => {
  const modelMap = {
    'frequency': '频率模型',
    'pattern': '模式模型',
    'trend': '趋势模型',
    'combined': '组合模型'
  }
  return modelMap[modelType] || modelType || '默认模型'
}

// 获取模型标签类型
const getModelTagType = (modelType) => {
  const typeMap = {
    'frequency': 'info',
    'pattern': 'success',
    'trend': 'warning',
    'combined': 'danger'
  }
  return typeMap[modelType] || 'info'
}

// 获取准确度颜色
const getAccuracyColor = (accuracy) => {
  if (accuracy >= 0.8) return '#67C23A' // 绿色 - 优秀
  if (accuracy >= 0.6) return '#409EFF' // 蓝色 - 良好
  if (accuracy >= 0.4) return '#E6A23C' // 黄色 - 一般
  return '#F56C6C' // 红色 - 较差
}

// 获取模型置信度
const getModelConfidence = (prediction) => {
  if (!prediction || !prediction.confidence_scores) return 0.75

  // 根据模型类型返回相应的置信度
  const model = prediction.model || 'frequency'

  if (model === 'combined' && prediction.confidence_scores.combined) {
    return prediction.confidence_scores.combined
  } else if (model === 'frequency' && prediction.confidence_scores.rf) {
    return prediction.confidence_scores.rf
  } else if (model === 'pattern' && prediction.confidence_scores.xgboost) {
    return prediction.confidence_scores.xgboost
  } else if (model === 'trend' && prediction.confidence_scores.lstm) {
    return prediction.confidence_scores.lstm
  }

  // 默认返回组合置信度或第一个可用的置信度
  return prediction.confidence_scores.combined ||
         prediction.confidence_scores.rf ||
         prediction.confidence_scores.xgboost ||
         prediction.confidence_scores.lstm ||
         0.75
}

// 加载预测历史记录
const loadPredictionHistory = async () => {
  try {
    isLoadingHistory.value = true

    const response = await safeApiCall(getPredictionHistory, {
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (response.error) {
      throw new Error(response.message || '获取历史记录失败')
    }

    // 处理响应数据
    const responseData = response.data || response

    predictionHistory.value = responseData.items || []
    totalPredictions.value = responseData.total || 0

    console.log('加载历史记录成功:', predictionHistory.value)
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败: ' + error.message)
  } finally {
    isLoadingHistory.value = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  loadPredictionHistory()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadPredictionHistory()
}

// 查看预测详情
const viewPrediction = (row) => {
  // 将选中的历史记录设置为当前预测结果
  predictionResult.value = row
  ElMessage.success('已加载选中的预测记录')

  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 评估预测结果
const evaluatePrediction = (row) => {
  ElMessageBox.prompt('请输入实际开出的特码', '评估预测', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputPattern: /^[1-9][0-9]?$|^49$/,
    inputErrorMessage: '请输入 1-49 之间的数字'
  }).then(({ value }) => {
    const actualNumber = parseInt(value)

    // 计算准确度
    const isHit = row.special_numbers_5.includes(actualNumber)
    const accuracy = isHit ? 1 : row.special_numbers_10.includes(actualNumber) ? 0.5 : 0

    // 更新记录
    const index = predictionHistory.value.findIndex(item => item.expect === row.expect)
    if (index !== -1) {
      predictionHistory.value[index].actual_result = actualNumber
      predictionHistory.value[index].accuracy = accuracy
    }

    // 显示结果
    const resultMessage = isHit ? '恭喜！预测命中特码！' : '预测未命中特码'
    ElMessage[isHit ? 'success' : 'warning'](resultMessage)
  }).catch(() => {
    ElMessage.info('已取消评估')
  })
}

// 计算准确度统计
const calculateAccuracyStats = async () => {
  try {
    isCalculatingStats.value = true
    ElMessage.info('正在分析预测数据...')

    // 调用API获取统计数据
    const response = await safeApiCall(async () => {
      // 这里应该是实际的API调用
      return {
        statistics: {
          overall_accuracy: 0.72,
          evaluated_count: 156,
          model_accuracy: {
            frequency: 0.65,
            pattern: 0.72,
            trend: 0.68,
            combined: 0.75
          },
          trend_data: [
            { month: '1月', accuracy: 0.65 },
            { month: '2月', accuracy: 0.68 },
            { month: '3月', accuracy: 0.62 },
            { month: '4月', accuracy: 0.70 },
            { month: '5月', accuracy: 0.75 },
            { month: '6月', accuracy: 0.72 },
            { month: '7月', accuracy: 0.78 }
          ]
        }
      }
    })

    // 添加错误处理和空值检查
    if (response.error) {
      throw new Error(response.message || '分析数据失败')
    }

    // 确保response和response.data存在
    const responseData = response.data || response

    // 添加空值检查，确保statistics属性存在
    if (!responseData || !responseData.statistics) {
      throw new Error('返回的数据格式不正确，缺少statistics属性')
    }

    // 更新统计数据
    overallAccuracy.value = responseData.statistics.overall_accuracy || 0
    evaluatedPredictions.value = responseData.statistics.evaluated_count || 0

    // 更新模型准确率数据
    if (responseData.statistics.model_accuracy) {
      modelAccuracyData.value = responseData.statistics.model_accuracy
    }

    // 更新趋势数据
    if (responseData.statistics.trend_data) {
      trendData.value = responseData.statistics.trend_data
    }

    // 更新图表
    renderAccuracyCharts()

    ElMessage.success('数据分析完成')
  } catch (error) {
    console.error('计算准确度统计失败:', error)
    ElMessage.error('计算准确度统计失败: ' + error.message)

    // 设置默认值，避免UI显示错误
    overallAccuracy.value = 0
    evaluatedPredictions.value = 0
  } finally {
    isCalculatingStats.value = false
  }
}

// 渲染准确度图表
const renderAccuracyCharts = () => {
  // 渲染模型准确度图表
  if (modelAccuracyChart.value) {
    const chart = echarts.init(modelAccuracyChart.value)

    const modelData = Object.entries(modelAccuracyData.value).map(([model, data]) => ({
      name: getModelName(model),
      value: data.total > 0 ? Math.round((data.hits / data.total) * 100) : 0
    }))

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}%'
      },
      series: [{
        type: 'bar',
        data: modelData,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        },
        itemStyle: {
          color: function(params) {
            const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C']
            return colors[params.dataIndex % colors.length]
          }
        }
      }]
    }

    chart.setOption(option)
  }

  // 渲染趋势图表
  if (trendChart.value && trendData.value.length > 0) {
    const chart = echarts.init(trendChart.value)

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const data = params[0].data
          return `${data.date}: ${Math.round(data.accuracy * 100)}%`
        }
      },
      xAxis: {
        type: 'category',
        data: trendData.value.map(item => item.date),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 1,
        axisLabel: {
          formatter: '{value * 100}%'
        }
      },
      series: [{
        data: trendData.value.map(item => ({
          date: item.date,
          accuracy: item.accuracy,
          value: item.accuracy
        })),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3
        },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }]
    }

    chart.setOption(option)
  }
}

/**
 * 更新图表显示
 * 根据最新数据更新准确率和趋势图表
 */
const updateCharts = () => {
  // 更新模型准确率图表
  if (modelAccuracyChart.value) {
    const chart = echarts.init(modelAccuracyChart.value)

    // 准备数据
    const modelNames = ['组合模型', '趋势模型', '模式模型', '频率模型']
    const modelValues = [
      modelAccuracyData.value.combined || 0,
      modelAccuracyData.value.trend || 0,
      modelAccuracyData.value.pattern || 0,
      modelAccuracyData.value.frequency || 0
    ].map(val => Math.round(val * 100))

    // 设置颜色
    const colors = ['#F56C6C', '#E6A23C', '#67C23A', '#409EFF']

    // 配置图表
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      yAxis: {
        type: 'category',
        data: modelNames
      },
      series: [
        {
          name: '准确率',
          type: 'bar',
          data: modelValues.map((value, index) => ({
            value,
            itemStyle: { color: colors[index] }
          })),
          label: {
            show: true,
            position: 'right',
            formatter: '{c}%'
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 更新趋势图表
  if (trendChart.value) {
    const chart = echarts.init(trendChart.value)

    // 准备数据
    const months = trendData.value.map(item => item.month)
    const accuracies = trendData.value.map(item => Math.round(item.accuracy * 100))

    // 配置图表
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '准确率',
          type: 'line',
          data: accuracies,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    }

    chart.setOption(option)
  }
}

// 仪表盘统计数据
const dashboardStats = ref([
  { label: '预测总次数', value: '0', icon: 'DataAnalysis', color: '#409EFF' },
  { label: '最高准确率', value: '0%', icon: 'SuccessFilled', color: '#67C23A' },
  { label: '最近预测', value: '无', icon: 'Calendar', color: '#E6A23C' },
  { label: '模型状态', value: '已就绪', icon: 'Cpu', color: '#909399' }
])

/**
 * 初始化图表
 * 创建模型准确率和趋势分析图表
 */
const initCharts = () => {
  // 初始化模型准确率图表
  if (modelAccuracyChart.value) {
    const chart = echarts.init(modelAccuracyChart.value);
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      yAxis: {
        type: 'category',
        data: ['组合模型', '趋势模型', '模式模型', '频率模型']
      },
      series: [
        {
          name: '准确率',
          type: 'bar',
          data: [
            {value: 75, itemStyle: {color: '#F56C6C'}},
            {value: 68, itemStyle: {color: '#E6A23C'}},
            {value: 72, itemStyle: {color: '#67C23A'}},
            {value: 65, itemStyle: {color: '#409EFF'}}
          ],
          label: {
            show: true,
            position: 'right',
            formatter: '{c}%'
          }
        }
      ]
    };
    chart.setOption(option);

    // 窗口大小变化时重新渲染
    window.addEventListener('resize', () => {
      chart.resize();
    });
  }

  // 初始化趋势图表
  if (trendChart.value) {
    const chart = echarts.init(trendChart.value);
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '准确率',
          type: 'line',
          data: [65, 68, 62, 70, 75, 72, 78],
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    };
    chart.setOption(option);

    // 窗口大小变化时重新渲染
    window.addEventListener('resize', () => {
      chart.resize();
    });
  }
}

// 页面加载时获取历史记录
onMounted(() => {
  loadPredictionHistory()

  // 加载仪表盘统计数据
  loadDashboardStats()

  // 初始化图表
  initCharts()
})

// 加载仪表盘统计数据
const loadDashboardStats = async () => {
  try {
    // 这里应该调用实际的API获取统计数据
    // 模拟数据
    dashboardStats.value = [
      { label: '预测总次数', value: '128', icon: 'TrendCharts', color: '#409EFF' },
      { label: '预测准确率', value: '76%', icon: 'CircleCheck', color: '#67C23A' },
      { label: '当前期号', value: '2025001', icon: 'Calendar', color: '#E6A23C' },
      { label: '模型数量', value: '4', icon: 'Cpu', color: '#909399' }
    ]
  } catch (error) {
    console.error('加载仪表盘统计数据失败:', error)
  }
}

/**
 * 更新顶部统计卡片
 * 根据最新数据更新顶部统计信息
 */
const updateDashboardStats = () => {
  try {
    // 更新预测总次数
    const currentCount = parseInt(dashboardStats.value[0].value) || 0
    dashboardStats.value[0].value = (currentCount + 1).toString()

    // 更新预测准确率
    if (overallAccuracy.value > 0) {
      dashboardStats.value[1].value = `${Math.round(overallAccuracy.value * 100)}%`
    }

    // 更新最近预测期号
    if (predictionResult.value && predictionResult.value.expect) {
      dashboardStats.value[2].value = predictionResult.value.expect
    } else {
      dashboardStats.value[2].value = dayjs().format('YYYY-MM-DD')
    }

    // 更新模型状态
    dashboardStats.value[3].value = '已就绪'
    dashboardStats.value[3].color = '#67C23A'

    // 保存到本地存储，以便页面刷新后仍能保持
    try {
      localStorage.setItem('dashboardStats', JSON.stringify(dashboardStats.value))
    } catch (storageError) {
      console.warn('无法保存统计数据到本地存储:', storageError)
    }
  } catch (error) {
    console.error('更新仪表盘统计数据失败:', error)
  }
}
</script>

<style scoped>
.prediction-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 顶部统计卡片 */
.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: rgba(64, 158, 255, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 预测结果 */
.prediction-result {
  margin-top: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

.number-prediction {
  margin-bottom: 20px;
}

.number-prediction h3,
.attribute-prediction h3,
.zodiac-prediction h3,
.prediction-strategy h3,
.confidence-scores h3 {
  font-size: 16px;
  margin-bottom: 10px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.number-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.number-group {
  display: flex;
  align-items: center;
}

.group-label {
  width: 60px;
  font-weight: bold;
}

.number-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.number-ball {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(145deg, #f0f0f0, #e6e6e6);
  box-shadow: 3px 3px 6px #d1d1d1, -3px -3px 6px #ffffff;
  font-weight: bold;
  animation: fadeIn 0.5s ease-out;
  animation-delay: calc(var(--index) * 0.05s);
  opacity: 0;
  animation-fill-mode: forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.attribute-prediction,
.zodiac-prediction {
  margin-bottom: 20px;
}

.prediction-strategy {
  margin-bottom: 20px;
}

.strategy {
  white-space: pre-line;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border-left: 3px solid #409EFF;
}

.confidence-scores {
  margin-bottom: 20px;
}

.confidence-card {
  height: 100%;
}

.confidence-header {
  font-size: 14px;
  font-weight: bold;
}

.confidence-value {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  padding: 15px 0;
}

/* 统计卡片 */
.stat-card {
  padding: 20px;
  text-align: center;
}

.stat-value {
  margin-top: 15px;
}

.progress-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-desc {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}

/* 历史记录表格 */
.number-balls {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 训练进度 */
.training-progress {
  margin: 20px 0;
}

.training-message {
  margin-bottom: 10px;
  font-weight: bold;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .el-col {
    width: 100% !important;
    margin-bottom: 15px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .number-balls {
    flex-wrap: wrap;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .prediction-card {
    background-color: #1e1e1e;
    color: #e0e0e0;
  }

  .stat-card {
    background-color: #2d2d2d;
  }

  .number-ball {
    background: linear-gradient(145deg, #2a2a2a, #333);
    color: #e0e0e0;
  }

  .strategy {
    background-color: #2d2d2d;
  }
}

.advanced-options {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>