<template>
  <div class="history-page">
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <h2>预测历史</h2>
          <div class="header-actions">
            <el-button type="primary" @click="exportData">
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索条件 -->
      <div class="search-form">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="期号">
            <el-input v-model="searchForm.expect" placeholder="请输入期号" />
          </el-form-item>
          <el-form-item label="预测时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="准确度">
            <el-select v-model="searchForm.accuracy" placeholder="请选择准确度范围">
              <el-option label="全部" value="" />
              <el-option label="90%以上" value="90" />
              <el-option label="80%以上" value="80" />
              <el-option label="70%以上" value="70" />
              <el-option label="60%以上" value="60" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预测历史表格 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="expect" label="期号" width="120" />
        <el-table-column prop="prediction_time" label="预测时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.prediction_time) }}
          </template>
        </el-table-column>
        <el-table-column label="预测号码" width="250">
          <template #default="scope">
            <div class="number-list">
              <span
                v-for="num in scope.row.special_numbers_5"
                :key="num"
                class="number"
              >
                {{ String(num).padStart(2, '0') }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="生肖预测" width="200">
          <template #default="scope">
            {{ scope.row.zodiac_3.join(', ') }}
          </template>
        </el-table-column>
        <el-table-column label="属性预测" width="200">
          <template #default="scope">
            <div class="attributes">
              <el-tag size="small">{{ scope.row.attribute_predictions.special_odd_even }}</el-tag>
              <el-tag size="small" type="success">{{ scope.row.attribute_predictions.special_big_small }}</el-tag>
              <el-tag size="small" type="warning">{{ scope.row.attribute_predictions.special_color }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="actual_result" label="实际开奖" width="100" />
        <el-table-column label="准确度" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.accuracy ? Math.round(scope.row.accuracy * 100) : 0"
              :status="getAccuracyStatus(scope.row.accuracy)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              v-if="!scope.row.actual_result"
              size="small"
              @click="handleEvaluate(scope.row)"
            >
              评估
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 评估对话框 -->
      <el-dialog
        v-model="evaluateDialogVisible"
        title="评估预测结果"
        width="30%"
      >
        <el-form :model="evaluateForm" label-width="120px">
          <el-form-item label="实际开奖号码">
            <el-input-number
              v-model="evaluateForm.actual_result"
              :min="1"
              :max="49"
              controls-position="right"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="evaluateDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitEvaluation">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 详情对话框 -->
      <el-dialog
        v-model="detailDialogVisible"
        title="预测详情"
        width="60%"
      >
        <div v-if="selectedPrediction" class="prediction-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="期号">{{ selectedPrediction.expect || '暂无期号数据' }}</el-descriptions-item>
            <el-descriptions-item label="预测时间">{{ formatDateTime(selectedPrediction.prediction_time) }}</el-descriptions-item>
          </el-descriptions>

          <div class="detail-section">
            <h4>预测号码</h4>
            <div v-for="(numbers, key) in {
              special_numbers_5: '5码',
              special_numbers_10: '10码',
              special_numbers_15: '15码',
              special_numbers_20: '20码',
              special_numbers_30: '30码'
            }" :key="key" class="number-group">
              <span class="group-label">{{ numbers }}:</span>
              <div class="number-list" v-if="selectedPrediction[key] && Array.isArray(selectedPrediction[key]) && selectedPrediction[key].length > 0">
                <span
                  v-for="num in selectedPrediction[key]"
                  :key="num"
                  class="number"
                >
                  {{ String(num).padStart(2, '0') }}
                </span>
              </div>
              <div class="number-list" v-else>
                <span class="no-data">暂无数据</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>属性预测</h4>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="单双">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_odd_even || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="大小">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_big_small || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="波色">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_color || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="家禽/野兽">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_animal_type || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="五行">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_element || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="尾数大小">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_tail_big_small || '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="合数单双">{{ selectedPrediction.attribute_predictions && selectedPrediction.attribute_predictions.special_sum_odd_even || '暂无数据' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="detail-section">
            <h4>生肖预测</h4>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="3肖">{{ selectedPrediction.zodiac_3 && Array.isArray(selectedPrediction.zodiac_3) ? selectedPrediction.zodiac_3.join(', ') : '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="5肖">{{ selectedPrediction.zodiac_5 && Array.isArray(selectedPrediction.zodiac_5) ? selectedPrediction.zodiac_5.join(', ') : '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="7肖">{{ selectedPrediction.zodiac_7 && Array.isArray(selectedPrediction.zodiac_7) ? selectedPrediction.zodiac_7.join(', ') : '暂无数据' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="detail-section">
            <h4>竞猜策略</h4>
            <p class="strategy">{{ selectedPrediction.strategy || '暂无策略建议' }}</p>
          </div>

          <div class="detail-section">
            <h4>预测置信度</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="LSTM模型">{{ selectedPrediction.confidence_scores && typeof selectedPrediction.confidence_scores.lstm === 'number' ? (selectedPrediction.confidence_scores.lstm * 100).toFixed(2) + '%' : '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="随机森林模型">{{ selectedPrediction.confidence_scores && typeof selectedPrediction.confidence_scores.rf === 'number' ? (selectedPrediction.confidence_scores.rf * 100).toFixed(2) + '%' : '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="XGBoost模型">{{ selectedPrediction.confidence_scores && typeof selectedPrediction.confidence_scores.xgboost === 'number' ? (selectedPrediction.confidence_scores.xgboost * 100).toFixed(2) + '%' : '暂无数据' }}</el-descriptions-item>
              <el-descriptions-item label="综合置信度">{{ selectedPrediction.confidence_scores && typeof selectedPrediction.confidence_scores.combined === 'number' ? (selectedPrediction.confidence_scores.combined * 100).toFixed(2) + '%' : '暂无数据' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="selectedPrediction.actual_result" class="detail-section">
            <h4>预测结果</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="实际开奖">{{ selectedPrediction.actual_result }}</el-descriptions-item>
              <el-descriptions-item label="准确度">{{ typeof selectedPrediction.accuracy === 'number' ? (selectedPrediction.accuracy * 100).toFixed(2) + '%' : '暂无数据' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getPredictions, evaluatePrediction, getPrediction } from '@/api/prediction'
import dayjs from 'dayjs'

// 状态变量
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const evaluateDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const evaluateForm = ref({
  expect: '',
  actual_result: null
})
const selectedPrediction = ref(null)

// 搜索表单
const searchForm = ref({
  expect: '',
  dateRange: null,
  accuracy: ''
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const response = await getPredictions({
      page: currentPage.value,
      page_size: pageSize.value,
      expect: searchForm.value.expect,
      start_date: searchForm.value.dateRange?.[0],
      end_date: searchForm.value.dateRange?.[1],
      min_accuracy: searchForm.value.accuracy ? Number(searchForm.value.accuracy) / 100 : null
    })
    // 处理响应数据
    if (response.data) {
      tableData.value = response.data
    } else if (response.items) {
      tableData.value = response.items
    } else {
      tableData.value = []
    }

    // 处理总数
    if (response.headers && response.headers['x-total-count']) {
      total.value = parseInt(response.headers['x-total-count'])
    } else if (response.total) {
      total.value = response.total
    } else {
      total.value = tableData.value.length
    }
  } catch (error) {
    ElMessage.error('加载数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    expect: '',
    dateRange: null,
    accuracy: ''
  }
  handleSearch()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 评估
const handleEvaluate = (row) => {
  evaluateForm.value = {
    expect: row.expect,
    actual_result: null
  }
  evaluateDialogVisible.value = true
}

const submitEvaluation = async () => {
  try {
    await evaluatePrediction(
      evaluateForm.value.expect,
      evaluateForm.value.actual_result
    )
    ElMessage.success('评估完成')
    evaluateDialogVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('评估失败: ' + error.message)
  }
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    loading.value = true
    console.log('开始获取预测详情，期号:', row.expect)
    console.log('原始表格行数据:', row)

    // 先获取完整的预测详情
    const response = await getPrediction(row.expect)
    console.log('获取到的API响应:', response)

    if (response) {
      // 合并数据，确保保留原始数据中的有效字段
      const mergedData = { ...row, ...response }
      console.log('合并后的数据:', mergedData)
      selectedPrediction.value = mergedData
    } else {
      selectedPrediction.value = row
      console.warn('未能获取完整预测详情，使用表格行数据')
    }

    // 检查关键字段是否存在
    console.log('最终选中的预测数据:', selectedPrediction.value)
    console.log('attribute_predictions 存在吗?', !!selectedPrediction.value.attribute_predictions)
    console.log('confidence_scores 存在吗?', !!selectedPrediction.value.confidence_scores)
    console.log('zodiac_3 存在吗?', !!selectedPrediction.value.zodiac_3)

    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取预测详情失败:', error)
    ElMessage.error('获取预测详情失败: ' + error.message)
    // 使用表格行数据作为备用
    selectedPrediction.value = row
    detailDialogVisible.value = true
  } finally {
    loading.value = false
  }
}

// 导出数据
const exportData = () => {
  // TODO: 实现导出功能
  ElMessage.warning('功能开发中')
}

// 工具函数
const formatDateTime = (datetime) => {
  if (!datetime) return '暂无时间数据'
  try {
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
  } catch (error) {
    console.error('时间格式化错误:', error)
    return '时间格式错误'
  }
}

const getAccuracyStatus = (accuracy) => {
  if (!accuracy) return ''
  const percentage = accuracy * 100
  if (percentage >= 80) return 'success'
  if (percentage >= 60) return 'warning'
  return 'exception'
}

// 生命周期钩子
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.history-page {
  padding: 20px;
}

.history-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.search-form {
  margin-bottom: 20px;
}

.number-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .number {
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background-color: var(--el-color-primary-light-9);
    border-radius: 50%;
    font-size: 13px;
  }

  .no-data {
    color: var(--el-text-color-secondary);
    font-style: italic;
  }
}

.attributes {
  display: flex;
  gap: 4px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.prediction-detail {
  .detail-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 10px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }

  .number-group {
    margin-bottom: 10px;

    .group-label {
      display: inline-block;
      width: 60px;
      font-weight: bold;
    }
  }

  .strategy {
    padding: 15px;
    background-color: var(--el-bg-color-page);
    border-radius: 4px;
    line-height: 1.6;
  }
}
</style>