#!/usr/bin/env python3
"""
测试连号分析修复
"""
import requests
import json
import time

def test_consecutive_analysis():
    """测试连号分析功能"""
    print("🔗 测试连号分析修复")
    print("=" * 50)
    
    try:
        # 获取统计数据
        print("📡 获取统计数据...")
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        
        if response.status_code != 200:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return False
        
        data = response.json()['data']
        number_frequency = data.get('numberFrequency', {})
        
        if not number_frequency:
            print("❌ 号码频率数据为空")
            return False
        
        print(f"✅ 成功获取数据，包含 {len(number_frequency)} 个号码")
        
        # 分析连号数据
        print("\n🔍 分析连号数据...")
        consecutive_pairs = []
        
        for i in range(1, 49):  # 1-48
            current_count = number_frequency.get(str(i), 0)
            next_count = number_frequency.get(str(i + 1), 0)
            
            if current_count > 0 and next_count > 0:
                strength = min(current_count, next_count)
                consecutive_pairs.append((i, i + 1, strength))
        
        consecutive_pairs.sort(key=lambda x: x[2], reverse=True)
        
        print(f"📊 发现 {len(consecutive_pairs)} 对有效连号")
        
        if consecutive_pairs:
            print("\n🔥 连号强度排行榜 (前5名):")
            for i, (num1, num2, strength) in enumerate(consecutive_pairs[:5]):
                print(f"   {i+1}. 号码{num1:02d}-{num2:02d}: 强度 {strength}")
        
        # 模拟前端热力图数据生成
        print("\n🔥 模拟热力图数据生成...")
        heatmap_data = []
        
        for num1, num2, strength in consecutive_pairs:
            # 前向连号
            heatmap_data.append([num1-1, num2-1, strength])
        
        print(f"📈 热力图数据点数量: {len(heatmap_data)}")
        
        if heatmap_data:
            max_strength = max(item[2] for item in heatmap_data)
            print(f"📊 最大连号强度: {max_strength}")
            
            print("\n🎯 热力图数据示例 (前5个):")
            for i, (x, y, strength) in enumerate(heatmap_data[:5]):
                print(f"   {i+1}. 坐标({x},{y}): 强度 {strength}")
        
        # 检查数据完整性
        print("\n✅ 数据完整性检查:")
        print(f"   - 连号对数量: {len(consecutive_pairs)}")
        print(f"   - 热力图数据点: {len(heatmap_data)}")
        print(f"   - 数据一致性: {'✅ 通过' if len(heatmap_data) == len(consecutive_pairs) else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_chart_types():
    """测试不同图表类型的数据结构"""
    print("\n📊 测试图表类型数据结构")
    print("=" * 50)
    
    try:
        # 获取数据
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        data = response.json()['data']
        number_frequency = data.get('numberFrequency', {})
        
        # 模拟前端数据处理
        consecutive_data = []  # 关系图数据
        heatmap_data = []      # 热力图数据
        sankey_nodes = []      # 桑基图节点
        sankey_links = []      # 桑基图连线
        
        for i in range(1, 49):
            current_count = number_frequency.get(str(i), 0)
            next_count = number_frequency.get(str(i + 1), 0)
            
            # 添加桑基图节点
            if current_count > 0:
                sankey_nodes.append({
                    'name': str(i),
                    'value': current_count
                })
            
            if current_count > 0 and next_count > 0:
                strength = min(current_count, next_count)
                
                # 关系图数据
                consecutive_data.append({
                    'source': str(i),
                    'target': str(i + 1),
                    'value': strength
                })
                
                # 热力图数据
                heatmap_data.append([i-1, i, strength])
                
                # 桑基图连线
                sankey_links.append({
                    'source': str(i),
                    'target': str(i + 1),
                    'value': strength
                })
        
        print("📈 各图表类型数据统计:")
        print(f"   🌐 关系图连线: {len(consecutive_data)}")
        print(f"   🔥 热力图数据点: {len(heatmap_data)}")
        print(f"   🌊 桑基图节点: {len(sankey_nodes)}")
        print(f"   🌊 桑基图连线: {len(sankey_links)}")
        
        # 检查数据有效性
        all_valid = True
        
        if len(heatmap_data) == 0:
            print("⚠️  警告: 热力图数据为空")
            all_valid = False
        
        if len(consecutive_data) == 0:
            print("⚠️  警告: 关系图数据为空")
            all_valid = False
        
        if len(sankey_nodes) == 0:
            print("⚠️  警告: 桑基图节点为空")
            all_valid = False
        
        if all_valid:
            print("✅ 所有图表类型数据都有效")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 图表类型测试失败: {e}")
        return False

def generate_fix_summary():
    """生成修复总结"""
    print("\n🔧 修复总结")
    print("=" * 50)
    
    print("🎯 主要修复内容:")
    print("   1. 修复热力图 visualMap 的 max 值计算错误")
    print("   2. 添加数据验证和错误处理")
    print("   3. 增加安全的图表更新机制")
    print("   4. 添加调试日志便于问题排查")
    
    print("\n🛠️ 技术细节:")
    print("   - 修复: Math.max(...heatmapData.map(item => item[2]), 1)")
    print("   - 改为: heatmapData.length > 0 ? Math.max(...heatmapData.map(item => item[2])) : 1")
    print("   - 添加: try-catch 错误处理")
    print("   - 添加: 数据验证日志")
    
    print("\n📊 图表功能:")
    print("   🔥 热力图: 颜色深浅表示连号关系强度")
    print("   🌐 关系图: 节点和连线展示号码网络")
    print("   ⭕ 和弦图: 圆形布局显示整体关系")
    print("   🌊 桑基图: 流量宽度表示连号强度")
    
    print("\n🎮 使用方法:")
    print("   1. 访问: http://localhost:3000/statistics")
    print("   2. 滚动到 '特码连号分析' 部分")
    print("   3. 点击右上角切换图表类型")
    print("   4. 悬停或点击查看详细信息")

def main():
    """主函数"""
    print("🔗 特码连号分析修复测试")
    print("=" * 60)
    
    # 测试连号分析
    test1_result = test_consecutive_analysis()
    
    # 测试图表类型
    test2_result = test_chart_types()
    
    # 生成修复总结
    generate_fix_summary()
    
    print(f"\n📋 测试结果:")
    print(f"   连号分析功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   图表类型数据: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！连号分析功能已修复")
        print("💡 建议: 刷新浏览器页面查看修复效果")
    else:
        print("\n😞 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
