import sqlite3

conn = sqlite3.connect('backend/data/lottery.db')
cursor = conn.cursor()

# 检查2025年期号连续性
cursor.execute("""
    SELECT expect 
    FROM draws 
    WHERE expect LIKE '2025%'
    ORDER BY expect
""")

all_expects = [row[0] for row in cursor.fetchall()]
expect_nums = [int(expect[4:]) for expect in all_expects]

if expect_nums:
    min_num = min(expect_nums)
    max_num = max(expect_nums)
    
    missing = []
    for num in range(min_num, max_num + 1):
        if num not in expect_nums:
            missing.append(f'2025{num:03d}')
    
    print(f'2025年期号范围: 2025{min_num:03d} - 2025{max_num:03d}')
    print(f'总期数: {len(all_expects)}')
    if missing:
        print(f'缺失期号: {missing}')
    else:
        print('期号连续，无缺失')

conn.close()
