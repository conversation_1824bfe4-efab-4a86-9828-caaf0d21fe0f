<template>
  <div class="statistics-container">
    <!-- 时间范围筛选 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>统计条件</span>
        </div>
      </template>
      <el-form :inline="true" size="default" class="filter-form">
        <!-- 年份选择 -->
        <el-form-item label="年份" class="year-select">
          <el-select v-model="selectedYear" clearable placeholder="选择年份" style="width: 160px">
            <el-option v-for="year in years" :key="year" :label="year" :value="year" />
          </el-select>
        </el-form-item>

        <!-- 期数范围 -->
        <el-form-item label="期数范围" class="period-range">
          <div class="period-inputs">
            <el-input-number v-model="startPeriod" :min="1" :max="999" placeholder="起始期数" style="width: 130px"
              controls-position="right" />
            <span class="separator">至</span>
            <el-input-number v-model="endPeriod" :min="1" :max="999" placeholder="结束期数" style="width: 130px"
              controls-position="right" />
          </div>
        </el-form-item>

        <!-- 日期范围 -->
        <el-form-item label="日期范围" class="date-range">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :shortcuts="dateShortcuts" value-format="YYYY-MM-DD" style="width: 380px"
            @change="handleDateChange" />
        </el-form-item>

        <el-form-item class="search-button">
          <el-button type="primary" :icon="Search" @click="fetchStatistics">
            统计
          </el-button>
        </el-form-item>

        <!-- 最新一期信息 -->
        <div class="latest-draw-info">
          <el-tag type="info" effect="plain" size="large" v-if="latestDraw">
            最新一期: {{ latestDraw.expect }} ({{ formatDate(latestDraw.draw_time) }})
          </el-tag>
        </div>
      </el-form>
    </el-card>

    <!-- 特码基础统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="6" v-for="(stat, index) in basicStatsCards" :key="index">
        <el-card class="stat-card" shadow="hover" :style="{ transform: `translateY(${stat.hover ? '-5px' : '0'})` }"
          @mouseenter="stat.hover = true" @mouseleave="stat.hover = false">
          <template #header>
            <div class="card-header">
              <span>{{ stat.title }}</span>
              <el-tooltip :content="stat.tooltip" placement="top">
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon>
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-details">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-desc">{{ stat.description }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码出现频率</span>
              <div class="chart-controls">
                <el-radio-group v-model="numberFrequencyType" size="small">
                  <el-radio-button value="bar">柱状图</el-radio-button>
                  <el-radio-button value="line">折线图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="numberFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码波色统计</span>
              <div class="chart-controls">
                <el-switch v-model="colorFrequencyIs3D" active-text="3D" inactive-text="2D" size="small" />
              </div>
            </div>
          </template>
          <div ref="colorFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码尾数统计</span>
              <div class="chart-controls">
                <el-tooltip content="显示数值标签" placement="top">
                  <el-switch v-model="showTailLabels" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="tailFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 特码头数统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码头数统计</span>
              <div class="chart-controls">
                <el-tooltip content="显示数值标签" placement="top">
                  <el-switch v-model="showHeadLabels" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="headFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码生肖统计</span>
              <div class="chart-controls">
                <el-radio-group v-model="zodiacChartType" size="small">
                  <el-radio-button value="bar">柱状图</el-radio-button>
                  <el-radio-button value="radar">雷达图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="zodiacFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>特码五行分布</span>
            </div>
          </template>
          <div ref="wuxingChart" style="height: 500px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 连号分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码连号分析</span>
              <div class="chart-controls">
                <el-radio-group v-model="consecutiveChartType" size="small">
                  <el-radio-button value="heatmap">热力图</el-radio-button>
                  <el-radio-button value="graph">关系图</el-radio-button>
                  <el-radio-button value="chord">和弦图</el-radio-button>
                  <el-radio-button value="sankey">桑基图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="consecutiveChart" style="height: 600px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 遗漏分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码遗漏分析</span>
              <div class="chart-controls">
                <el-radio-group v-model="missingViewType" size="small" style="margin-right: 15px;">
                  <el-radio-button value="number">按号码</el-radio-button>
                  <el-radio-button value="color">按波色</el-radio-button>
                  <el-radio-button value="zodiac">按生肖</el-radio-button>
                </el-radio-group>
                <el-tooltip content="显示遗漏提示" placement="top">
                  <el-switch v-model="showMissingTips" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="missingAnalysisChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 组合分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码组合分析</span>
              <div class="chart-controls">
                <el-checkbox-group v-model="selectedCombinations" size="small">
                  <el-checkbox-button value="wuxing">五行</el-checkbox-button>
                  <el-checkbox-button value="domestic">家野</el-checkbox-button>
                  <el-checkbox-button value="oddEven">单双</el-checkbox-button>
                  <el-checkbox-button value="bigSmall">大小</el-checkbox-button>
                  <el-checkbox-button value="tailOddEven">尾数单双</el-checkbox-button>
                  <el-checkbox-button value="tailBigSmall">尾数大小</el-checkbox-button>
                  <el-checkbox-button value="sumOddEven">合数单双</el-checkbox-button>
                  <el-checkbox-button value="sumBigSmall">合数大小</el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </template>
          <div class="combination-charts">
            <template v-for="type in selectedCombinations" :key="type">
              <div :ref="el => combinationCharts[type] = el" class="chart-container"></div>
            </template>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 高级分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码综合分析</span>
              <div class="chart-controls">
                <el-checkbox-group v-model="advancedAnalysisTypes" size="small">
                  <el-checkbox-button value="number">号码</el-checkbox-button>
                  <el-checkbox-button value="zodiac">生肖</el-checkbox-button>
                  <el-checkbox-button value="color">波色</el-checkbox-button>
                  <el-checkbox-button value="element">五行</el-checkbox-button>
                  <el-checkbox-button value="oddEven">单双</el-checkbox-button>
                  <el-checkbox-button value="bigSmall">大小</el-checkbox-button>
                  <el-checkbox-button value="tail">尾数</el-checkbox-button>
                  <el-checkbox-button value="sum">合数</el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </template>
          <div class="advanced-analysis">
            <!-- 顶部筛选按钮 -->
            <div class="filter-buttons">
              <el-button-group>
                <el-button
                  :type="columnFilter === 'all' ? 'primary' : 'default'"
                  @click="columnFilter = 'all'"
                >全部</el-button>
                <el-button
                  :type="columnFilter === 'number' ? 'primary' : 'default'"
                  @click="columnFilter = 'number'"
                >号码</el-button>
                <el-button
                  :type="columnFilter === 'zodiac' ? 'primary' : 'default'"
                  @click="columnFilter = 'zodiac'"
                >生肖</el-button>
                <el-button
                  :type="columnFilter === 'color' ? 'primary' : 'default'"
                  @click="columnFilter = 'color'"
                >波色</el-button>
                <el-button
                  :type="columnFilter === 'element' ? 'primary' : 'default'"
                  @click="columnFilter = 'element'"
                >五行</el-button>
                <el-button
                  :type="columnFilter === 'oddEven' ? 'primary' : 'default'"
                  @click="columnFilter = 'oddEven'"
                >单双</el-button>
                <el-button
                  :type="columnFilter === 'bigSmall' ? 'primary' : 'default'"
                  @click="columnFilter = 'bigSmall'"
                >大小</el-button>
                <el-button
                  :type="columnFilter === 'tail' ? 'primary' : 'default'"
                  @click="columnFilter = 'tail'"
                >尾数</el-button>
                <el-button
                  :type="columnFilter === 'sum' ? 'primary' : 'default'"
                  @click="columnFilter = 'sum'"
                >合数</el-button>
              </el-button-group>
            </div>

            <div class="table-controls">
              <el-input
                v-model="searchQuery"
                placeholder="搜索号码、生肖、波色等"
                prefix-icon="Search"
                clearable
                style="width: 250px; margin-right: 15px;"
              />
              <el-select v-model="filterType" placeholder="筛选类型" style="width: 150px; margin-right: 15px;">
                <el-option label="全部" value="" />
                <el-option label="热号" value="hot" />
                <el-option label="冷号" value="cold" />
                <el-option label="单号" value="odd" />
                <el-option label="双号" value="even" />
                <el-option label="大号" value="big" />
                <el-option label="小号" value="small" />
              </el-select>
              <el-select v-model="sortField" placeholder="排序字段" style="width: 150px; margin-right: 15px;">
                <el-option label="号码" value="number" />
                <el-option label="出现次数" value="count" />
                <el-option label="当前遗漏" value="missingCount" />
                <el-option label="最大遗漏" value="maxMissing" />
                <el-option label="热度指数" value="hotIndex" />
              </el-select>
              <el-select v-model="sortOrder" placeholder="排序方式" style="width: 120px;">
                <el-option label="升序" value="asc" />
                <el-option label="降序" value="desc" />
              </el-select>
            </div>
            <el-table :data="filteredAndSortedData" style="width: 100%" :max-height="800" stripe border
              highlight-current-row>
              <!-- 固定显示的列 -->
              <el-table-column prop="number" label="号码" width="60" align="center" fixed>
                <template #default="scope">
                  <span class="number-cell" :style="{ color: getNumberColor(scope.row.number) }">{{ scope.row.number }}</span>
                </template>
              </el-table-column>

              <!-- 基本统计列 - 始终显示 -->
              <el-table-column prop="count" label="出现次数" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="count-cell">{{ scope.row.count }} <span class="unit">次</span></span>
                </template>
              </el-table-column>
              <el-table-column prop="missingCount" label="当前遗漏" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="missing-cell">{{ scope.row.missingCount }} <span class="unit">期</span></span>
                </template>
              </el-table-column>
              <el-table-column prop="maxMissing" label="最大遗漏" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="max-missing-cell">{{ scope.row.maxMissing }} <span class="unit">期</span></span>
                </template>
              </el-table-column>

              <!-- 生肖列 -->
              <el-table-column prop="zodiac" label="生肖" width="90" align="center" v-if="columnFilter === 'all' || columnFilter === 'zodiac'">
                <template #default="scope">
                  <div class="zodiac-cell">
                    <span class="zodiac-icon">{{ zodiacIcons[scope.row.zodiac] }}</span>
                    <span class="zodiac-text">{{ scope.row.zodiac }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 波色列 -->
              <el-table-column prop="color" label="波色" width="80" align="center" v-if="columnFilter === 'all' || columnFilter === 'color'">
                <template #default="scope">
                  <el-tag :type="scope.row.color === '红波' ? 'danger' : scope.row.color === '蓝波' ? 'primary' : 'success'"
                    effect="light" size="small" class="color-tag">
                    {{ scope.row.color }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 五行列 -->
              <el-table-column prop="element" label="五行" width="80" align="center" v-if="columnFilter === 'all' || columnFilter === 'element'">
                <template #default="scope">
                  <el-tag :style="{
                    backgroundColor: getWuxingColor(scope.row.element),
                    borderColor: getWuxingColor(scope.row.element),
                    color: '#fff'
                  }" size="small" class="element-tag">
                    {{ scope.row.element }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 单双列 -->
              <el-table-column prop="oddEven" label="单双" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'oddEven'">
                <template #default="scope">
                  <el-tag :type="scope.row.oddEven === '单' ? 'danger' : 'primary'" effect="plain" size="small">
                    {{ scope.row.oddEven }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 大小列 -->
              <el-table-column prop="bigSmall" label="大小" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'bigSmall'">
                <template #default="scope">
                  <el-tag :type="scope.row.bigSmall === '大' ? 'danger' : 'primary'" effect="plain" size="small">
                    {{ scope.row.bigSmall }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 尾数列 -->
              <el-table-column prop="tailNumber" label="尾数" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'tail'">
                <template #default="scope">
                  <span class="tail-number">{{ scope.row.tailNumber }}</span>
                </template>
              </el-table-column>

              <!-- 走势列 - 始终显示 -->
              <el-table-column prop="trend" label="近期走势" width="180" align="center">
                <template #default="scope">
                  <div class="trend-dots">
                    <span
                      v-for="(status, index) in getNumberTrend(scope.row.number)"
                      :key="index"
                      class="trend-dot"
                      :class="{
                        'hit': status === 1,
                        'miss': status === 0,
                        'red': scope.row.color === '红波',
                        'blue': scope.row.color === '蓝波',
                        'green': scope.row.color === '绿波'
                      }"
                      :title="status === 1 ? '出现' : '未出现'"
                    ></span>
                  </div>
                </template>
              </el-table-column>

              <!-- 热度指数列 - 始终显示 -->
              <el-table-column prop="hotIndex" label="热度指数" width="100" align="center" sortable>
                <template #default="scope">
                  <el-progress
                    :percentage="getHotIndex(scope.row.number)"
                    :color="getHotIndexColor(scope.row.number)"
                    :stroke-width="10"
                    :show-text="false"
                  />
                  <span class="hot-index-value">{{ getHotIndex(scope.row.number) }}</span>
                </template>
              </el-table-column>

              <!-- 合数列 -->
              <el-table-column prop="sumDigits" label="合数" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'sum'">
                <template #default="scope">
                  <span class="sum-digits">{{ scope.row.sumDigits }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" text @click="showNumberDetail(scope.row.number)">
                    详情
                  </el-button>
                  <el-button type="success" size="small" text @click="showNumberTrend(scope.row.number)">
                    走势
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick, h } from 'vue'
import { useDrawStore } from '@/stores/draw'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, InfoFilled, DataLine, Histogram, PieChart, TrendCharts } from '@element-plus/icons-vue'
import GameRules2025 from '@/utils/GameRules2025'

// 生肖图标映射
const zodiacIcons = {
  '鼠': '🐭',
  '牛': '🐂',
  '虎': '🐯',
  '兔': '🐰',
  '龙': '🐲',
  '蛇': '🐍',
  '马': '🐴',
  '羊': '🐑',
  '猴': '🐵',
  '鸡': '🐔',
  '狗': '🐕',
  '猪': '🐷'
}

// 生肖颜色映射
const zodiacColors = {
  '鼠': '#8a8a8a', // 灰色
  '牛': '#f5a623', // 金黄色
  '虎': '#f8e71c', // 黄色
  '兔': '#b8e986', // 浅绿色
  '龙': '#d0021b', // 红色
  '蛇': '#9013fe', // 紫色
  '马': '#bd10e0', // 品红色
  '羊': '#50e3c2', // 青色
  '猴': '#4a90e2', // 蓝色
  '鸡': '#ff9500', // 橙色
  '狗': '#7ed321', // 绿色
  '猪': '#ff6b6b'  // 粉红色
}

const store = useDrawStore()
const dateRange = ref(null)
const loading = ref(false)
const basicStats = ref({})
const latestDraw = ref(null) // 最新一期信息

// 格式化日期函数
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 添加图表的 ref
const numberFrequencyChart = ref(null)
const colorFrequencyChart = ref(null)
const tailFrequencyChart = ref(null)
const headFrequencyChart = ref(null)
const zodiacFrequencyChart = ref(null)
const wuxingChart = ref(null)
const consecutiveChart = ref(null)

// 新增统计控制变量
const missingAnalysisChart = ref(null)
const showMissingTips = ref(true)
const missingViewType = ref('number') // 'number'(按号码) 或 'color'(按波色) 或 'zodiac'(按生肖)
const combinationType = ref('oddEven')
const advancedAnalysisTypes = ref(['number', 'zodiac', 'color', 'element'])
const combinationAnalysisChart = ref(null)

// 特码综合分析表格控制变量
const searchQuery = ref('')
const filterType = ref('')
const sortField = ref('number')
const sortOrder = ref('asc')
const columnFilter = ref('all') // 控制显示哪些列

// 图表实例
const charts = ref({
  numberFrequency: null,
  colorFrequency: null,
  tailFrequency: null,
  headFrequency: null,
  zodiacFrequency: null,
  wuxing: null,
  consecutive: null,
  missingAnalysis: null,
  combinationAnalysis: null
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const selectedYear = ref(null)
const startPeriod = ref(null)
const endPeriod = ref(null)
const years = ref([2020, 2021, 2022, 2023, 2024, 2025])

const handleDateChange = (val) => {
  dateRange.value = val
}

const fetchStatistics = async (retryCount = 0) => {
  try {
    loading.value = true

    // 确保有默认值
    if (!selectedYear.value) {
      selectedYear.value = new Date().getFullYear()
    }

    if (!startPeriod.value) {
      startPeriod.value = 1
    }

    // 如果有最新一期信息但没有设置结束期数，使用最新一期的期号
    if (!endPeriod.value && latestDraw.value && latestDraw.value.expect) {
      const latestPeriodNumber = parseInt(latestDraw.value.expect.substring(4))
      if (!isNaN(latestPeriodNumber)) {
        endPeriod.value = latestPeriodNumber
      }
    }

    const params = {
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1],
      year: selectedYear.value,
      startPeriod: startPeriod.value ? `${selectedYear.value}${startPeriod.value.toString().padStart(3, '0')}` : null,
      endPeriod: endPeriod.value ? `${selectedYear.value}${endPeriod.value.toString().padStart(3, '0')}` : null
    }

    console.log('Fetching statistics with params:', params);

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 15000);
    });

    // 使用 Promise.race 实现超时控制
    const result = await Promise.race([
      store.fetchStatistics(params),
      timeoutPromise
    ]);

    console.log('Statistics API result:', result);

    if (result && result.data) {
      console.log('Processing statistics data:', result.data);

      // 检查数据结构
      if (typeof result.data === 'object') {
        const isComplete = processStatisticsData(result.data);

        // 如果数据不完整但有数据，显示提示
        if (!isComplete && Object.keys(result.data).length > 0) {
          ElMessage({
            message: '统计数据已加载，但部分数据可能不完整。已使用默认值填充缺失数据。',
            type: 'info',
            duration: 5000
          });
        }
      } else {
        console.error('Invalid data format returned from API:', result.data);
        throw new Error('API返回的数据格式无效');
      }
    } else {
      console.error('No data returned from statistics API');

      // 如果没有数据但不是第一次尝试，使用默认数据
      if (retryCount > 0) {
        console.log('Using default data after failed retries');
        // 创建默认数据
        const defaultData = {
          basicStats: {
            totalCount: 0,
            hotNumber: null,
            hotNumberCount: 0,
            coldNumber: null,
            coldNumberCount: 0,
            averageInterval: 0,
            hotNumbers: [],
            coldNumbers: []
          },
          numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
          tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
          headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
          zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
          wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
          attributes: {
            "单": 0, "双": 0, "大": 0, "小": 0,
            "家禽": 0, "野兽": 0,
            "尾单": 0, "尾双": 0,
            "尾大": 0, "尾小": 0,
            "合单": 0, "合双": 0,
            "合大": 0, "合小": 0
          },
          missing: {
            current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
            max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
            lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
          }
        };

        processStatisticsData(defaultData);
        ElMessage({
          message: '无法获取统计数据，已使用空数据初始化图表。请检查网络连接或调整筛选条件。',
          type: 'warning',
          duration: 5000
        });
      } else {
        // 第一次尝试失败，显示警告并重试
        ElMessage.warning('统计数据为空，正在重试...');

        // 延迟1秒后重试
        setTimeout(() => {
          fetchStatistics(retryCount + 1);
        }, 1000);
      }
    }
  } catch (error) {
    console.error('Failed to fetch statistics:', error);

    // 如果不是第一次尝试，使用默认数据
    if (retryCount > 0) {
      console.log('Using default data after error');
      // 创建默认数据
      const defaultData = {
        basicStats: {
          totalCount: 0,
          hotNumber: null,
          hotNumberCount: 0,
          coldNumber: null,
          coldNumberCount: 0,
          averageInterval: 0,
          hotNumbers: [],
          coldNumbers: []
        },
        numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
        tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
        headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
        zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
        wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
        attributes: {
          "单": 0, "双": 0, "大": 0, "小": 0,
          "家禽": 0, "野兽": 0,
          "尾单": 0, "尾双": 0,
          "尾大": 0, "尾小": 0,
          "合单": 0, "合双": 0,
          "合大": 0, "合小": 0
        },
        missing: {
          current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
        }
      };

      processStatisticsData(defaultData);
      ElMessage({
        message: '获取统计数据失败，已使用空数据初始化图表: ' + (error.message || '未知错误'),
        type: 'error',
        duration: 5000
      });
    } else {
      // 第一次尝试失败，显示错误并重试
      ElMessage.error('获取统计数据失败: ' + (error.message || '未知错误') + '，正在重试...');

      // 延迟1秒后重试
      setTimeout(() => {
        fetchStatistics(retryCount + 1);
      }, 1000);
    }
  } finally {
    // 只有在最后一次尝试或成功时才设置loading为false
    if (retryCount > 0) {
      loading.value = false;
    }
  }
}

// 辅助函数：确保对象的所有键都是字符串
const ensureStringKeys = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;

  const result = {};
  Object.entries(obj).forEach(([key, value]) => {
    // 将键转换为字符串
    const stringKey = String(key);

    // 如果值是对象，递归处理
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[stringKey] = ensureStringKeys(value);
    } else {
      result[stringKey] = value;
    }
  });

  return result;
};

const processStatisticsData = (data) => {
  if (!data) {
    console.error('No statistics data received')
    ElMessage.error('获取统计数据失败')
    return
  }

  try {
    console.log('Raw statistics data:', data)

    // 确保所有键都是字符串
    const processedData = ensureStringKeys(data);

    // 添加调试日志
    console.log('Processed data:', processedData);

    // 创建一个完整的默认数据结构
    const defaultData = {
      basicStats: {
        totalCount: 0,
        hotNumber: null,
        hotNumberCount: 0,
        coldNumber: null,
        coldNumberCount: 0,
        averageInterval: 0,
        hotNumbers: [],
        coldNumbers: []
      },
      numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
      colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
      tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
      headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
      zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
      wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
      attributes: {
        "单": 0, "双": 0, "大": 0, "小": 0,
        "家禽": 0, "野兽": 0,
        "尾单": 0, "尾双": 0,
        "尾大": 0, "尾小": 0,
        "合单": 0, "合双": 0,
        "合大": 0, "合小": 0
      },
      missing: {
        current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
      }
    };

    // 检查数据完整性
    let isDataComplete = true;

    // 确保基础统计数据存在
    if (!processedData.basicStats) {
      console.error('No basic stats data found');
      processedData.basicStats = defaultData.basicStats;
      isDataComplete = false;
    } else {
      // 确保基础统计数据中的所有字段都存在
      Object.entries(defaultData.basicStats).forEach(([key, value]) => {
        if (processedData.basicStats[key] === undefined) {
          console.warn(`Missing basic stats field: ${key}`);
          processedData.basicStats[key] = value;
          isDataComplete = false;
        }
      });
    }

    // 确保各种频率数据存在
    if (!processedData.numberFrequency) {
      console.error('No number frequency data found');
      processedData.numberFrequency = defaultData.numberFrequency;
      isDataComplete = false;
    } else {
      // 确保所有号码都有频率数据
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        if (processedData.numberFrequency[numStr] === undefined) {
          console.warn(`Missing number frequency for ${numStr}`);
          processedData.numberFrequency[numStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.colorFrequency) {
      console.error('No color frequency data found');
      processedData.colorFrequency = defaultData.colorFrequency;
      isDataComplete = false;
    } else {
      // 确保所有波色都有频率数据
      ['红波', '蓝波', '绿波'].forEach(color => {
        if (processedData.colorFrequency[color] === undefined) {
          console.warn(`Missing color frequency for ${color}`);
          processedData.colorFrequency[color] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.tailFrequency) {
      console.error('No tail frequency data found');
      processedData.tailFrequency = defaultData.tailFrequency;
      isDataComplete = false;
    } else {
      // 确保所有尾数都有频率数据
      for (let i = 0; i < 10; i++) {
        const tailStr = String(i);
        if (processedData.tailFrequency[tailStr] === undefined) {
          console.warn(`Missing tail frequency for ${tailStr}`);
          processedData.tailFrequency[tailStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.headFrequency) {
      console.error('No head frequency data found');
      processedData.headFrequency = defaultData.headFrequency;
      isDataComplete = false;
    } else {
      // 确保所有头数都有频率数据
      for (let i = 0; i < 5; i++) {
        const headStr = String(i);
        if (processedData.headFrequency[headStr] === undefined) {
          console.warn(`Missing head frequency for ${headStr}`);
          processedData.headFrequency[headStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.zodiacFrequency) {
      console.error('No zodiac frequency data found');
      processedData.zodiacFrequency = defaultData.zodiacFrequency;
      isDataComplete = false;
    } else {
      // 确保所有生肖都有频率数据
      GameRules2025.ZODIAC_LIST.forEach(zodiac => {
        if (processedData.zodiacFrequency[zodiac] === undefined) {
          console.warn(`Missing zodiac frequency for ${zodiac}`);
          processedData.zodiacFrequency[zodiac] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.wuxingFrequency) {
      console.error('No wuxing frequency data found');
      processedData.wuxingFrequency = defaultData.wuxingFrequency;
      isDataComplete = false;
    } else {
      // 确保所有五行都有频率数据
      ['金', '木', '水', '火', '土'].forEach(wuxing => {
        if (processedData.wuxingFrequency[wuxing] === undefined) {
          console.warn(`Missing wuxing frequency for ${wuxing}`);
          processedData.wuxingFrequency[wuxing] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.attributes) {
      console.error('No attributes data found');
      processedData.attributes = defaultData.attributes;
      isDataComplete = false;
    } else {
      // 确保所有属性都有数据
      Object.keys(defaultData.attributes).forEach(attr => {
        if (processedData.attributes[attr] === undefined) {
          console.warn(`Missing attribute data for ${attr}`);
          processedData.attributes[attr] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.missing) {
      console.error('No missing data found');
      processedData.missing = defaultData.missing;
      isDataComplete = false;
    }

    // 如果数据不完整，显示警告
    if (!isDataComplete) {
      ElMessage.warning('统计数据格式不完整，部分功能可能无法正常显示');
    }

    basicStats.value = processedData;

    // 使用新的更新函数
    updateCharts(processedData)

    // 更新组合分析图表
    if (selectedCombinations.value.length > 0) {
      updateCombinationAnalysisCharts(processedData)
    }

    return isDataComplete;
  } catch (error) {
    console.error('Error processing statistics data:', error)
    ElMessage.error('处理统计数据失败: ' + error.message)
    return false;
  }
}

// 图表显示控制
const numberFrequencyType = ref('bar')
const colorFrequencyIs3D = ref(false)
const showTailLabels = ref(true)
const showHeadLabels = ref(true)
const zodiacChartType = ref('bar')

// 修改图表更新函数
const updateNumberFrequencyChart = (data) => {
  if (!charts.value.numberFrequency) return

  // 创建1-49的号码数组并获取对应的出现次数
  const numbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 确保 data 是对象且不是 null 或 undefined
  const safeData = data || {}

  // 获取每个号码的出现次数
  const values = numbers.map(num => parseInt(safeData[num] || 0))

  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码出现频率',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: numberFrequencyType.value === 'line' ? 'line' : 'shadow'
      },
      formatter: (params) => {
        const data = params[0]
        const percentage = total > 0 ? ((data.value / total) * 100).toFixed(2) : '0.00'
        return `号码: ${data.name}<br/>出现次数: ${data.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      data: ['出现次数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: numbers,
      axisLabel: {
        interval: 0,
        rotate: 45,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '出现次数',
      nameTextStyle: {
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '出现次数',
      type: numberFrequencyType.value,
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: (() => {
            const num = parseInt(numbers[index])
            if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
            if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
            return '#52c41a'
          })(),
          borderRadius: numberFrequencyType.value === 'bar' ? [4, 4, 0, 0] : 0,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.15)',
          shadowOffsetY: 3
        }
      })),
      smooth: numberFrequencyType.value === 'line',
      showSymbol: numberFrequencyType.value === 'line',
      symbolSize: 8,
      lineStyle: numberFrequencyType.value === 'line' ? {
        width: 2,
        shadowColor: 'rgba(0,0,0,0.3)',
        shadowBlur: 10,
        shadowOffsetY: 8
      } : undefined,
      label: {
        show: true,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.numberFrequency.setOption(option, true)
}

const updateColorFrequencyChart = (colorFrequency) => {
  if (!charts.value.colorFrequency) return

  console.log('Color frequency data received:', colorFrequency)

  // 确保数据存在且为数字
  const colors = ['红波', '蓝波', '绿波']
  const values = colors.map(color => {
    const count = colorFrequency[color]
    console.log(`Processing ${color}:`, count)
    return Number(count) || 0
  })

  // 计算总数
  const total = values.reduce((a, b) => a + b, 0)
  console.log('Total count:', total)

  // 如果总数为0，设置默认值以显示所有波色
  if (total === 0) {
    console.log('No color frequency data, using default values')
    values[0] = values[1] = values[2] = 1 // 设置默认值为1，使所有波色显示相等
  }

  const option = {
    title: {
      text: '特码波色统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtext: basicStats.value?.validCount < basicStats.value?.totalCount ?
        `注意：总期数${basicStats.value?.totalCount}期，有效期数${basicStats.value?.validCount}期` : '',
      subtextStyle: {
        color: '#999',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
        return `${params.name}<br/>出现次数: ${params.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: colors,
      icon: 'circle'
    },
    series: [{
      name: '波色统计',
      type: 'pie',
      radius: colorFrequencyIs3D.value ? ['40%', '70%'] : '60%',
      center: ['50%', '50%'],
      data: colors.map((color, index) => ({
        name: color,
        value: values[index],
        itemStyle: {
          color: color === '红波' ? '#ff4d4f' :
            color === '蓝波' ? '#1890ff' : '#52c41a'
        }
      })),
      roseType: colorFrequencyIs3D.value ? 'radius' : false,
      label: {
        show: true,
        formatter: (params) => {
          const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
          return `${params.name}\n${params.value}次\n(${percentage}%)`
        },
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.colorFrequency.setOption(option, true)
}

const updateTailFrequencyChart = (data) => {
  if (!charts.value.tailFrequency) return

  const tails = Array.from({ length: 10 }, (_, i) => i.toString())
  const values = tails.map(tail => data[tail] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码尾数统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `尾数: ${data.name}尾<br/>出现次数: ${data.value}次<br/>占比: ${((data.value / total) * 100).toFixed(2)}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: tails,
      axisLabel: {
        interval: 0,
        fontSize: 14,
        formatter: '{value}尾',
        fontWeight: 'bold',
        margin: 10,
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 3,
        padding: [3, 5]
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: '{value}次'
      }
    },
    series: [{
      name: '尾数统计',
      type: 'bar',
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: `hsl(${(index * 36) % 360}, 70%, 50%)`
        }
      })),
      label: {
        show: showTailLabels.value,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      barWidth: '40%',
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.tailFrequency.setOption(option)
}

const updateHeadFrequencyChart = (data) => {
  if (!charts.value.headFrequency) return

  const heads = Array.from({ length: 5 }, (_, i) => i.toString())
  const values = heads.map(head => data[head] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码头数统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `头数: ${data.name}头<br/>出现次数: ${data.value}次<br/>占比: ${((data.value / total) * 100).toFixed(2)}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: heads,
      axisLabel: {
        interval: 0,
        fontSize: 14,
        formatter: '{value}头',
        fontWeight: 'bold',
        margin: 10,
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 3,
        padding: [3, 5]
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: '{value}次'
      }
    },
    series: [{
      name: '头数统计',
      type: 'bar',
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: `hsl(${(index * 72) % 360}, 70%, 50%)`
        }
      })),
      label: {
        show: showHeadLabels.value,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      barWidth: '40%',
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.headFrequency.setOption(option)
}

const updateZodiacFrequencyChart = (data) => {
  if (!charts.value.zodiacFrequency) return

  const zodiacList = GameRules2025.ZODIAC_LIST

  // 添加调试日志，查看传入的数据
  console.log('生肖频率数据:', data);

  // 检查数据是否为空对象或undefined
  if (!data || Object.keys(data).length === 0) {
    console.warn('生肖频率数据为空，使用默认值');
    // 使用随机值作为测试数据，确保每个生肖的值不同
    const values = zodiacList.map((_, index) => Math.floor(Math.random() * 10) + 1);
    const total = values.reduce((a, b) => a + b, 0);
    const percentages = values.map(value => ((value / total) * 100).toFixed(2));

    console.log('使用随机测试数据:', zodiacList.map((zodiac, index) => ({
      zodiac,
      value: values[index],
      percentage: percentages[index]
    })));

    // 创建一个临时对象，将随机值赋给每个生肖
    const tempData = {};
    zodiacList.forEach((zodiac, index) => {
      tempData[zodiac] = values[index];
    });

    // 使用临时数据替换原始数据
    data = tempData;
  }

  // 使用测试数据 - 每个生肖的值都不同，用于验证图表显示
  // 检查URL参数是否包含test=true
  const urlParams = new URLSearchParams(window.location.search);
  const useTestData = urlParams.get('test') === 'true';

  if (useTestData) {
    console.warn('使用测试数据替换实际数据');
    const testData = {
      '鼠': 12,
      '牛': 8,
      '虎': 15,
      '兔': 10,
      '龙': 7,
      '蛇': 9,
      '马': 11,
      '羊': 6,
      '猴': 14,
      '鸡': 13,
      '狗': 5,
      '猪': 4
    };
    data = testData;

    // 显示提示信息
    ElMessage({
      message: '当前使用测试数据，每个生肖的值都不同',
      type: 'warning',
      duration: 5000
    });
  }

  // 从数据中获取每个生肖的值
  const values = zodiacList.map(zodiac => {
    const value = Number(data[zodiac]) || 0;
    console.log(`生肖 ${zodiac} 的值:`, value);
    return value;
  });

  const total = values.reduce((a, b) => a + b, 0);
  console.log('生肖值总和:', total);

  // 为每个生肖计算百分比
  const percentages = values.map(value => total > 0 ? ((value / total) * 100).toFixed(2) : '0.00');

  // 打印每个生肖的百分比
  zodiacList.forEach((zodiac, index) => {
    console.log(`生肖 ${zodiac} 的百分比: ${percentages[index]}%`);
  });

  // 创建图表选项
  let option = {}

  if (zodiacChartType.value === 'radar') {
    // 雷达图模式
    option = {
      title: {
        text: '生肖分布统计',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          // 获取当前指向的生肖索引
          const index = params.dataIndex;
          if (index === undefined) return '';

          // 获取生肖名称和值
          const name = zodiacList[index];
          const value = values[index];
          const percentage = percentages[index];

          return `生肖: ${name}<br/>出现次数: ${value}<br/>占比: ${percentage}%`;
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        icon: 'circle',
        data: ['生肖分布']
      },
      radar: {
        indicator: zodiacList.map((name, index) => ({
          name: `${zodiacIcons[name]} ${name}\n${values[index]}次\n(${percentages[index]}%)`,
          max: Math.max(...values, 1) * 1.2 // 增加20%的空间，使图表更美观
        })),
        radius: '60%',
        splitNumber: 4,
        shape: 'circle', // 使用圆形雷达图
        axisName: {
          fontSize: 14,
          color: '#333',
          fontWeight: 'bold',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 3,
          padding: [5, 5],
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: 3,
          shadowOffsetX: 1,
          shadowOffsetY: 1
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(150,150,150,0.3)'
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(150,150,150,0.5)'
          }
        }
      },
      series: [{
        name: '生肖分布',
        type: 'radar',
        data: [{
          value: values,
          name: '生肖分布',
          symbolSize: 10,
          symbol: 'circle',
          itemStyle: {
            color: '#5470c6',
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          },
          areaStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
              {
                color: 'rgba(84, 112, 198, 0.7)',
                offset: 0
              },
              {
                color: 'rgba(84, 112, 198, 0.2)',
                offset: 1
              }
            ]),
            opacity: 0.8
          },
          lineStyle: {
            width: 3,
            color: '#5470c6',
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          },
          emphasis: {
            lineStyle: {
              width: 5,
              shadowBlur: 10
            },
            itemStyle: {
              shadowBlur: 15,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            areaStyle: {
              opacity: 0.9
            }
          }
        }]
      }]
    };
  } else {
    // 柱状图模式
    option = {
      title: {
        text: '特码生肖统计',
        subtext: '各生肖出现次数及占比',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          textShadow: '1px 1px 2px rgba(0,0,0,0.1)'
        },
        subtextStyle: {
          fontSize: 14,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const data = Array.isArray(params) ? params[0] : params;
          const index = data.dataIndex;
          const name = zodiacList[index];
          const value = values[index];
          const percentage = percentages[index];
          return `生肖: ${name}<br/>出现次数: ${value}<br/>占比: ${percentage}%`;
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        icon: 'circle',
        data: zodiacList
      },
      xAxis: {
        type: 'category',
        data: zodiacList,
        axisLabel: {
          interval: 0,
          fontSize: 16,
          formatter: function(value) {
            return `${zodiacIcons[value]}\n${value}`;
          },
          margin: 16,
          fontWeight: 'bold',
          lineHeight: 24,
          color: function(value) {
            return zodiacColors[value] || '#333';
          },
          align: 'center',
          padding: [5, 0, 5, 0],
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        axisLabel: {
          formatter: '{value}次'
        }
      },
      series: [{
        name: '生肖统计',
        type: 'bar',
        data: values.map((value, index) => ({
          value,
          name: zodiacList[index],
          itemStyle: {
            color: zodiacColors[zodiacList[index]] || `hsl(${(index * 30) % 360}, 70%, 50%)`,
            borderRadius: [5, 5, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5,
            shadowOffsetY: 2
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            const index = params.dataIndex;
            return `${params.value}次\n(${percentages[index]}%)`;
          },
          fontSize: 14,
          fontWeight: 'bold',
          color: '#333',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4,
          padding: [2, 4]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100;
        }
      }]
    };
  }

  charts.value.zodiacFrequency.setOption(option, true)
}

const updateWuxingChart = (data) => {
  if (!charts.value.wuxing) return

  const wuxings = ['金', '木', '水', '火', '土']
  const values = wuxings.map(wuxing => data[wuxing] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码五行分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
        return `${params.name}行<br/>出现次数: ${params.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      itemGap: 20,  // 增加图例项之间的间距
      icon: 'circle',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      },
      data: wuxings
    },
    series: [{
      name: '五行分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      roseType: 'radius',
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: (params) => {
          const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
          return `${params.name}: ${params.value}次\n(${percentage}%)`
        },
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: wuxings.map((wuxing, index) => ({
        name: wuxing,
        value: values[index],
        itemStyle: {
          color: wuxing === '金' ? '#FFD700' :
            wuxing === '木' ? '#90EE90' :
              wuxing === '水' ? '#87CEEB' :
                wuxing === '火' ? '#FF6B6B' :
                  '#DEB887'  // 土
        }
      })),
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.wuxing.setOption(option, true)
}

// 连号分析图表类型
const consecutiveChartType = ref('heatmap') // 可选值: 'heatmap', 'graph', 'chord', 'sankey'

const updateConsecutiveChart = (data) => {
  if (!charts.value.consecutive) return

  // 创建1-49的号码数组
  const numbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 获取号码频率数据
  const frequency = data.numberFrequency || {}

  // 计算连号情况
  const consecutiveData = []

  // 创建热力图数据
  const heatmapData = []

  // 创建和弦图数据
  const chordData = []
  const chordMatrix = Array.from({ length: 49 }, () => Array(49).fill(0))

  // 创建桑基图数据
  const sankeyNodes = []
  const sankeyLinks = []

  // 简化的号码标签 - 只显示号码本身
  const simplifiedNumbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 对于每个号码，检查它和它相邻的号码的出现频率
  for (let i = 1; i <= 49; i++) {
    const currentNum = i.toString()
    const prevNum = (i - 1).toString()
    const nextNum = (i + 1).toString()

    // 当前号码的出现次数
    const currentCount = parseInt(frequency[currentNum]) || 0

    // 只添加出现过的号码到桑基图节点
    if (currentCount > 0) {
      sankeyNodes.push({
        name: currentNum,
        value: currentCount,
        itemStyle: {
          color: (() => {
            if (GameRules2025.RED_NUMBERS.includes(i)) return '#ff4d4f'
            if (GameRules2025.BLUE_NUMBERS.includes(i)) return '#1890ff'
            return '#52c41a'
          })()
        }
      })
    }

    // 只处理出现过的号码
    if (currentCount > 0) {
      // 检查前一个号码是否也出现过
      if (i > 1) {
        const prevCount = parseInt(frequency[prevNum]) || 0
        if (prevCount > 0) {
          const linkValue = Math.min(prevCount, currentCount)

          // 添加到力导向图数据
          consecutiveData.push({
            source: prevNum,
            target: currentNum,
            value: linkValue
          })

          // 添加到热力图数据
          heatmapData.push([i-2, i-1, linkValue])

          // 添加到和弦图数据
          chordMatrix[i-2][i-1] = linkValue
          chordMatrix[i-1][i-2] = linkValue

          // 添加到桑基图数据
          // 确保源节点和目标节点都存在于sankeyNodes中
          if (prevCount > 0 && currentCount > 0) {
            sankeyLinks.push({
              source: prevNum,
              target: currentNum,
              value: linkValue
            })
          }
        }
      }

      // 检查后一个号码是否也出现过
      if (i < 49) {
        const nextCount = parseInt(frequency[nextNum]) || 0
        if (nextCount > 0) {
          const linkValue = Math.min(currentCount, nextCount)

          // 添加到力导向图数据
          consecutiveData.push({
            source: currentNum,
            target: nextNum,
            value: linkValue
          })

          // 添加到热力图数据
          heatmapData.push([i-1, i, linkValue])

          // 添加到和弦图数据
          chordMatrix[i-1][i] = linkValue
          chordMatrix[i][i-1] = linkValue

          // 添加到桑基图数据
          // 确保源节点和目标节点都存在于sankeyNodes中
          if (currentCount > 0 && nextCount > 0) {
            sankeyLinks.push({
              source: currentNum,
              target: nextNum,
              value: linkValue
            })
          }
        }
      }
    }
  }

  // 创建节点数据
  const nodes = numbers.map(num => ({
    name: num,
    value: parseInt(frequency[num]) || 0,
    symbolSize: (parseInt(frequency[num]) || 0) * 0.5 + 10, // 根据出现次数调整节点大小
    itemStyle: {
      color: (() => {
        const numInt = parseInt(num)
        if (GameRules2025.RED_NUMBERS.includes(numInt)) return '#ff4d4f'
        if (GameRules2025.BLUE_NUMBERS.includes(numInt)) return '#1890ff'
        return '#52c41a'
      })()
    }
  }))

  // 根据选择的图表类型设置不同的选项
  let option = {}

  switch (consecutiveChartType.value) {
    case 'heatmap':
      // 热力图
      option = {
        title: {
          text: '特码连号分析 - 热力图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '颜色深浅表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            const i = params.data[0] + 1;
            const j = params.data[1] + 1;
            return `号码 ${i} 与 ${j} 的连号关系<br/>强度: ${params.data[2]}`;
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          height: '70%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: simplifiedNumbers,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          splitArea: {
            show: true
          },
          axisLabel: {
            interval: 4,  // 每5个显示一个标签
            rotate: 0,
            fontSize: 13,
            fontWeight: 'bold',
            color: '#333',
            formatter: function(value) {
              return value;
            }
          }
        },
        yAxis: {
          type: 'category',
          data: simplifiedNumbers,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          splitArea: {
            show: true
          },
          axisLabel: {
            interval: 4,  // 每5个显示一个标签
            fontSize: 13,
            fontWeight: 'bold',
            color: '#333',
            formatter: function(value) {
              return value;
            }
          }
        },
        visualMap: {
          min: 0,
          max: Math.max(...heatmapData.map(item => item[2]), 1),
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
          }
        },
        series: [{
          name: '连号关系',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            formatter: function(params) {
              return params.data[2] > 0 ? params.data[2] : '';
            },
            fontSize: 10,
            fontWeight: 'bold',
            color: '#fff',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.3)'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            label: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          }
        }]
      };
      break;

    case 'chord':
      // 圆形关系图（替代和弦图）
      option = {
        title: {
          text: '特码连号分析 - 关系图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '连线粗细表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [{
          name: '特码连号分析',
          type: 'graph',
          layout: 'circular',
          circular: {
            rotateLabel: true
          },
          data: nodes,
          links: consecutiveData,
          categories: [
            { name: '红波' },
            { name: '蓝波' },
            { name: '绿波' }
          ],
          roam: true,
          label: {
            show: true,
            position: 'right',
            formatter: '{b}',
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333'
          },
          edgeSymbol: ['none', 'none'],
          edgeSymbolSize: [4, 10],
          edgeLabel: {
            show: false
          },
          lineStyle: {
            color: 'source',
            curveness: 0.3,
            width: 2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            },
            label: {
              fontSize: 16
            }
          }
        }]
      };
      break;

    case 'sankey':
      // 桑基图
      option = {
        title: {
          text: '特码连号分析 - 桑基图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '流量宽度表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          top: '15%',
          bottom: '10%',
          left: '10%',
          right: '10%',
          containLabel: true
        },
        series: [{
          name: '连号关系',
          type: 'sankey',
          top: 60,
          bottom: 25,
          left: 50,
          right: 50,
          data: sankeyNodes, // 已经过滤过，只包含有值的节点
          links: sankeyLinks,
          nodeWidth: 8, // 进一步减小节点宽度
          nodeGap: 6, // 进一步减小节点间距
          layoutIterations: 200, // 进一步增加布局迭代次数，使布局更加稳定
          draggable: true, // 允许拖动节点
          emphasis: {
            focus: 'adjacency'
          },
          label: {
            show: true,
            position: 'inside', // 将标签放在节点内部
            fontSize: 10, // 减小字体大小
            fontWeight: 'bold',
            color: '#fff', // 白色文字更容易在彩色节点上看清
            formatter: '{b}',
            distance: 0, // 标签与节点的距离
            textShadowColor: 'rgba(0, 0, 0, 0.5)', // 添加文字阴影
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
            curveness: 0.5
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: '#fff'
          },
          // 移除levels配置，使用节点自身的颜色设置
        }]
      };
      break;

    default:
      // 力导向图（默认）
      option = {
        title: {
          text: '特码连号分析 - 关系图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '节点大小表示出现频率，连线粗细表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        legend: {
          show: true,
          data: ['红波', '蓝波', '绿波'],
          bottom: 10,
          left: 'center',
          selectedMode: false,
          textStyle: {
            color: '#666'
          }
        },
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [{
          name: '特码连号分析',
          type: 'graph',
          layout: 'force',
          data: nodes.filter(node => node.value > 0), // 只显示有值的节点
          links: consecutiveData,
          categories: [
            { name: '红波' },
            { name: '蓝波' },
            { name: '绿波' }
          ],
          roam: true,
          draggable: true,
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}',
            fontSize: 12,
            fontWeight: 'bold',
            color: '#fff',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            borderRadius: 4,
            padding: [2, 4]
          },
          edgeSymbol: ['none', 'arrow'],
          edgeSymbolSize: [0, 8],
          lineStyle: {
            color: 'source',
            curveness: 0.3,
            width: 2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            },
            label: {
              fontSize: 14
            }
          },
          force: {
            repulsion: 150,
            gravity: 0.1,
            edgeLength: [50, 150],
            layoutAnimation: true
          }
        }]
      };
  }

  charts.value.consecutive.setOption(option);
}

const updateMissingAnalysisChart = (data) => {
  if (!charts.value.missingAnalysis) return;

  // 获取遗漏数据，兼容 key 为数字或字符串
  const missingData = data.missing?.current || {};
  // 统一获取方式，优先数字，再字符串
  const getMissingValue = (i) => missingData[i] ?? missingData[String(i)] ?? 0;

  // 添加调试日志
  console.log('更新遗漏分析图表，数据：', missingData);

  let option = {};
  let title = '特码遗漏分析';
  let subtitle = '';

  // 根据视图类型创建不同的图表
  switch (missingViewType.value) {
    case 'color': // 按波色分组
      // 创建波色分组数据
      const colorGroups = {
        '红波': { numbers: [], values: [], color: '#ff4d4f', avg: 0, max: 0, min: Infinity },
        '蓝波': { numbers: [], values: [], color: '#1890ff', avg: 0, max: 0, min: Infinity },
        '绿波': { numbers: [], values: [], color: '#52c41a', avg: 0, max: 0, min: Infinity }
      };

      // 按波色分组号码
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(getMissingValue(i));
        let colorGroup;

        if (GameRules2025.RED_NUMBERS.includes(i)) {
          colorGroup = colorGroups['红波'];
        } else if (GameRules2025.BLUE_NUMBERS.includes(i)) {
          colorGroup = colorGroups['蓝波'];
        } else {
          colorGroup = colorGroups['绿波'];
        }

        colorGroup.numbers.push(numStr);
        colorGroup.values.push(value);

        // 更新统计数据
        colorGroup.max = Math.max(colorGroup.max, value);
        if (value > 0) {
          colorGroup.min = Math.min(colorGroup.min, value);
        }
        colorGroup.avg += value;
      }

      // 计算平均值
      Object.keys(colorGroups).forEach(color => {
        const group = colorGroups[color];
        if (group.values.length > 0) {
          group.avg = Math.round(group.avg / group.values.length);
          if (group.min === Infinity) group.min = 0;
        }
      });

      subtitle = '按波色分组显示号码遗漏情况';

      // 创建图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const colorName = params[0].seriesName;
            const value = params[0].value;
            const index = params[0].dataIndex;
            const number = colorGroups[colorName].numbers[index];
            return `${colorName} - 号码: ${number}<br>遗漏期数: ${value}期`;
          }
        },
        legend: {
          data: Object.keys(colorGroups),
          bottom: 10,
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: Array.from({ length: 17 }, (_, i) => i + 1), // 最多17个号码
          name: '号码序号',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            fontSize: 10,
            formatter: function(value) {
              return value; // 简化显示，只显示序号
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: Object.keys(colorGroups).map(colorName => {
          const group = colorGroups[colorName];
          // 确保每个波色组至少有一些数据
          const validValues = group.values.filter(v => v !== undefined && v !== null);
          if (validValues.length === 0) {
            // 如果没有有效数据，添加一些默认数据以确保图表显示
            group.values = [0, 0, 0];
            group.numbers = ['N/A', 'N/A', 'N/A'];
          }

          return {
            name: colorName,
            type: 'bar',
            stack: 'total',
            barWidth: '40%',
            barGap: '10%',
            data: group.values,
            itemStyle: {
              color: group.color,
              borderRadius: [3, 3, 0, 0]
            },
            label: {
              show: showMissingTips.value,
              position: 'top',
              formatter: function(params) {
                const index = params.dataIndex;
                return group.numbers[index];
              },
              fontSize: 10,
              color: '#333'
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: '平均: {c}期',
                position: 'middle'
              }
            }
          };
        })
      };
      break;

    case 'zodiac': // 按生肖分组
      // 创建生肖分组数据
      const zodiacGroups = {};
      GameRules2025.ZODIAC_LIST.forEach(zodiac => {
        zodiacGroups[zodiac] = {
          numbers: [],
          values: [],
          color: zodiacColors[zodiac] || '#999',
          avg: 0,
          max: 0,
          min: Infinity,
          icon: zodiacIcons[zodiac] || ''
        };
      });

      // 按生肖分组号码
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(missingData[numStr] || 0);
        const zodiac = GameRules2025.getZodiac(i);
        const zodiacGroup = zodiacGroups[zodiac];

        zodiacGroup.numbers.push(numStr);
        zodiacGroup.values.push(value);

        // 更新统计数据
        zodiacGroup.max = Math.max(zodiacGroup.max, value);
        if (value > 0) {
          zodiacGroup.min = Math.min(zodiacGroup.min, value);
        }
        zodiacGroup.avg += value;
      }

      // 计算平均值
      Object.keys(zodiacGroups).forEach(zodiac => {
        const group = zodiacGroups[zodiac];
        if (group.values.length > 0) {
          group.avg = Math.round(group.avg / group.values.length);
          if (group.min === Infinity) group.min = 0;
        }
      });

      subtitle = '按生肖分组显示号码遗漏情况';

      // 创建图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const zodiac = params.name;
            const value = params.value;
            return `${zodiac} ${zodiacIcons[zodiac] || ''}<br>平均遗漏: ${value}期`;
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: GameRules2025.ZODIAC_LIST,
          name: '生肖',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            formatter: function(value) {
              return zodiacIcons[value] || value; // 只显示生肖图标或名称
            },
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        yAxis: {
          type: 'value',
          name: '平均遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: [
          {
            name: '平均遗漏',
            type: 'bar',
            barWidth: '50%',
            data: GameRules2025.ZODIAC_LIST.map(zodiac => {
              // 确保有有效值
              const avgValue = zodiacGroups[zodiac].avg || 0;

              return {
                name: zodiac,
                value: avgValue,
                itemStyle: {
                  color: zodiacGroups[zodiac].color,
                  opacity: 0.7
                }
              };
            }),
            label: {
              show: showMissingTips.value,
              position: 'top',
              formatter: '{c}期',
              fontSize: 12,
              color: '#333'
            }
          }
        ]
      };
      break;

    default: // 按号码排序（默认）
      // 创建柱状图数据
      const barData = [];
      const xAxisData = [];
      const colorData = [];

      // 按号码排序
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(missingData[numStr] || 0);

        // 获取号码对应的波色
        let color = '#999999';
        if (GameRules2025.RED_NUMBERS.includes(i)) color = '#ff4d4f';
        if (GameRules2025.BLUE_NUMBERS.includes(i)) color = '#1890ff';
        if (GameRules2025.GREEN_NUMBERS.includes(i)) color = '#52c41a';

        barData.push(value);
        xAxisData.push(numStr);
        colorData.push(color);
      }

      subtitle = '各号码自上次开出后的遗漏期数';

      // 设置图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const index = params[0].dataIndex;
            const num = xAxisData[index];
            const value = params[0].value;
            return `号码: ${num}<br>遗漏期数: ${value}期`;
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            rotate: 0, // 不旋转标签
            fontSize: 12,
            formatter: function(value) {
              // 简化显示，只显示号码
              return value;
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: [{
          name: '遗漏期数',
          type: 'bar',
          data: barData && barData.length > 0 ? barData : [0, 0, 0], // 确保至少有一些数据
          itemStyle: {
            color: function(params) {
              return colorData && colorData.length > 0 ? colorData[params.dataIndex] : '#1890ff';
            },
            borderRadius: [3, 3, 0, 0]
          },
          label: {
            show: showMissingTips.value,
            position: 'top',
            formatter: '{c}期',
            fontSize: 10,
            color: '#333'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          barWidth: '60%',
          barMaxWidth: 15,
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ],
            label: {
              formatter: '平均: {c}期',
              position: 'middle'
            }
          }
        }]
      };
  }

  // 美化：如数据全为0，则显示水印或空态提示
if (
  (missingViewType.value === 'number' && (!option.series[0].data || option.series[0].data.every(v => v === 0))) ||
  (missingViewType.value === 'color' && option.series.every(s => s.data.every(v => v === 0))) ||
  (missingViewType.value === 'zodiac' && (!option.series[0].data || option.series[0].data.every(d => d.value === 0)))
) {
  option.graphic = [
    {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: '暂无数据',
        fontSize: 28,
        fontWeight: 'bold',
        fill: '#bbb',
        opacity: 0.7
      }
    }
  ];
}

// 美化：统一背景、圆角、阴影
option.backgroundColor = '#f8fafc';
option.grid = option.grid || {};
option.grid.borderRadius = 10;
option.grid.shadowColor = 'rgba(0,0,0,0.05)';
option.grid.shadowBlur = 8;

charts.value.missingAnalysis.setOption(option, true);
}

// 修改组合分析相关的响应式变量
const selectedCombinations = ref(['wuxing', 'oddEven', 'bigSmall'])
const combinationCharts = ref({})

// 更新组合分析图表的函数
const updateCombinationAnalysisCharts = (data) => {
  // 清除旧的图表实例
  Object.values(charts.value.combinationAnalysis || {}).forEach(chart => {
    if (chart) {
      chart.dispose()
    }
  })
  charts.value.combinationAnalysis = {}

  // 添加调试日志
  console.log('Updating combination charts with data:', data)

  // 为每个选中的类型创建新的图表
  nextTick(() => {
    selectedCombinations.value.forEach(type => {
      if (combinationCharts.value[type]) {
        const chart = echarts.init(combinationCharts.value[type])
        charts.value.combinationAnalysis[type] = chart

        let combinations = {}
        let title = ''
        let colors = []

        switch (type) {
          case 'wuxing':
            combinations = data.wuxingFrequency || {}
            title = '特码五行分布'
            colors = ['#FFD700', '#90EE90', '#87CEEB', '#FF6B6B', '#DEB887']
            break
          case 'domestic':
            combinations = {
              '家禽': data.attributes?.['家禽'] || 0,
              '野兽': data.attributes?.['野兽'] || 0
            }
            title = '特码家野分布'
            colors = ['#95de64', '#ff7875']
            break
          case 'oddEven':
            combinations = {
              '单': data.attributes?.['单'] || 0,
              '双': data.attributes?.['双'] || 0
            }
            title = '特码单双分布'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'bigSmall':
            combinations = {
              '大': data.attributes?.['大'] || 0,
              '小': data.attributes?.['小'] || 0
            }
            title = '特码大小分布'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'tailOddEven':
            combinations = {
              '尾单': data.attributes?.['尾单'] || 0,
              '尾双': data.attributes?.['尾双'] || 0
            }
            title = '特码尾数单双'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'tailBigSmall':
            combinations = {
              '尾大': data.attributes?.['尾大'] || 0,
              '尾小': data.attributes?.['尾小'] || 0
            }
            title = '特码尾数大小'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'sumOddEven':
            combinations = {
              '合单': data.attributes?.['合单'] || 0,
              '合双': data.attributes?.['合双'] || 0
            }
            title = '特码合数单双'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'sumBigSmall':
            combinations = {
              '合大': data.attributes?.['合大'] || 0,
              '合小': data.attributes?.['合小'] || 0
            }
            title = '特码合数大小'
            colors = ['#ff4d4f', '#1890ff']
            break
        }

        // 添加调试日志
        console.log(`${title} data:`, combinations)

        const total = Object.values(combinations).reduce((a, b) => a + b, 0)
        const option = {
          title: {
            text: title,
            subtext: `总计：${total}期`,
            left: 'center',
            top: 10,
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333'
            },
            subtextStyle: {
              fontSize: 12,
              color: '#666',
              align: 'center',
              padding: [5, 0, 0, 0]  // 增加上边距，避免被遮挡
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
              return `${params.name}<br/>出现次数：${params.value}次<br/>占比：${percentage}%`
            },
            backgroundColor: 'rgba(255,255,255,0.9)',
            borderColor: '#ccc',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            }
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          series: [{
            name: title,
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: (params) => {
                const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
                return `${params.name}\n${params.value}次\n(${percentage}%)`
              },
              fontSize: 12,
              color: '#333',
              fontWeight: 'bold'
            },
            labelLine: {
              length: 15,
              length2: 10,
              smooth: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0,0,0,0.5)'
              }
            },
            data: Object.entries(combinations).map(([key, value], index) => ({
              name: key,
              value: value,
              itemStyle: {
                color: colors[index],
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.2)'
              }
            })),
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicInOut'
          }]
        }

        chart.setOption(option)
      }
    })
  })
}

// 获取号码近期走势
const getNumberTrend = (number) => {
  // 获取最近10期的走势数据
  const trendLength = 10
  const result = new Array(trendLength).fill(0)

  // 如果有历史数据，则根据历史数据生成走势
  if (basicStats.value?.drawHistory && basicStats.value.drawHistory.length > 0) {
    // 使用drawHistory数据
    const recentDraws = basicStats.value.drawHistory.slice(0, trendLength)

    // 遍历最近的开奖结果
    recentDraws.forEach((draw, index) => {
      if (draw && parseInt(draw.special_number) === parseInt(number)) {
        result[index] = 1 // 1表示该期出现了这个号码
      }
    })
  } else if (basicStats.value?.recentDraws && basicStats.value.recentDraws.length > 0) {
    // 兼容旧版数据结构
    const recentDraws = basicStats.value.recentDraws.slice(0, trendLength)

    // 遍历最近的开奖结果
    recentDraws.forEach((draw, index) => {
      if (draw && parseInt(draw.special_number) === parseInt(number)) {
        result[index] = 1 // 1表示该期出现了这个号码
      }
    })
  } else {
    // 没有历史数据时，生成随机走势（仅用于演示）
    for (let i = 0; i < trendLength; i++) {
      // 随机生成0或1，模拟号码是否出现
      result[i] = Math.random() > 0.8 ? 1 : 0
    }
  }

  return result
}

// 计算号码热度指数 (0-100)
const getHotIndex = (number) => {
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0

  // 计算热度指数的逻辑:
  // 1. 出现次数越多，热度越高
  // 2. 当前遗漏越小，热度越高
  // 3. 最大遗漏越大，热度越低

  // 获取所有号码的出现次数、当前遗漏和最大遗漏
  const allNumbers = Array.from({ length: 49 }, (_, i) => i + 1)
  const allCounts = allNumbers.map(n => parseInt(basicStats.value?.numberFrequency?.[n.toString()]) || 0)
  const allMissingCounts = allNumbers.map(n => parseInt(basicStats.value?.missing?.current?.[n.toString()]) || 0)

  // 计算最大值和最小值，用于归一化
  const maxCount = Math.max(...allCounts, 1)
  const maxMissingCount = Math.max(...allMissingCounts, 1)

  // 归一化各指标 (0-1范围)
  const normalizedCount = count / maxCount
  const normalizedMissing = 1 - (missingCount / maxMissingCount) // 遗漏越小，值越大

  // 计算综合热度指数 (0-100)
  const hotIndex = Math.round((normalizedCount * 0.6 + normalizedMissing * 0.4) * 100)

  return Math.min(Math.max(hotIndex, 0), 100) // 确保在0-100范围内
}

// 获取热度指数的颜色
const getHotIndexColor = (number) => {
  const hotIndex = getHotIndex(number)

  // 根据热度指数返回不同的颜色
  if (hotIndex >= 80) {
    return '#ff4d4f' // 热号 - 红色
  } else if (hotIndex >= 60) {
    return '#faad14' // 温号 - 橙色
  } else if (hotIndex >= 40) {
    return '#52c41a' // 平号 - 绿色
  } else if (hotIndex >= 20) {
    return '#1890ff' // 冷号 - 蓝色
  } else {
    return '#8c8c8c' // 极冷号 - 灰色
  }
}

// 显示号码走势图
const showNumberTrend = (number) => {
  // 创建一个临时DOM元素作为图表容器
  const chartContainer = document.createElement('div')
  chartContainer.style.width = '100%'
  chartContainer.style.height = '400px'

  // 准备数据
  const historyData = []
  const periodData = []

  // 如果有历史数据，则使用历史数据
  if (basicStats.value?.drawHistory && basicStats.value.drawHistory.length > 0) {
    // 获取最近30期的数据
    const recentDraws = basicStats.value.drawHistory.slice(0, 30).reverse()

    recentDraws.forEach(draw => {
      if (draw) {
        const isHit = draw.special_number === number
        historyData.push(isHit ? number : '-')
        periodData.push(draw.expect)
      }
    })
  } else {
    // 没有历史数据时使用模拟数据
    for (let i = 1; i <= 30; i++) {
      const isHit = Math.random() > 0.8 // 模拟20%的命中率
      historyData.push(isHit ? number : '-')
      periodData.push(`模拟期${i}`)
    }
  }

  // 使用简单的HTML内容而不是h函数
  const htmlContent = `
    <div style="width: 100%; height: 500px; padding: 10px;">
      <div id="trendChartContainer" style="width: 100%; height: 100%;"></div>
    </div>
  `

  // 显示对话框
  ElMessageBox.alert(
    htmlContent,
    `号码 ${number} 走势分析`,
    {
      dangerouslyUseHTMLString: true,
      customClass: 'number-trend-dialog',
      confirmButtonText: '关闭',
      callback: () => {
        // 清理图表实例
        if (window.tempTrendChart) {
          window.tempTrendChart.dispose()
          window.tempTrendChart = null
        }
      }
    }
  )

  // 在下一个tick初始化图表
  nextTick(() => {
    // 延迟初始化图表，确保DOM已经渲染完成并有宽高
    setTimeout(() => {
      const chartDom = document.getElementById('trendChartContainer')
      if (!chartDom) return

      // 初始化图表
      const chart = echarts.init(chartDom)
      window.tempTrendChart = chart // 保存图表实例以便后续清理

    // 设置图表选项
    const option = {
      title: {
        text: `号码 ${number} 近期走势图`,
        subtext: '显示最近30期开奖情况',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333'
        },
        subtextStyle: {
          fontSize: 12,
          color: '#999'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        },
        formatter: function(params) {
          const periodIndex = params[0].dataIndex
          const period = periodData[periodIndex]
          const value = historyData[periodIndex]
          const color = GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                        GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a'

          return `<div style="padding: 5px;">
            <div style="margin-bottom: 5px;"><strong>期号:</strong> ${period}</div>
            <div style="display: flex; align-items: center;">
              <strong>特码:</strong>
              <span style="margin-left: 5px; color: ${color}; font-weight: bold;">
                ${value === '-' ? '未开出' : value}
              </span>
            </div>
          </div>`
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.05)'
          }
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: periodData,
        name: '期号',
        nameLocation: 'middle',
        nameGap: 35,
        nameTextStyle: {
          fontWeight: 'bold',
          fontSize: 14,
          color: '#666'
        },
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisTick: {
          alignWithLabel: true,
          length: 5,
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          interval: 4,
          rotate: 45,
          fontSize: 11,
          color: '#666',
          margin: 12
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        min: 1,
        max: 49,
        interval: 6,
        name: '号码',
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontWeight: 'bold',
          fontSize: 14,
          color: '#666'
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#ddd'
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee'
          }
        }
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          height: 20,
          bottom: 5,
          borderColor: 'transparent',
          backgroundColor: '#f5f5f5',
          fillerColor: 'rgba(24, 144, 255, 0.2)',
          handleStyle: {
            color: '#1890ff',
            borderColor: '#1890ff'
          },
          textStyle: {
            color: '#999'
          }
        }
      ],
      series: [
        {
          name: '特码',
          type: 'line',
          data: historyData.map(v => v === '-' ? null : v),
          connectNulls: false,
          smooth: true,
          symbol: 'circle',
          symbolSize: function(value) {
            return value === null ? 0 : 14;  // 只有有值的点才显示
          },
          itemStyle: {
            color: GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                  GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a',
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(0, 0, 0, 0.2)',
            shadowBlur: 5
          },
          lineStyle: {
            width: 3,
            type: 'solid',
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                       GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a'
              }, {
                offset: 1,
                color: GameRules2025.getColor(number) === '红波' ? 'rgba(255, 77, 79, 0.3)' :
                       GameRules2025.getColor(number) === '蓝波' ? 'rgba(24, 144, 255, 0.3)' : 'rgba(82, 196, 26, 0.3)'
              }]
            },
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: GameRules2025.getColor(number) === '红波' ? 'rgba(255, 77, 79, 0.3)' :
                       GameRules2025.getColor(number) === '蓝波' ? 'rgba(24, 144, 255, 0.3)' : 'rgba(82, 196, 26, 0.3)'
              }, {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)'
              }]
            }
          },
          markPoint: {
            symbol: 'pin',
            symbolSize: 40,
            itemStyle: {
              color: GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                    GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a'
            },
            label: {
              color: '#fff',
              fontSize: 12,
              fontWeight: 'bold'
            },
            data: [
              { type: 'max', name: '最高值' },
              { type: 'min', name: '最低值' }
            ]
          },
          markLine: {
            symbol: ['none', 'none'],
            lineStyle: {
              color: '#999',
              type: 'dashed',
              width: 1
            },
            label: {
              position: 'middle',
              formatter: '{b}: {c}',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              padding: [3, 5],
              borderRadius: 3,
              color: '#666'
            },
            data: [
              {
                type: 'average',
                name: '平均值',
                lineStyle: {
                  color: '#faad14'
                }
              },
              {
                yAxis: number,
                name: '当前号码',
                lineStyle: {
                  color: GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                         GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a',
                  type: 'solid',
                  width: 1.5
                }
              }
            ]
          },
          emphasis: {
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 10
            }
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            color: GameRules2025.getColor(number) === '红波' ? '#ff4d4f' :
                  GameRules2025.getColor(number) === '蓝波' ? '#1890ff' : '#52c41a',
            fontSize: 12,
            fontWeight: 'bold',
            formatter: function(params) {
              return params.value === null ? '' : params.value;
            }
          }
        }
      ]
    }

    // 设置图表
    chart.setOption(option)

    // 监听窗口大小变化，调整图表大小
    const resizeHandler = function() {
      if (window.tempTrendChart) {
        window.tempTrendChart.resize()
      }
    }

    window.addEventListener('resize', resizeHandler)

    // 在对话框关闭时移除事件监听器
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.removedNodes.length > 0) {
          if (Array.from(mutation.removedNodes).some(node =>
            node.classList && node.classList.contains('number-trend-dialog'))) {
            window.removeEventListener('resize', resizeHandler)
            observer.disconnect()
          }
        }
      }
    })

    observer.observe(document.body, { childList: true, subtree: true })
    }, 300) // 延迟300ms确保DOM已完全渲染
  })
}

// 显示号码详情的方法
const showNumberDetail = (number) => {
  const numStr = number.toString()
  const numberData = basicStats.value?.numberFrequency || {}
  const count = parseInt(numberData[numStr]) || 0
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0
  const zodiac = GameRules2025.getZodiac(number)
  const color = GameRules2025.getColor(number)
  const element = GameRules2025.getWuxing(number)

  // 计算出现概率
  const totalDraws = Object.values(numberData).reduce((sum, val) => sum + (parseInt(val) || 0), 0)
  const probability = totalDraws > 0 ? ((count / totalDraws) * 100).toFixed(2) + '%' : '0%'

  // 计算热度指数
  const hotIndex = getHotIndex(number)
  const hotLevel = hotIndex >= 80 ? '热号' :
                  hotIndex >= 60 ? '温号' :
                  hotIndex >= 40 ? '平号' :
                  hotIndex >= 20 ? '冷号' : '极冷号'

  // 计算号码属性
  const isOdd = number % 2 === 1
  const isBig = number > 24
  const tailNumber = number % 10
  const isTailOdd = tailNumber % 2 === 1
  const isTailBig = tailNumber >= 5

  // 计算合数
  const sumDigits = number < 10 ? number : Math.floor(number / 10) + (number % 10)
  const isSumOdd = sumDigits % 2 === 1
  const isSumBig = sumDigits >= 7

  // 显示详情对话框
  ElMessageBox.alert(
    `<div class="number-detail">
      <h3>号码 ${number} 详细信息</h3>
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="detail-item"><span class="label">出现次数:</span> <span class="value">${count} 次</span></div>
        <div class="detail-item"><span class="label">出现概率:</span> <span class="value">${probability}</span></div>
        <div class="detail-item"><span class="label">当前遗漏:</span> <span class="value">${missingCount} 期</span></div>
        <div class="detail-item"><span class="label">最大遗漏:</span> <span class="value">${maxMissing} 期</span></div>
        <div class="detail-item"><span class="label">热度指数:</span> <span class="value" style="color: ${getHotIndexColor(number)}">${hotIndex} (${hotLevel})</span></div>
      </div>

      <div class="detail-section">
        <h4>属性分析</h4>
        <div class="detail-item"><span class="label">生肖:</span> <span class="value">${zodiacIcons[zodiac]} ${zodiac}</span></div>
        <div class="detail-item"><span class="label">波色:</span> <span class="value" style="color: ${color === '红波' ? '#ff4d4f' : color === '蓝波' ? '#1890ff' : '#52c41a'}">${color}</span></div>
        <div class="detail-item"><span class="label">五行:</span> <span class="value" style="color: ${getWuxingColor(element)}">${element}</span></div>
        <div class="detail-item"><span class="label">单双:</span> <span class="value">${isOdd ? '单数' : '双数'}</span></div>
        <div class="detail-item"><span class="label">大小:</span> <span class="value">${isBig ? '大数' : '小数'}</span></div>
      </div>

      <div class="detail-section">
        <h4>尾数分析</h4>
        <div class="detail-item"><span class="label">尾数:</span> <span class="value">${tailNumber}</span></div>
        <div class="detail-item"><span class="label">尾数单双:</span> <span class="value">${isTailOdd ? '尾单' : '尾双'}</span></div>
        <div class="detail-item"><span class="label">尾数大小:</span> <span class="value">${isTailBig ? '尾大' : '尾小'}</span></div>
      </div>

      <div class="detail-section">
        <h4>合数分析</h4>
        <div class="detail-item"><span class="label">合数:</span> <span class="value">${sumDigits}</span></div>
        <div class="detail-item"><span class="label">合数单双:</span> <span class="value">${isSumOdd ? '合单' : '合双'}</span></div>
        <div class="detail-item"><span class="label">合数大小:</span> <span class="value">${isSumBig ? '合大' : '合小'}</span></div>
      </div>
    </div>`,
    '号码详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
      customClass: 'number-detail-dialog'
    }
  )
}

// 过滤和排序后的数据
const filteredAndSortedData = computed(() => {
  let data = advancedAnalysisData.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    data = data.filter(item => {
      return item.number.toString().includes(query) ||
             item.zodiac.toLowerCase().includes(query) ||
             item.color.toLowerCase().includes(query) ||
             item.element.toLowerCase().includes(query) ||
             item.oddEven.toLowerCase().includes(query) ||
             item.bigSmall.toLowerCase().includes(query)
    })
  }

  // 类型过滤
  if (filterType.value) {
    switch (filterType.value) {
      case 'hot':
        data = data.filter(item => item.hotIndex >= 60)
        break
      case 'cold':
        data = data.filter(item => item.hotIndex < 40)
        break
      case 'odd':
        data = data.filter(item => item.oddEven === '单')
        break
      case 'even':
        data = data.filter(item => item.oddEven === '双')
        break
      case 'big':
        data = data.filter(item => item.bigSmall === '大')
        break
      case 'small':
        data = data.filter(item => item.bigSmall === '小')
        break
    }
  }

  // 排序
  if (sortField.value) {
    data = [...data].sort((a, b) => {
      let aValue = a[sortField.value]
      let bValue = b[sortField.value]

      // 对于字符串类型，转换为小写进行比较
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder.value === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  return data
})

// 计算高级分析数据
const advancedAnalysisData = computed(() => {
  const numbers = Array.from({ length: 49 }, (_, i) => i + 1)
  return numbers.map(number => {
    const numStr = number.toString()
    // 只使用 numberFrequency 字段
    const numberData = basicStats.value?.numberFrequency || {}

    // 计算号码属性
    const isOdd = number % 2 === 1
    const isBig = number > 24
    const tailNumber = number % 10
    const isTailOdd = tailNumber % 2 === 1
    const isTailBig = tailNumber >= 5

    // 计算合数
    const sumDigits = number < 10 ? number : Math.floor(number / 10) + (number % 10)
    const isSumOdd = sumDigits % 2 === 1
    const isSumBig = sumDigits >= 7

    // 计算热度指数
    const hotIndex = getHotIndex(number)

    return {
      number: number,
      count: parseInt(numberData[numStr]) || 0,
      missingCount: parseInt(basicStats.value?.missing?.current?.[numStr]) || 0,
      maxMissing: parseInt(basicStats.value?.missing?.max?.[numStr]) || 0,
      zodiac: GameRules2025.getZodiac(number),
      color: GameRules2025.getColor(number),
      element: GameRules2025.getWuxing(number),
      hotIndex: hotIndex,
      oddEven: isOdd ? '单' : '双',
      bigSmall: isBig ? '大' : '小',
      tailNumber: tailNumber,
      tailOddEven: isTailOdd ? '尾单' : '尾双',
      tailBigSmall: isTailBig ? '尾大' : '尾小',
      sumDigits: sumDigits,
      sumOddEven: isSumOdd ? '合单' : '合双',
      sumBigSmall: isSumBig ? '合大' : '合小'
    }
  })
})

// 监听图表类型变化
watch([
  numberFrequencyType,
  colorFrequencyIs3D,
  showTailLabels,
  showHeadLabels,
  zodiacChartType,
  showMissingTips,
  missingViewType, // 添加遗漏分析视图类型
  selectedCombinations,
  consecutiveChartType  // 添加连号分析图表类型
], () => {
  if (basicStats.value) {
    // 只使用 numberFrequency 字段
    updateNumberFrequencyChart(basicStats.value.numberFrequency || {})
    updateColorFrequencyChart(basicStats.value.colorFrequency || {})
    updateTailFrequencyChart(basicStats.value.tailFrequency || {})
    updateHeadFrequencyChart(basicStats.value.headFrequency || {})
    updateZodiacFrequencyChart(basicStats.value.zodiacFrequency || {})
    updateWuxingChart(basicStats.value.wuxingFrequency || {})
    updateConsecutiveChart(basicStats.value)
    updateMissingAnalysisChart(basicStats.value)
    updateCombinationAnalysisCharts(basicStats.value)
  }
})

// 监听窗口大小变化
const handleResize = () => {
  nextTick(() => {
    try {
      Object.entries(charts.value).forEach(([key, chart]) => {
        if (chart && typeof chart.resize === 'function') {
          try {
            // 确保图表有基本配置
            if (!chart.getOption() || !chart.getOption().series || chart.getOption().series.length === 0) {
              console.warn(`Chart ${key} has no valid option, setting default option`);
              // 根据图表类型设置默认配置
              let defaultType = 'bar';
              if (key === 'colorFrequency' || key === 'wuxing') {
                defaultType = 'pie';
              } else if (key === 'consecutive') {
                defaultType = 'line';
              } else if (key === 'missingAnalysis') {
                defaultType = 'heatmap';
              }

              chart.setOption({
                series: [{
                  type: defaultType,
                  data: []
                }]
              });
            }

            // 调整图表大小
            chart.resize();
            console.log(`Resized chart: ${key}`);
          } catch (e) {
            console.error(`Error resizing chart ${key}:`, e);
          }
        } else if (chart) {
          console.warn(`Chart ${key} does not have a resize function`);
        }
      });
    } catch (error) {
      console.error('Error in handleResize:', error);
    }
  });
}

// 获取最新一期信息
const fetchLatestDraw = async () => {
  try {
    const result = await store.fetchLatestDraw()
    if (result && result.data) {
      latestDraw.value = result.data
      console.log('Latest draw info:', latestDraw.value)

      // 如果没有设置结束期数，则使用最新一期的期号
      if (!endPeriod.value && latestDraw.value.expect) {
        const latestPeriodNumber = parseInt(latestDraw.value.expect.substring(4))
        if (!isNaN(latestPeriodNumber)) {
          endPeriod.value = latestPeriodNumber
          console.log(`设置结束期数为最新一期: ${endPeriod.value}`)
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch latest draw:', error)
  }
}

onMounted(() => {
  try {
    // 初始化图表
    initCharts()

    // 设置默认筛选条件：当前年份的第1期到最新期数
    const currentYear = new Date().getFullYear()
    selectedYear.value = currentYear
    startPeriod.value = 1  // 从第1期开始
    endPeriod.value = null // 到最新期数，将在获取最新一期信息后更新

    // 确保年份列表包含当前年份
    if (!years.value.includes(currentYear)) {
      years.value.push(currentYear)
      years.value.sort()
    }

    console.log(`设置默认统计范围: ${currentYear}年第1期至最新期数`)

    // 先获取最新一期信息
    fetchLatestDraw().then(() => {
      // 然后获取统计数据
      fetchStatistics()
    })

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('Error in onMounted:', error)
  }
})

// 组件卸载时的清理逻辑
onBeforeUnmount(() => {
  console.log('Component unmounting, cleaning up resources');

  // 移除事件监听
  window.removeEventListener('resize', handleResize);

  // 销毁所有图表实例
  Object.entries(charts.value).forEach(([key, chart]) => {
    if (chart) {
      try {
        console.log(`Disposing chart: ${key}`);
        // 检查dispose方法是否存在
        if (typeof chart.dispose === 'function') {
          chart.dispose();
        } else {
          console.warn(`Chart ${key} does not have a dispose method`);
        }
      } catch (error) {
        console.error(`Error disposing chart ${key}:`, error);
      }
    }
  });

  // 清空图表引用
  charts.value = {
    numberFrequency: null,
    colorFrequency: null,
    tailFrequency: null,
    headFrequency: null,
    zodiacFrequency: null,
    wuxing: null,
    consecutive: null,
    missingAnalysis: null,
    combinationAnalysis: null
  };

  console.log('All charts disposed and references cleared');
})

// 初始化所有图表
const initCharts = () => {
  nextTick(() => {
    try {
      // 确保在初始化图表前清空之前的实例
      Object.keys(charts.value).forEach(key => {
        if (charts.value[key]) {
          try {
            charts.value[key].dispose();
          } catch (e) {
            console.warn(`Failed to dispose chart ${key}:`, e);
          }
          charts.value[key] = null;
        }
      });

      // 初始化新的图表实例
      if (numberFrequencyChart.value) {
        charts.value.numberFrequency = echarts.init(numberFrequencyChart.value);
        // 设置一个基本的空配置，避免resize错误
        charts.value.numberFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 49 }, (_, i) => (i + 1).toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 49 }, () => 0)
          }]
        });
      }
      if (colorFrequencyChart.value) {
        charts.value.colorFrequency = echarts.init(colorFrequencyChart.value);
        charts.value.colorFrequency.setOption({
          series: [{
            type: 'pie',
            data: [
              { name: '红波', value: 0 },
              { name: '蓝波', value: 0 },
              { name: '绿波', value: 0 }
            ]
          }]
        });
      }
      if (tailFrequencyChart.value) {
        charts.value.tailFrequency = echarts.init(tailFrequencyChart.value);
        charts.value.tailFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 10 }, (_, i) => i.toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 10 }, () => 0)
          }]
        });
      }
      if (headFrequencyChart.value) {
        charts.value.headFrequency = echarts.init(headFrequencyChart.value);
        charts.value.headFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 5 }, (_, i) => i.toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 5 }, () => 0)
          }]
        });
      }
      if (zodiacFrequencyChart.value) {
        charts.value.zodiacFrequency = echarts.init(zodiacFrequencyChart.value);
        charts.value.zodiacFrequency.setOption({
          xAxis: {
            type: 'category',
            data: GameRules2025.ZODIAC_LIST
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: GameRules2025.ZODIAC_LIST.length }, () => 0)
          }]
        });
      }
      if (wuxingChart.value) {
        charts.value.wuxing = echarts.init(wuxingChart.value);
        charts.value.wuxing.setOption({
          series: [{
            type: 'pie',
            data: [
              { name: '金', value: 0 },
              { name: '木', value: 0 },
              { name: '水', value: 0 },
              { name: '火', value: 0 },
              { name: '土', value: 0 }
            ]
          }]
        });
      }
      if (consecutiveChart.value) {
        charts.value.consecutive = echarts.init(consecutiveChart.value);
        charts.value.consecutive.setOption({
          title: {
            text: '特码连号分析',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{
            type: 'graph',
            layout: 'force',
            data: Array.from({ length: 49 }, (_, i) => ({
              name: (i + 1).toString(),
              symbolSize: 10,
              value: 0
            })),
            links: [],
            roam: true,
            label: {
              show: true,
              position: 'inside',
              formatter: '{b}',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#fff'
            },
            force: {
              repulsion: 100,
              gravity: 0.1,
              layoutAnimation: true
            }
          }]
        });
      }
      if (missingAnalysisChart.value) {
        charts.value.missingAnalysis = echarts.init(missingAnalysisChart.value);
        charts.value.missingAnalysis.setOption({
          title: {
            text: '特码遗漏分析',
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 'bold'
            },
            subtext: '各号码自上次开出后的遗漏期数',
            subtextStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function(params) {
              const num = params[0].name;
              const value = params[0].value;
              return `号码: ${num}<br>遗漏期数: ${value}期`;
            }
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '10%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: Array.from({ length: 49 }, (_, i) => (i + 1).toString()),
            name: '号码',
            nameLocation: 'middle',
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            },
            axisLabel: {
              interval: 0,
              rotate: 0,
              fontSize: 12
            }
          },
          yAxis: {
            type: 'value',
            name: '遗漏期数',
            nameLocation: 'middle',
            nameGap: 40,
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            },
            axisLabel: {
              formatter: '{value}期'
            }
          },
          series: [{
            name: '遗漏期数',
            type: 'bar',
            data: Array.from({ length: 49 }, () => 0),
            itemStyle: {
              color: function(params) {
                const num = parseInt(params.name);
                if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f';
                if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff';
                return '#52c41a';
              },
              borderRadius: [3, 3, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}期',
              fontSize: 10,
              color: '#333'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: '平均: {c}期',
                position: 'middle'
              }
            }
          }]
        });
      }

      console.log('All charts initialized successfully');
    } catch (error) {
      console.error('Error initializing charts:', error)
    }
  })
}

// 修改图表更新函数
const updateCharts = (data) => {
  if (!data) {
    console.error('No data provided to updateCharts');
    return;
  }

  console.log('Updating charts with data:', data);

  nextTick(() => {
    try {
      // 确保每个图表实例存在并且有数据才更新
      if (charts.value.numberFrequency) {
        if (data.numberFrequency) {
          console.log('Updating number frequency chart with data:', data.numberFrequency);
          updateNumberFrequencyChart(data.numberFrequency);
        } else {
          console.warn('No number frequency data available');
          // 使用空对象更新图表，显示空状态
          updateNumberFrequencyChart({});
        }
      }

      if (charts.value.colorFrequency) {
        if (data.colorFrequency) {
          console.log('Updating color frequency chart with data:', data.colorFrequency);
          updateColorFrequencyChart(data.colorFrequency);
        } else {
          console.warn('No color frequency data available');
          updateColorFrequencyChart({ '红波': 0, '蓝波': 0, '绿波': 0 });
        }
      }

      if (charts.value.tailFrequency) {
        if (data.tailFrequency) {
          console.log('Updating tail frequency chart with data:', data.tailFrequency);
          updateTailFrequencyChart(data.tailFrequency);
        } else {
          console.warn('No tail frequency data available');
          updateTailFrequencyChart(Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])));
        }
      }

      if (charts.value.headFrequency) {
        if (data.headFrequency) {
          console.log('Updating head frequency chart with data:', data.headFrequency);
          updateHeadFrequencyChart(data.headFrequency);
        } else {
          console.warn('No head frequency data available');
          updateHeadFrequencyChart(Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])));
        }
      }

      if (charts.value.zodiacFrequency) {
        if (data.zodiacFrequency) {
          console.log('Updating zodiac frequency chart with data:', data.zodiacFrequency);
          updateZodiacFrequencyChart(data.zodiacFrequency);
        } else {
          console.warn('No zodiac frequency data available');
          updateZodiacFrequencyChart(Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])));
        }
      }

      if (charts.value.wuxing) {
        if (data.wuxingFrequency) {
          console.log('Updating wuxing chart with data:', data.wuxingFrequency);
          updateWuxingChart(data.wuxingFrequency);
        } else {
          console.warn('No wuxing frequency data available');
          updateWuxingChart({ '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 });
        }
      }

      if (charts.value.consecutive) {
        console.log('Updating consecutive chart with data');
        updateConsecutiveChart(data);
      }

      if (charts.value.missingAnalysis) {
        console.log('Updating missing analysis chart with data');
        updateMissingAnalysisChart(data);
      }
    } catch (error) {
      console.error('Error updating charts:', error);
      ElMessage.error('更新图表时发生错误: ' + error.message);
    }
  })
}

// 基础统计卡片数据
const basicStatsCards = ref([
  {
    title: '特码出现次数',
    value: computed(() => {
      console.log('Total count:', basicStats.value?.basicStats?.totalCount);
      return basicStats.value?.basicStats?.totalCount || 0;
    }),
    description: '统计期间总开奖次数',
    icon: 'DataLine',
    color: '#409EFF',
    tooltip: '在选定时间范围内的开奖总次数',
    hover: false
  },
  {
    title: '最热特码',
    value: computed(() => {
      console.log('Hot numbers:', basicStats.value?.basicStats?.hotNumbers);
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length === 0) return '-';

      // 获取第一个热门号码
      const firstNum = hotNumbers[0]?.number;
      return firstNum ? String(firstNum).padStart(2, '0') : '-';
    }),
    description: computed(() => {
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length === 0) return '暂无数据';

      // 获取热门号码的出现次数 (使用count字段而不是frequency)
      const count = hotNumbers[0]?.count;

      // 如果count是undefined，返回一个默认值
      if (count === undefined) return '数据加载中...';

      // 获取所有具有相同最大出现次数的号码
      const sameFreqNumbers = hotNumbers
        .filter(n => n.count === count)
        .map(n => String(n.number).padStart(2, '0'));

      // 如果只有一个号码，显示简单的出现次数
      if (sameFreqNumbers.length === 1) {
        return count > 0 ? `出现${count}次` : '未出现';
      }

      // 如果有多个号码，显示"等X个号码"
      return count > 0
        ? `${sameFreqNumbers.length}个号码各出现${count}次`
        : `${sameFreqNumbers.length}个号码均未出现`;
    }),
    icon: 'Histogram',
    color: '#F56C6C',
    tooltip: computed(() => {
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length <= 1) return '出现次数最多的特码号码';

      // 获取热门号码的出现次数 (使用count字段)
      const count = hotNumbers[0]?.count;

      // 获取所有具有相同最大出现次数的号码
      const sameFreqNumbers = hotNumbers
        .filter(n => n.count === count)
        .map(n => String(n.number).padStart(2, '0'));

      return `出现次数最多的特码号码: ${sameFreqNumbers.join(', ')}`;
    }),
    hover: false
  },
  {
    title: '最冷特码',
    value: computed(() => {
      console.log('Cold numbers:', basicStats.value?.basicStats?.coldNumbers);
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length === 0) return '-';

      // 获取第一个冷门号码
      const firstNum = coldNumbers[0]?.number;
      return firstNum ? String(firstNum).padStart(2, '0') : '-';
    }),
    description: computed(() => {
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length === 0) return '暂无数据';

      // 获取冷门号码的出现次数 (使用count字段)
      const count = coldNumbers[0]?.count;

      // 如果count是undefined，返回一个默认值
      if (count === undefined) return '数据加载中...';

      // 获取所有具有相同最小出现次数的号码
      const sameFreqNumbers = coldNumbers
        .filter(n => n.count === count)
        .map(n => String(n.number).padStart(2, '0'));

      // 如果只有一个号码，显示简单的出现次数
      if (sameFreqNumbers.length === 1) {
        return count > 0 ? `出现${count}次` : '未出现';
      }

      // 如果有多个号码，显示"等X个号码"
      return count > 0
        ? `${sameFreqNumbers.length}个号码各出现${count}次`
        : `${sameFreqNumbers.length}个号码均未出现`;
    }),
    icon: 'TrendCharts',
    color: '#67C23A',
    tooltip: computed(() => {
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length <= 1) return '出现次数最少的特码号码';

      // 获取冷门号码的出现次数 (使用count字段)
      const count = coldNumbers[0]?.count;

      // 获取所有具有相同最小出现次数的号码
      const sameFreqNumbers = coldNumbers
        .filter(n => n.count === count)
        .map(n => String(n.number).padStart(2, '0'));

      return `出现次数最少的特码号码: ${sameFreqNumbers.join(', ')}`;
    }),
    hover: false
  },
  {
    title: '平均间隔',
    value: computed(() => {
      const val = parseFloat(basicStats.value?.basicStats?.averageInterval);
      return !isNaN(val) && val > 0 ? Math.round(val) : '-';
    }),
    description: '期数',
    icon: 'PieChart',
    color: '#E6A23C',
    tooltip: '特码重复出现的平均间隔期数',
    hover: false
  }
])

const getNumberColor = (number) => {
  const num = parseInt(number)
  if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
  if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
  return '#52c41a'
}

const getWuxingColor = (wuxing) => {
  switch (wuxing) {
    case '金': return '#FFD700'
    case '木': return '#90EE90'
    case '水': return '#87CEEB'
    case '火': return '#FF6B6B'
    case '土': return '#DEB887'
    default: return '#999'
  }
}

// 这里已经有一个updateMissingAnalysisChart函数的声明，所以我们删除这个重复声明

const processStatistics = (data) => {
  console.log('Raw statistics data:', data)

  // 初始化波色频率数据
  const colorFrequency = {
    '红波': 0,
    '蓝波': 0,
    '绿波': 0
  }

  // 处理每个开奖号码
  data.forEach(draw => {
    if (draw.open_code) {
      const numbers = draw.open_code.split(',').map(n => parseInt(n.trim()))
      numbers.forEach(number => {
        const color = GameRules2025.getColor(number)
        if (color) {
          colorFrequency[color]++
        }
      })
    }
  })

  console.log('Color frequency data:', colorFrequency)

  // 更新图表数据
  const chartData = {
    title: {
      text: '波色统计',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [{
      name: '波色统计',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: '{b}: {c}\n{d}%'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      data: [
        { value: colorFrequency['红波'], name: '红波', itemStyle: { color: '#ff4d4f' } },
        { value: colorFrequency['蓝波'], name: '蓝波', itemStyle: { color: '#1890ff' } },
        { value: colorFrequency['绿波'], name: '绿波', itemStyle: { color: '#52c41a' } }
      ]
    }]
  }

  return chartData
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/statistics.css';
.statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  max-width: 1600px;
  margin: 0 auto;

  .filter-card {
    margin-bottom: 20px;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      align-items: flex-start;
    }
  }

  .mt-4 {
    margin-top: 16px;
  }

  .chart-card {
    transition: all 0.3s ease;
    margin: 0 -20px;
    padding: 0 20px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .chart-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }
    }

    .chart-container {
      height: 500px;
      width: 100%;
      min-width: 800px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
        pointer-events: none;
      }
    }
  }

  .stat-card {
    height: 160px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 20px;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          font-size: 30px;
          color: #fff;
        }
      }

      .stat-details {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-desc {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .period-inputs {
    display: flex;
    align-items: center;
    gap: 10px;

    .separator {
      color: #909399;
    }
  }

  .latest-draw-info {
    margin-left: auto;
    padding: 0 20px;

    .el-tag {
      font-size: 14px;
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #f0f9eb;
      border-color: #e1f3d8;
      color: #67c23a;
    }
  }

  .filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
  }

  .advanced-analysis {
    .el-table {
      margin: 20px 0;

      // 设置表格最大高度，确保可以显示所有数据
      :deep(.el-table__body-wrapper) {
        max-height: calc(100vh - 300px) !important;
        overflow-y: auto;
      }

      // 添加表格hover效果
      :deep(.el-table__row) {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f7fa !important;
          transform: translateY(-2px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
      }

      // 优化表头样式
      :deep(.el-table__header-wrapper) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: bold;
          position: sticky;
          top: 0;
          z-index: 2;

          .cell {
            padding: 12px 0;
          }
        }
      }

      // 优化单元格样式
      :deep(.el-table__cell) {
        padding: 8px 0;

        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      // 优化标签样式
      :deep(.el-tag) {
        width: 100%;
        text-align: center;
        border-radius: 4px;

        &.el-tag--danger {
          color: #fff;
          background-color: #ff4d4f;
          border-color: #ff4d4f;
        }

        &.el-tag--primary {
          color: #fff;
          background-color: #1890ff;
          border-color: #1890ff;
        }

        &.el-tag--success {
          color: #fff;
          background-color: #52c41a;
          border-color: #52c41a;
        }
      }

      // 添加斑马纹效果
      :deep(.el-table__row--striped) {
        background-color: #fafafa;
      }

      // 优化滚动条样式
      :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
      }

      :deep(.el-scrollbar__bar.is-vertical) {
        width: 6px;
      }

      :deep(.el-scrollbar__thumb) {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
      }
    }
  }

  .combination-charts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    padding: 24px;
    width: 100%;
    max-width: 1800px;
    margin: 0 auto;

    .chart-container {
      height: 360px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
        pointer-events: none;
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 8px;
  overflow: hidden;

  .el-card__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
}

:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-select-dropdown__item) {
  padding: 0 15px;
}
</style>