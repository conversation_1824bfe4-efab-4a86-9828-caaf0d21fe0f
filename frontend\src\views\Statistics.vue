<template>
  <div class="statistics-container">
    <!-- 时间范围筛选 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>统计条件</span>
        </div>
      </template>
      <el-form :inline="true" size="default" class="filter-form">
        <!-- 年份选择 -->
        <el-form-item label="年份" class="year-select">
          <el-select v-model="selectedYear" clearable placeholder="选择年份" style="width: 160px">
            <el-option v-for="year in years" :key="year" :label="year" :value="year" />
          </el-select>
        </el-form-item>

        <!-- 期数范围 -->
        <el-form-item label="期数范围" class="period-range">
          <div class="period-inputs">
            <el-input-number v-model="startPeriod" :min="1" :max="999" placeholder="起始期数" style="width: 130px"
              controls-position="right" />
            <span class="separator">至</span>
            <el-input-number v-model="endPeriod" :min="1" :max="999" placeholder="结束期数" style="width: 130px"
              controls-position="right" />
          </div>
        </el-form-item>

        <!-- 日期范围 -->
        <el-form-item label="日期范围" class="date-range">
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :shortcuts="dateShortcuts" value-format="YYYY-MM-DD" style="width: 380px"
            @change="handleDateChange" />
        </el-form-item>

        <el-form-item class="search-button">
          <el-button type="primary" :icon="Search" @click="fetchStatistics">
            统计
          </el-button>
        </el-form-item>

        <!-- 最新一期信息 -->
        <div class="latest-draw-info">
          <el-tag type="info" effect="plain" size="large" v-if="latestDraw">
            最新一期: {{ latestDraw.expect }} ({{ formatDate(latestDraw.draw_time) }})
          </el-tag>
        </div>
      </el-form>
    </el-card>

    <!-- 特码基础统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="6" v-for="(stat, index) in basicStatsCards" :key="index">
        <div class="enhanced-stat-card"
             :style="{ '--card-color': stat.color }"
             @mouseenter="stat.hover = true"
             @mouseleave="stat.hover = false">
          <div class="card-background"></div>
          <div class="card-content">
            <div class="card-header">
              <div class="card-title">{{ stat.title }}</div>
              <el-tooltip :content="typeof stat.tooltip === 'object' ? stat.tooltip.value : stat.tooltip" placement="top">
                <div class="info-icon">
                  <el-icon><InfoFilled /></el-icon>
                </div>
              </el-tooltip>
            </div>
            <div class="card-body">
              <div class="stat-icon-wrapper">
                <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                  <el-icon size="24">
                    <component :is="stat.icon" />
                  </el-icon>
                </div>
                <div class="icon-glow" :style="{ backgroundColor: stat.color }"></div>
              </div>
              <div class="stat-data">
                <div class="stat-value">
                  <el-tooltip
                    v-if="(typeof stat.value === 'object' ? stat.value.value : stat.value).toString().includes(',')"
                    :content="typeof stat.tooltip === 'object' ? stat.tooltip.value : stat.tooltip"
                    placement="top"
                    :show-after="500"
                    popper-class="number-tooltip"
                  >
                    <span
                      class="value-number"
                      :class="{
                        'multi-numbers': (typeof stat.value === 'object' ? stat.value.value : stat.value).toString().includes(','),
                        'has-ellipsis': (typeof stat.value === 'object' ? stat.value.value : stat.value).toString().includes('...')
                      }"
                    >
                      {{ typeof stat.value === 'object' ? stat.value.value : stat.value }}
                    </span>
                  </el-tooltip>
                  <span
                    v-else
                    class="value-number"
                    :class="{
                      'multi-numbers': (typeof stat.value === 'object' ? stat.value.value : stat.value).toString().includes(','),
                      'has-ellipsis': (typeof stat.value === 'object' ? stat.value.value : stat.value).toString().includes('...')
                    }"
                  >
                    {{ typeof stat.value === 'object' ? stat.value.value : stat.value }}
                  </span>
                  <span class="value-unit" v-if="stat.unit">{{ stat.unit }}</span>
                </div>
                <div class="stat-description">
                  {{ typeof stat.description === 'object' ? stat.description.value : stat.description }}
                </div>
              </div>
            </div>
          </div>
          <div class="card-decoration"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码出现频率</span>
              <div class="chart-controls">
                <el-radio-group v-model="numberFrequencyType" size="small">
                  <el-radio-button value="bar">柱状图</el-radio-button>
                  <el-radio-button value="line">折线图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="numberFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码波色统计</span>
              <div class="chart-controls">
                <el-switch v-model="colorFrequencyIs3D" active-text="3D" inactive-text="2D" size="small" />
              </div>
            </div>
          </template>
          <div ref="colorFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码尾数统计</span>
              <div class="chart-controls">
                <el-tooltip content="显示数值标签" placement="top">
                  <el-switch v-model="showTailLabels" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="tailFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 特码头数统计 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码头数统计</span>
              <div class="chart-controls">
                <el-tooltip content="显示数值标签" placement="top">
                  <el-switch v-model="showHeadLabels" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="headFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码生肖统计</span>
              <div class="chart-controls">
                <el-radio-group v-model="zodiacChartType" size="small">
                  <el-radio-button value="bar">柱状图</el-radio-button>
                  <el-radio-button value="radar">雷达图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="zodiacFrequencyChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>特码五行分布</span>
            </div>
          </template>
          <div ref="wuxingChart" style="height: 500px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 连号分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码连号分析</span>
              <div class="chart-controls">
                <el-tooltip :content="getChartTooltip(consecutiveChartType)" placement="top" :show-after="500">
                  <el-button
                    type="info"
                    size="small"
                    icon="QuestionFilled"
                    circle
                    @click="showChartGuide = true"
                    style="margin-right: 10px;"
                  ></el-button>
                </el-tooltip>
                <el-radio-group v-model="consecutiveChartType" size="small">
                  <el-radio-button value="heatmap">热力图</el-radio-button>
                  <el-radio-button value="graph">关系图</el-radio-button>
                  <el-radio-button value="chord">和弦图</el-radio-button>
                  <el-radio-button value="sankey">桑基图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <!-- 图表使用说明 -->
          <div class="chart-guide-banner" v-if="showChartInstructions">
            <el-alert
              :title="getChartInstructions(consecutiveChartType).title"
              :description="getChartInstructions(consecutiveChartType).description"
              type="info"
              show-icon
              :closable="true"
              @close="showChartInstructions = false"
            >
              <template #default>
                <div class="chart-instructions">
                  <h4>{{ getChartInstructions(consecutiveChartType).title }}</h4>
                  <p>{{ getChartInstructions(consecutiveChartType).description }}</p>
                  <ul>
                    <li v-for="tip in getChartInstructions(consecutiveChartType).tips" :key="tip">
                      {{ tip }}
                    </li>
                  </ul>
                </div>
              </template>
            </el-alert>
          </div>

          <div ref="consecutiveChart" style="height: 600px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 遗漏分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码遗漏分析</span>
              <div class="chart-controls">
                <el-radio-group v-model="missingViewType" size="small" style="margin-right: 15px;">
                  <el-radio-button value="number">按号码</el-radio-button>
                  <el-radio-button value="color">按波色</el-radio-button>
                  <el-radio-button value="zodiac">按生肖</el-radio-button>
                </el-radio-group>
                <el-tooltip content="显示遗漏提示" placement="top">
                  <el-switch v-model="showMissingTips" size="small" />
                </el-tooltip>
              </div>
            </div>
          </template>
          <div ref="missingAnalysisChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 组合分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码组合分析</span>
              <div class="chart-controls">
                <el-checkbox-group v-model="selectedCombinations" size="small">
                  <el-checkbox-button value="wuxing">五行</el-checkbox-button>
                  <el-checkbox-button value="domestic">家野</el-checkbox-button>
                  <el-checkbox-button value="oddEven">单双</el-checkbox-button>
                  <el-checkbox-button value="bigSmall">大小</el-checkbox-button>
                  <el-checkbox-button value="tailOddEven">尾数单双</el-checkbox-button>
                  <el-checkbox-button value="tailBigSmall">尾数大小</el-checkbox-button>
                  <el-checkbox-button value="sumOddEven">合数单双</el-checkbox-button>
                  <el-checkbox-button value="sumBigSmall">合数大小</el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </template>
          <div class="combination-charts">
            <template v-for="type in selectedCombinations" :key="type">
              <div :ref="el => combinationCharts[type] = el" class="chart-container"></div>
            </template>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 高级分析 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>特码综合分析</span>
              <div class="chart-controls">
                <el-checkbox-group v-model="advancedAnalysisTypes" size="small">
                  <el-checkbox-button value="number">号码</el-checkbox-button>
                  <el-checkbox-button value="zodiac">生肖</el-checkbox-button>
                  <el-checkbox-button value="color">波色</el-checkbox-button>
                  <el-checkbox-button value="element">五行</el-checkbox-button>
                  <el-checkbox-button value="oddEven">单双</el-checkbox-button>
                  <el-checkbox-button value="bigSmall">大小</el-checkbox-button>
                  <el-checkbox-button value="tail">尾数</el-checkbox-button>
                  <el-checkbox-button value="sum">合数</el-checkbox-button>
                </el-checkbox-group>
              </div>
            </div>
          </template>
          <div class="advanced-analysis">
            <!-- 优化后的筛选控制区 -->
            <div class="analysis-controls">
              <!-- 列显示控制 -->
              <div class="control-section">
                <label class="control-label">显示列：</label>
                <el-select v-model="columnFilter" placeholder="选择显示列" style="width: 160px;">
                  <el-option label="全部" value="all" />
                  <el-option label="基础信息" value="basic" />
                  <el-option label="号码属性" value="attributes" />
                  <el-option label="位置组合" value="position" />
                  <el-option label="数学特征" value="mathematical" />
                  <el-option label="波动趋势" value="trend" />
                  <el-option label="关联分析" value="relation" />
                  <el-option label="预测指标" value="prediction" />
                  <el-option label="特殊模式" value="pattern" />
                  <el-option label="综合评分" value="comprehensive" />
                </el-select>
              </div>

              <!-- 快速筛选标签 -->
              <div class="control-section">
                <label class="control-label">快速筛选：</label>
                <el-tag
                  v-for="tag in quickFilterTags"
                  :key="tag.value"
                  :type="filterType === tag.value ? 'primary' : 'info'"
                  :effect="filterType === tag.value ? 'dark' : 'plain'"
                  @click="toggleQuickFilter(tag.value)"
                  class="quick-filter-tag"
                >
                  {{ tag.label }}
                </el-tag>
              </div>

              <!-- 数据统计摘要 -->
              <div class="control-section">
                <el-statistic
                  :value="filteredAndSortedData.length"
                  title="显示数量"
                  suffix="个"
                  class="data-summary"
                />
              </div>
            </div>

            <!-- 优化后的表格控制区 -->
            <div class="table-controls">
              <div class="controls-row">
                <!-- 搜索框 -->
                <div class="control-item">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索号码、生肖、波色等"
                    prefix-icon="Search"
                    clearable
                    style="width: 280px;"
                    @input="handleSearchInput"
                  >
                    <template #suffix>
                      <el-tooltip content="支持搜索号码、生肖、波色、五行等" placement="top">
                        <el-icon><InfoFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </div>

                <!-- 排序控制 -->
                <div class="control-item">
                  <el-select v-model="sortField" placeholder="排序字段" style="width: 140px;">
                    <el-option label="号码" value="number" />
                    <el-option label="出现次数" value="count" />
                    <el-option label="当前遗漏" value="missingCount" />
                    <el-option label="最大遗漏" value="maxMissing" />
                    <el-option label="热度指数" value="hotIndex" />
                  </el-select>
                </div>

                <div class="control-item">
                  <el-select v-model="sortOrder" placeholder="排序方式" style="width: 100px;">
                    <el-option label="升序" value="asc">
                      <el-icon><Sort /></el-icon> 升序
                    </el-option>
                    <el-option label="降序" value="desc">
                      <el-icon><Sort /></el-icon> 降序
                    </el-option>
                  </el-select>
                </div>

                <!-- 操作按钮 -->
                <div class="control-item">
                  <el-button-group>
                    <el-button @click="resetFilters" :icon="Refresh">重置</el-button>
                    <el-button @click="exportData" :icon="Download">导出</el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
            <!-- 优化后的表格 -->
            <el-table
              :data="filteredAndSortedData"
              v-loading="tableLoading"
              element-loading-text="正在加载数据..."
              element-loading-spinner="el-icon-loading"
              style="width: 100%"
              :max-height="800"
              stripe
              border
              highlight-current-row
              :empty-text="getEmptyText()"
              @sort-change="handleSortChange"
              @row-click="handleRowClick"
            >
              <!-- 固定显示的列 -->
              <el-table-column prop="number" label="号码" width="60" align="center" fixed>
                <template #default="scope">
                  <span class="number-cell" :style="{ color: getNumberColor(scope.row.number) }">{{ scope.row.number }}</span>
                </template>
              </el-table-column>

              <!-- 基本统计列 - 始终显示 -->
              <el-table-column prop="count" label="出现次数" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="count-cell">{{ scope.row.count }} <span class="unit">次</span></span>
                </template>
              </el-table-column>
              <el-table-column prop="missingCount" label="当前遗漏" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="missing-cell">{{ scope.row.missingCount }} <span class="unit">期</span></span>
                </template>
              </el-table-column>
              <el-table-column prop="maxMissing" label="最大遗漏" width="90" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'number'">
                <template #default="scope">
                  <span class="max-missing-cell">{{ scope.row.maxMissing }} <span class="unit">期</span></span>
                </template>
              </el-table-column>

              <!-- 生肖列 -->
              <el-table-column prop="zodiac" label="生肖" width="90" align="center" v-if="columnFilter === 'all' || columnFilter === 'zodiac'">
                <template #default="scope">
                  <div class="zodiac-cell">
                    <span class="zodiac-icon">{{ zodiacIcons[scope.row.zodiac] }}</span>
                    <span class="zodiac-text">{{ scope.row.zodiac }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 波色列 -->
              <el-table-column prop="color" label="波色" width="80" align="center" v-if="columnFilter === 'all' || columnFilter === 'color'">
                <template #default="scope">
                  <el-tag :type="scope.row.color === '红波' ? 'danger' : scope.row.color === '蓝波' ? 'primary' : 'success'"
                    effect="light" size="small" class="color-tag">
                    {{ scope.row.color }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 五行列 -->
              <el-table-column prop="element" label="五行" width="80" align="center" v-if="columnFilter === 'all' || columnFilter === 'element'">
                <template #default="scope">
                  <el-tag :style="{
                    backgroundColor: getWuxingColor(scope.row.element),
                    borderColor: getWuxingColor(scope.row.element),
                    color: '#fff'
                  }" size="small" class="element-tag">
                    {{ scope.row.element }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 单双列 -->
              <el-table-column prop="oddEven" label="单双" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'oddEven'">
                <template #default="scope">
                  <el-tag :type="scope.row.oddEven === '单' ? 'danger' : 'primary'" effect="plain" size="small">
                    {{ scope.row.oddEven }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 大小列 -->
              <el-table-column prop="bigSmall" label="大小" width="80" align="center" sortable v-if="columnFilter === 'all' || columnFilter === 'bigSmall'">
                <template #default="scope">
                  <el-tag :type="scope.row.bigSmall === '大' ? 'danger' : 'primary'" effect="plain" size="small">
                    {{ scope.row.bigSmall }}
                  </el-tag>
                </template>
              </el-table-column>



              <!-- 优化的热度指数列 -->
              <el-table-column prop="hotIndex" label="热度指数" width="120" align="center" sortable>
                <template #default="scope">
                  <div class="hot-index-container">
                    <el-progress
                      :percentage="getHotIndex(scope.row.number)"
                      :color="getHotIndexColor(scope.row.number)"
                      :stroke-width="8"
                      :show-text="false"
                      class="hot-index-progress"
                    />
                    <div class="hot-index-info">
                      <span class="hot-index-value" :style="{ color: getHotIndexColor(scope.row.number) }">
                        {{ getHotIndex(scope.row.number) }}
                      </span>
                      <span class="hot-index-label">{{ getHotIndexLabel(scope.row.number) }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 新增：开出概率列 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'probability'"
                prop="probability" label="开出概率" width="100" align="center" sortable>
                <template #default="scope">
                  <div class="probability-container">
                    <span class="probability-value">{{ getProbability(scope.row.number) }}%</span>
                    <div class="probability-bar">
                      <div class="probability-fill"
                           :style="{ width: getProbability(scope.row.number) + '%',
                                    backgroundColor: getProbabilityColor(scope.row.number) }">
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 新增：回补指数列 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'rebound'"
                prop="reboundIndex" label="回补指数" width="100" align="center" sortable>
                <template #default="scope">
                  <div class="rebound-container">
                    <el-tag :type="getReboundTagType(scope.row.number)" size="small">
                      {{ getReboundIndex(scope.row.number) }}
                    </el-tag>
                    <div class="rebound-desc">{{ getReboundDesc(scope.row.number) }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 新增：稳定性指数列 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'comprehensive'"
                prop="stabilityIndex" label="稳定性" width="100" align="center" sortable>
                <template #default="scope">
                  <div class="stability-container">
                    <el-rate
                      :model-value="getStabilityRating(scope.row.number)"
                      disabled
                      show-score
                      score-template="{value}"
                      :max="5"
                      size="small"
                    />
                  </div>
                </template>
              </el-table-column>

              <!-- 位置组合分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'position'"
                prop="positionAnalysis" label="位置分析" width="120" align="center" sortable>
                <template #default="scope">
                  <div class="position-analysis">
                    <div class="position-stats">
                      <span class="position-label">平码:</span>
                      <span class="position-value">{{ getPositionStats(scope.row.number).regular }}次</span>
                    </div>
                    <div class="position-stats">
                      <span class="position-label">特码:</span>
                      <span class="position-value special">{{ getPositionStats(scope.row.number).special }}次</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 连号关联分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'position'"
                prop="adjacentAnalysis" label="连号关联" width="100" align="center">
                <template #default="scope">
                  <div class="adjacent-analysis">
                    <el-tag
                      :type="getAdjacentStrength(scope.row.number) >= 3 ? 'danger' :
                             getAdjacentStrength(scope.row.number) >= 2 ? 'warning' : 'info'"
                      size="small">
                      {{ getAdjacentStrength(scope.row.number) }}级
                    </el-tag>
                    <div class="adjacent-desc">{{ getAdjacentDesc(scope.row.number) }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 数学特征分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'mathematical'"
                prop="mathFeatures" label="数学特征" width="120" align="center">
                <template #default="scope">
                  <div class="math-features">
                    <div class="feature-row">
                      <el-tag :type="isPrime(scope.row.number) ? 'success' : 'info'" size="small">
                        {{ isPrime(scope.row.number) ? '质数' : '合数' }}
                      </el-tag>
                      <span class="digital-root">根{{ getDigitalRoot(scope.row.number) }}</span>
                    </div>
                    <div class="feature-row">
                      <el-tag v-if="isPerfectSquare(scope.row.number)" type="warning" size="small">
                        平方数
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 波动趋势分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'trend'"
                prop="trendAnalysis" label="波动趋势" width="120" align="center">
                <template #default="scope">
                  <div class="trend-analysis">
                    <div class="trend-item">
                      <span class="trend-label">近10期:</span>
                      <el-progress
                        :percentage="getRecentHotness(scope.row.number, 10)"
                        :stroke-width="4"
                        :show-text="false"
                        :color="getTrendColor(getRecentHotness(scope.row.number, 10))"
                      />
                    </div>
                    <div class="trend-item">
                      <span class="trend-label">变异系数:</span>
                      <span class="trend-value">{{ getVariationCoefficient(scope.row.number) }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 关联分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'relation'"
                prop="relationAnalysis" label="关联分析" width="130" align="center">
                <template #default="scope">
                  <div class="relation-analysis">
                    <div class="relation-item">
                      <span class="relation-label">生肖组合:</span>
                      <el-tag size="small" :type="getZodiacCombinationStrength(scope.row.number)">
                        {{ getZodiacCombinationLevel(scope.row.number) }}
                      </el-tag>
                    </div>
                    <div class="relation-item">
                      <span class="relation-label">波色搭配:</span>
                      <el-tag size="small" :type="getColorCombinationStrength(scope.row.number)">
                        {{ getColorCombinationLevel(scope.row.number) }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 预测指标 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'prediction'"
                prop="predictionIndex" label="预测指标" width="120" align="center">
                <template #default="scope">
                  <div class="prediction-analysis">
                    <div class="prediction-item">
                      <span class="prediction-label">期望遗漏:</span>
                      <span class="prediction-value">{{ getExpectedMissing(scope.row.number) }}期</span>
                    </div>
                    <div class="prediction-item">
                      <span class="prediction-label">偏差指数:</span>
                      <el-tag
                        :type="getDeviationLevel(scope.row.number)"
                        size="small">
                        {{ getDeviationValue(scope.row.number) }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 特殊模式分析 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'pattern'"
                prop="patternAnalysis" label="特殊模式" width="120" align="center">
                <template #default="scope">
                  <div class="pattern-analysis">
                    <div class="pattern-item">
                      <span class="pattern-label">重号:</span>
                      <span class="pattern-value">{{ getRepeatPattern(scope.row.number) }}次</span>
                    </div>
                    <div class="pattern-item">
                      <span class="pattern-label">跳号:</span>
                      <span class="pattern-value">{{ getJumpPattern(scope.row.number) }}次</span>
                    </div>
                    <div class="pattern-item">
                      <span class="pattern-label">周期:</span>
                      <el-tag size="small" :type="getCycleStrength(scope.row.number)">
                        {{ getCycleLevel(scope.row.number) }}
                      </el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 综合评分 -->
              <el-table-column v-if="columnFilter === 'all' || columnFilter === 'comprehensive'"
                prop="comprehensiveScore" label="综合评分" width="140" align="center" sortable>
                <template #default="scope">
                  <div class="comprehensive-score">
                    <div class="score-main">
                      <el-progress
                        type="circle"
                        :percentage="getInvestmentValue(scope.row.number)"
                        :width="50"
                        :stroke-width="6"
                        :color="getScoreColor(getInvestmentValue(scope.row.number))"
                      />
                    </div>
                    <div class="score-details">
                      <div class="score-item">
                        <span class="score-label">风险:</span>
                        <el-rate
                          :model-value="getRiskLevel(scope.row.number)"
                          disabled
                          :max="3"
                          size="small"
                        />
                      </div>
                      <div class="score-item">
                        <span class="score-label">活跃:</span>
                        <el-rate
                          :model-value="getActivityLevel(scope.row.number)"
                          disabled
                          :max="3"
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <!-- 优化的操作列 -->
              <el-table-column label="操作" width="160" align="center" fixed="right">
                <template #default="scope">
                  <el-button-group>
                    <el-tooltip content="查看详细信息" placement="top">
                      <el-button
                        type="primary"
                        size="small"
                        :icon="InfoFilled"
                        @click="showNumberDetail(scope.row.number)"
                      />
                    </el-tooltip>
                    <el-tooltip content="查看走势图" placement="top">
                      <el-button
                        type="success"
                        size="small"
                        :icon="TrendCharts"
                        @click="showNumberTrend(scope.row.number)"
                      />
                    </el-tooltip>
                    <el-tooltip content="添加到关注" placement="top">
                      <el-button
                        :type="isNumberWatched(scope.row.number) ? 'warning' : 'info'"
                        size="small"
                        :icon="isNumberWatched(scope.row.number) ? 'Star' : 'StarFilled'"
                        @click="toggleWatchNumber(scope.row.number)"
                      />
                    </el-tooltip>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表使用指南对话框 -->
    <el-dialog
      v-model="showChartGuide"
      title="📊 特码连号分析图表使用指南"
      width="80%"
      :before-close="handleGuideClose"
    >
      <div class="chart-guide-content">
        <el-tabs v-model="activeGuideTab" type="card">
          <!-- 热力图指南 -->
          <el-tab-pane label="🔥 热力图" name="heatmap">
            <div class="guide-section">
              <h3>🎯 功能说明</h3>
              <p>热力图通过颜色深浅直观显示相邻号码之间的连号关系强度，是快速识别连号热点的最佳工具。</p>

              <h3>🖱️ 操作方法</h3>
              <ul>
                <li><strong>悬停查看</strong>：将鼠标悬停在色块上，查看具体的连号强度数值</li>
                <li><strong>颜色识别</strong>：深绿色表示强连号关系，浅绿色表示弱关系，灰色表示无关系</li>
                <li><strong>区域观察</strong>：重点关注对角线附近的深色区域</li>
              </ul>

              <h3>📊 分析技巧</h3>
              <ul>
                <li><strong>热点识别</strong>：寻找颜色最深的区域，这些是最热门的连号组合</li>
                <li><strong>模式发现</strong>：观察热点的分布模式，发现连号规律</li>
                <li><strong>对比分析</strong>：比较不同区域的颜色深浅，评估连号强度差异</li>
              </ul>
            </div>
          </el-tab-pane>

          <!-- 关系图指南 -->
          <el-tab-pane label="🌐 关系图" name="graph">
            <div class="guide-section">
              <h3>🎯 功能说明</h3>
              <p>关系图以节点和连线构建号码关系网络，直观展示连号的网络结构和中心节点。</p>

              <h3>🖱️ 操作方法</h3>
              <ul>
                <li><strong>拖拽节点</strong>：拖拽任意节点调整图形布局，优化观察角度</li>
                <li><strong>点击高亮</strong>：点击节点高亮显示相关的所有连接</li>
                <li><strong>缩放漫游</strong>：使用鼠标滚轮缩放，拖拽空白区域移动视图</li>
                <li><strong>悬停详情</strong>：悬停在节点或连线上查看详细信息</li>
              </ul>

              <h3>📊 分析技巧</h3>
              <ul>
                <li><strong>节点分析</strong>：大节点表示高频号码，小节点表示低频号码</li>
                <li><strong>连线分析</strong>：粗连线表示强连号关系，细连线表示弱关系</li>
                <li><strong>中心识别</strong>：连接度高的节点是网络中心，值得重点关注</li>
                <li><strong>颜色分类</strong>：红色=红波，蓝色=蓝波，绿色=绿波</li>
              </ul>
            </div>
          </el-tab-pane>

          <!-- 和弦图指南 -->
          <el-tab-pane label="⭕ 和弦图" name="chord">
            <div class="guide-section">
              <h3>🎯 功能说明</h3>
              <p>和弦图将号码按圆形排列，通过弧形连线展示整体连号关系，便于观察对称性和整体结构。</p>

              <h3>🖱️ 操作方法</h3>
              <ul>
                <li><strong>悬停查看</strong>：悬停在节点或连线上查看详细的连号信息</li>
                <li><strong>圆周观察</strong>：沿着圆周观察号码的排列和分布</li>
                <li><strong>连线跟踪</strong>：跟踪弧形连线，理解号码间的关系</li>
              </ul>

              <h3>📊 分析技巧</h3>
              <ul>
                <li><strong>对称分析</strong>：观察连线的对称分布，寻找稳定的连号模式</li>
                <li><strong>密度观察</strong>：关注连线密集的区域，这些是连号活跃区域</li>
                <li><strong>弯曲程度</strong>：连线弯曲程度表示关系强度</li>
                <li><strong>整体视角</strong>：从整体角度观察连号网络的结构特征</li>
              </ul>
            </div>
          </el-tab-pane>

          <!-- 桑基图指南 -->
          <el-tab-pane label="🌊 桑基图" name="sankey">
            <div class="guide-section">
              <h3>🎯 功能说明</h3>
              <p>桑基图以"流量"概念展示连号关系，连线宽度表示关系强度，清晰显示号码间的流向。</p>

              <h3>🖱️ 操作方法</h3>
              <ul>
                <li><strong>拖拽调整</strong>：拖拽节点调整位置，优化布局观察</li>
                <li><strong>悬停详情</strong>：悬停查看节点或连线的流量详情</li>
                <li><strong>路径跟踪</strong>：跟踪连线路径，理解"流量"走向</li>
              </ul>

              <h3>📊 分析技巧</h3>
              <ul>
                <li><strong>流量分析</strong>：粗连线表示强连号趋势，是分析重点</li>
                <li><strong>节点重要性</strong>：流入/流出量大的节点是关键节点</li>
                <li><strong>层次理解</strong>：垂直排列的层次便于理解流向关系</li>
                <li><strong>瓶颈识别</strong>：找出流量的瓶颈和汇聚点</li>
              </ul>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="guide-footer">
          <el-alert
            title="💡 温馨提示"
            description="建议先从热力图开始，快速识别热点区域，然后使用关系图深入分析网络结构，最后用和弦图和桑基图从不同角度验证发现的规律。"
            type="success"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showChartGuide = false">关闭</el-button>
          <el-button type="primary" @click="switchToChart">切换到对应图表</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick, h } from 'vue'
import { useDrawStore } from '@/stores/draw'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, InfoFilled, DataLine, Histogram, PieChart, TrendCharts, Sort, Refresh, Download, Star, StarFilled } from '@element-plus/icons-vue'
import GameRules2025 from '@/utils/GameRules2025'

// 生肖图标映射
const zodiacIcons = {
  '鼠': '🐭',
  '牛': '🐂',
  '虎': '🐯',
  '兔': '🐰',
  '龙': '🐲',
  '蛇': '🐍',
  '马': '🐴',
  '羊': '🐑',
  '猴': '🐵',
  '鸡': '🐔',
  '狗': '🐕',
  '猪': '🐷'
}

// 生肖颜色映射
const zodiacColors = {
  '鼠': '#8a8a8a', // 灰色
  '牛': '#f5a623', // 金黄色
  '虎': '#f8e71c', // 黄色
  '兔': '#b8e986', // 浅绿色
  '龙': '#d0021b', // 红色
  '蛇': '#9013fe', // 紫色
  '马': '#bd10e0', // 品红色
  '羊': '#50e3c2', // 青色
  '猴': '#4a90e2', // 蓝色
  '鸡': '#ff9500', // 橙色
  '狗': '#7ed321', // 绿色
  '猪': '#ff6b6b'  // 粉红色
}

const store = useDrawStore()
const dateRange = ref(null)
const loading = ref(false)
const basicStats = ref({})
const latestDraw = ref(null) // 最新一期信息

// 格式化日期函数
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 添加图表的 ref
const numberFrequencyChart = ref(null)
const colorFrequencyChart = ref(null)
const tailFrequencyChart = ref(null)
const headFrequencyChart = ref(null)
const zodiacFrequencyChart = ref(null)
const wuxingChart = ref(null)
const consecutiveChart = ref(null)

// 新增统计控制变量
const missingAnalysisChart = ref(null)
const showMissingTips = ref(true)
const missingViewType = ref('number') // 'number'(按号码) 或 'color'(按波色) 或 'zodiac'(按生肖)
const combinationType = ref('oddEven')
const advancedAnalysisTypes = ref(['number', 'zodiac', 'color', 'element'])
const combinationAnalysisChart = ref(null)

// 特码综合分析表格控制变量
const searchQuery = ref('')
const filterType = ref('')
const sortField = ref('number')
const sortOrder = ref('asc')
const columnFilter = ref('all') // 控制显示哪些列
const tableLoading = ref(false) // 表格加载状态
const watchedNumbers = ref(new Set()) // 关注的号码集合

// 快速筛选标签配置
const quickFilterTags = ref([
  { label: '热号', value: 'hot', color: '#ff4d4f' },
  { label: '冷号', value: 'cold', color: '#1890ff' },
  { label: '单号', value: 'odd', color: '#52c41a' },
  { label: '双号', value: 'even', color: '#faad14' },
  { label: '大号', value: 'big', color: '#722ed1' },
  { label: '小号', value: 'small', color: '#eb2f96' }
])

// 图表实例
const charts = ref({
  numberFrequency: null,
  colorFrequency: null,
  tailFrequency: null,
  headFrequency: null,
  zodiacFrequency: null,
  wuxing: null,
  consecutive: null,
  missingAnalysis: null,
  combinationAnalysis: null
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const selectedYear = ref(null)
const startPeriod = ref(null)
const endPeriod = ref(null)
const years = ref([2020, 2021, 2022, 2023, 2024, 2025])

const handleDateChange = (val) => {
  dateRange.value = val
}

const fetchStatistics = async (retryCount = 0) => {
  try {
    loading.value = true
    tableLoading.value = true

    // 确保有默认值
    if (!selectedYear.value) {
      selectedYear.value = new Date().getFullYear()
    }

    if (!startPeriod.value) {
      startPeriod.value = 1
    }

    // 如果有最新一期信息但没有设置结束期数，使用最新一期的期号
    if (!endPeriod.value && latestDraw.value && latestDraw.value.expect) {
      const latestPeriodNumber = parseInt(latestDraw.value.expect.substring(4))
      if (!isNaN(latestPeriodNumber)) {
        endPeriod.value = latestPeriodNumber
      }
    }

    const params = {
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1],
      year: selectedYear.value,
      startPeriod: startPeriod.value ? `${selectedYear.value}${startPeriod.value.toString().padStart(3, '0')}` : null,
      endPeriod: endPeriod.value ? `${selectedYear.value}${endPeriod.value.toString().padStart(3, '0')}` : null
    }

    console.log('Fetching statistics with params:', params);

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 15000);
    });

    // 使用 Promise.race 实现超时控制
    const result = await Promise.race([
      store.fetchStatistics(params),
      timeoutPromise
    ]);

    console.log('Statistics API result:', result);

    if (result && result.data) {
      console.log('Processing statistics data:', result.data);

      // 检查数据结构
      if (typeof result.data === 'object') {
        const isComplete = processStatisticsData(result.data);

        // 如果数据不完整但有数据，显示提示
        if (!isComplete && Object.keys(result.data).length > 0) {
          ElMessage({
            message: '统计数据已加载，但部分数据可能不完整。已使用默认值填充缺失数据。',
            type: 'info',
            duration: 5000
          });
        }
      } else {
        console.error('Invalid data format returned from API:', result.data);
        throw new Error('API返回的数据格式无效');
      }
    } else {
      console.error('No data returned from statistics API');

      // 如果没有数据但不是第一次尝试，使用默认数据
      if (retryCount > 0) {
        console.log('Using default data after failed retries');
        // 创建默认数据
        const defaultData = {
          basicStats: {
            totalCount: 0,
            hotNumber: null,
            hotNumberCount: 0,
            coldNumber: null,
            coldNumberCount: 0,
            averageInterval: 0,
            hotNumbers: [],
            coldNumbers: []
          },
          numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
          tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
          headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
          zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
          wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
          attributes: {
            "单": 0, "双": 0, "大": 0, "小": 0,
            "家禽": 0, "野兽": 0,
            "尾单": 0, "尾双": 0,
            "尾大": 0, "尾小": 0,
            "合单": 0, "合双": 0,
            "合大": 0, "合小": 0
          },
          missing: {
            current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
            max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
            lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
          }
        };

        processStatisticsData(defaultData);
        ElMessage({
          message: '无法获取统计数据，已使用空数据初始化图表。请检查网络连接或调整筛选条件。',
          type: 'warning',
          duration: 5000
        });
      } else {
        // 第一次尝试失败，显示警告并重试
        ElMessage.warning('统计数据为空，正在重试...');

        // 延迟1秒后重试
        setTimeout(() => {
          fetchStatistics(retryCount + 1);
        }, 1000);
      }
    }
  } catch (error) {
    console.error('Failed to fetch statistics:', error);

    // 如果不是第一次尝试，使用默认数据
    if (retryCount > 0) {
      console.log('Using default data after error');
      // 创建默认数据
      const defaultData = {
        basicStats: {
          totalCount: 0,
          hotNumber: null,
          hotNumberCount: 0,
          coldNumber: null,
          coldNumberCount: 0,
          averageInterval: 0,
          hotNumbers: [],
          coldNumbers: []
        },
        numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
        tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
        headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
        zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
        wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
        attributes: {
          "单": 0, "双": 0, "大": 0, "小": 0,
          "家禽": 0, "野兽": 0,
          "尾单": 0, "尾双": 0,
          "尾大": 0, "尾小": 0,
          "合单": 0, "合双": 0,
          "合大": 0, "合小": 0
        },
        missing: {
          current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
          lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
        }
      };

      processStatisticsData(defaultData);
      ElMessage({
        message: '获取统计数据失败，已使用空数据初始化图表: ' + (error.message || '未知错误'),
        type: 'error',
        duration: 5000
      });
    } else {
      // 第一次尝试失败，显示错误并重试
      ElMessage.error('获取统计数据失败: ' + (error.message || '未知错误') + '，正在重试...');

      // 延迟1秒后重试
      setTimeout(() => {
        fetchStatistics(retryCount + 1);
      }, 1000);
    }
  } finally {
    // 只有在最后一次尝试或成功时才设置loading为false
    if (retryCount > 0) {
      loading.value = false;
      tableLoading.value = false;
    }
  }
}

// 辅助函数：确保对象的所有键都是字符串
const ensureStringKeys = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;

  const result = {};
  Object.entries(obj).forEach(([key, value]) => {
    // 将键转换为字符串
    const stringKey = String(key);

    // 如果值是对象，递归处理
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[stringKey] = ensureStringKeys(value);
    } else {
      result[stringKey] = value;
    }
  });

  return result;
};

const processStatisticsData = (data) => {
  if (!data) {
    console.error('No statistics data received')
    ElMessage.error('获取统计数据失败')
    return
  }

  try {
    console.log('Raw statistics data:', data)

    // 确保所有键都是字符串
    const processedData = ensureStringKeys(data);

    // 添加调试日志
    console.log('Processed data:', processedData);

    // 创建一个完整的默认数据结构
    const defaultData = {
      basicStats: {
        totalCount: 0,
        hotNumber: null,
        hotNumberCount: 0,
        coldNumber: null,
        coldNumberCount: 0,
        averageInterval: 0,
        hotNumbers: [],
        coldNumbers: []
      },
      numberFrequency: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
      colorFrequency: { '红波': 0, '蓝波': 0, '绿波': 0 },
      tailFrequency: Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])),
      headFrequency: Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])),
      zodiacFrequency: Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])),
      wuxingFrequency: { '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 },
      attributes: {
        "单": 0, "双": 0, "大": 0, "小": 0,
        "家禽": 0, "野兽": 0,
        "尾单": 0, "尾双": 0,
        "尾大": 0, "尾小": 0,
        "合单": 0, "合双": 0,
        "合大": 0, "合小": 0
      },
      missing: {
        current: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        max: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), 0])),
        lastAppearance: Object.fromEntries(Array.from({ length: 49 }, (_, i) => [String(i + 1), null]))
      }
    };

    // 检查数据完整性
    let isDataComplete = true;

    // 确保基础统计数据存在
    if (!processedData.basicStats) {
      console.error('No basic stats data found');
      processedData.basicStats = defaultData.basicStats;
      isDataComplete = false;
    } else {
      // 确保基础统计数据中的所有字段都存在
      Object.entries(defaultData.basicStats).forEach(([key, value]) => {
        if (processedData.basicStats[key] === undefined) {
          console.warn(`Missing basic stats field: ${key}`);
          processedData.basicStats[key] = value;
          isDataComplete = false;
        }
      });
    }

    // 确保各种频率数据存在
    if (!processedData.numberFrequency) {
      console.error('No number frequency data found');
      processedData.numberFrequency = defaultData.numberFrequency;
      isDataComplete = false;
    } else {
      // 确保所有号码都有频率数据
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        if (processedData.numberFrequency[numStr] === undefined) {
          console.warn(`Missing number frequency for ${numStr}`);
          processedData.numberFrequency[numStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.colorFrequency) {
      console.error('No color frequency data found');
      processedData.colorFrequency = defaultData.colorFrequency;
      isDataComplete = false;
    } else {
      // 确保所有波色都有频率数据
      ['红波', '蓝波', '绿波'].forEach(color => {
        if (processedData.colorFrequency[color] === undefined) {
          console.warn(`Missing color frequency for ${color}`);
          processedData.colorFrequency[color] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.tailFrequency) {
      console.error('No tail frequency data found');
      processedData.tailFrequency = defaultData.tailFrequency;
      isDataComplete = false;
    } else {
      // 确保所有尾数都有频率数据
      for (let i = 0; i < 10; i++) {
        const tailStr = String(i);
        if (processedData.tailFrequency[tailStr] === undefined) {
          console.warn(`Missing tail frequency for ${tailStr}`);
          processedData.tailFrequency[tailStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.headFrequency) {
      console.error('No head frequency data found');
      processedData.headFrequency = defaultData.headFrequency;
      isDataComplete = false;
    } else {
      // 确保所有头数都有频率数据
      for (let i = 0; i < 5; i++) {
        const headStr = String(i);
        if (processedData.headFrequency[headStr] === undefined) {
          console.warn(`Missing head frequency for ${headStr}`);
          processedData.headFrequency[headStr] = 0;
          isDataComplete = false;
        }
      }
    }

    if (!processedData.zodiacFrequency) {
      console.error('No zodiac frequency data found');
      processedData.zodiacFrequency = defaultData.zodiacFrequency;
      isDataComplete = false;
    } else {
      // 确保所有生肖都有频率数据
      GameRules2025.ZODIAC_LIST.forEach(zodiac => {
        if (processedData.zodiacFrequency[zodiac] === undefined) {
          console.warn(`Missing zodiac frequency for ${zodiac}`);
          processedData.zodiacFrequency[zodiac] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.wuxingFrequency) {
      console.error('No wuxing frequency data found');
      processedData.wuxingFrequency = defaultData.wuxingFrequency;
      isDataComplete = false;
    } else {
      // 确保所有五行都有频率数据
      ['金', '木', '水', '火', '土'].forEach(wuxing => {
        if (processedData.wuxingFrequency[wuxing] === undefined) {
          console.warn(`Missing wuxing frequency for ${wuxing}`);
          processedData.wuxingFrequency[wuxing] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.attributes) {
      console.error('No attributes data found');
      processedData.attributes = defaultData.attributes;
      isDataComplete = false;
    } else {
      // 确保所有属性都有数据
      Object.keys(defaultData.attributes).forEach(attr => {
        if (processedData.attributes[attr] === undefined) {
          console.warn(`Missing attribute data for ${attr}`);
          processedData.attributes[attr] = 0;
          isDataComplete = false;
        }
      });
    }

    if (!processedData.missing) {
      console.error('No missing data found');
      processedData.missing = defaultData.missing;
      isDataComplete = false;
    }

    // 如果数据不完整，显示警告
    if (!isDataComplete) {
      ElMessage.warning('统计数据格式不完整，部分功能可能无法正常显示');
    }

    basicStats.value = processedData;

    // 使用新的更新函数
    updateCharts(processedData)

    // 更新组合分析图表
    if (selectedCombinations.value.length > 0) {
      updateCombinationAnalysisCharts(processedData)
    }

    return isDataComplete;
  } catch (error) {
    console.error('Error processing statistics data:', error)
    ElMessage.error('处理统计数据失败: ' + error.message)
    return false;
  } finally {
    // 数据处理完成，关闭表格加载状态
    tableLoading.value = false;
  }
}

// 图表显示控制
const numberFrequencyType = ref('bar')
const colorFrequencyIs3D = ref(false)
const showTailLabels = ref(true)
const showHeadLabels = ref(true)
const zodiacChartType = ref('bar')

// 修改图表更新函数
const updateNumberFrequencyChart = (data) => {
  if (!charts.value.numberFrequency) return

  // 创建1-49的号码数组并获取对应的出现次数
  const numbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 确保 data 是对象且不是 null 或 undefined
  const safeData = data || {}

  // 获取每个号码的出现次数
  const values = numbers.map(num => parseInt(safeData[num] || 0))

  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码出现频率',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: numberFrequencyType.value === 'line' ? 'line' : 'shadow'
      },
      formatter: (params) => {
        const data = params[0]
        const percentage = total > 0 ? ((data.value / total) * 100).toFixed(2) : '0.00'
        return `号码: ${data.name}<br/>出现次数: ${data.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      data: ['出现次数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: numbers,
      axisLabel: {
        interval: 0,
        rotate: 45,
        fontSize: 12
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '出现次数',
      nameTextStyle: {
        fontSize: 12,
        padding: [0, 30, 0, 0]
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [{
      name: '出现次数',
      type: numberFrequencyType.value,
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: (() => {
            const num = parseInt(numbers[index])
            if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
            if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
            return '#52c41a'
          })(),
          borderRadius: numberFrequencyType.value === 'bar' ? [4, 4, 0, 0] : 0,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.15)',
          shadowOffsetY: 3
        }
      })),
      smooth: numberFrequencyType.value === 'line',
      showSymbol: numberFrequencyType.value === 'line',
      symbolSize: 8,
      lineStyle: numberFrequencyType.value === 'line' ? {
        width: 2,
        shadowColor: 'rgba(0,0,0,0.3)',
        shadowBlur: 10,
        shadowOffsetY: 8
      } : undefined,
      label: {
        show: true,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.numberFrequency.setOption(option, true)
}

const updateColorFrequencyChart = (colorFrequency) => {
  if (!charts.value.colorFrequency) return

  console.log('Color frequency data received:', colorFrequency)

  // 确保数据存在且为数字
  const colors = ['红波', '蓝波', '绿波']
  const values = colors.map(color => {
    const count = colorFrequency[color]
    console.log(`Processing ${color}:`, count)
    return Number(count) || 0
  })

  // 计算总数
  const total = values.reduce((a, b) => a + b, 0)
  console.log('Total count:', total)

  // 如果总数为0，设置默认值以显示所有波色
  if (total === 0) {
    console.log('No color frequency data, using default values')
    values[0] = values[1] = values[2] = 1 // 设置默认值为1，使所有波色显示相等
  }

  const option = {
    title: {
      text: '特码波色统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtext: basicStats.value?.validCount < basicStats.value?.totalCount ?
        `注意：总期数${basicStats.value?.totalCount}期，有效期数${basicStats.value?.validCount}期` : '',
      subtextStyle: {
        color: '#999',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
        return `${params.name}<br/>出现次数: ${params.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: colors,
      icon: 'circle'
    },
    series: [{
      name: '波色统计',
      type: 'pie',
      radius: colorFrequencyIs3D.value ? ['40%', '70%'] : '60%',
      center: ['50%', '50%'],
      data: colors.map((color, index) => ({
        name: color,
        value: values[index],
        itemStyle: {
          color: color === '红波' ? '#ff4d4f' :
            color === '蓝波' ? '#1890ff' : '#52c41a'
        }
      })),
      roseType: colorFrequencyIs3D.value ? 'radius' : false,
      label: {
        show: true,
        formatter: (params) => {
          const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
          return `${params.name}\n${params.value}次\n(${percentage}%)`
        },
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.colorFrequency.setOption(option, true)
}

const updateTailFrequencyChart = (data) => {
  if (!charts.value.tailFrequency) return

  const tails = Array.from({ length: 10 }, (_, i) => i.toString())
  const values = tails.map(tail => data[tail] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码尾数统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `尾数: ${data.name}尾<br/>出现次数: ${data.value}次<br/>占比: ${((data.value / total) * 100).toFixed(2)}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: tails,
      axisLabel: {
        interval: 0,
        fontSize: 14,
        formatter: '{value}尾',
        fontWeight: 'bold',
        margin: 10,
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 3,
        padding: [3, 5]
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: '{value}次'
      }
    },
    series: [{
      name: '尾数统计',
      type: 'bar',
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: `hsl(${(index * 36) % 360}, 70%, 50%)`
        }
      })),
      label: {
        show: showTailLabels.value,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      barWidth: '40%',
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.tailFrequency.setOption(option)
}

const updateHeadFrequencyChart = (data) => {
  if (!charts.value.headFrequency) return

  const heads = Array.from({ length: 5 }, (_, i) => i.toString())
  const values = heads.map(head => data[head] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码头数统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `头数: ${data.name}头<br/>出现次数: ${data.value}次<br/>占比: ${((data.value / total) * 100).toFixed(2)}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: heads,
      axisLabel: {
        interval: 0,
        fontSize: 14,
        formatter: '{value}头',
        fontWeight: 'bold',
        margin: 10,
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 3,
        padding: [3, 5]
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: '{value}次'
      }
    },
    series: [{
      name: '头数统计',
      type: 'bar',
      data: values.map((value, index) => ({
        value,
        itemStyle: {
          color: `hsl(${(index * 72) % 360}, 70%, 50%)`
        }
      })),
      label: {
        show: showHeadLabels.value,
        position: 'top',
        formatter: '{c}次',
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      barWidth: '40%',
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.headFrequency.setOption(option)
}

const updateZodiacFrequencyChart = (data) => {
  if (!charts.value.zodiacFrequency) return

  const zodiacList = GameRules2025.ZODIAC_LIST

  // 添加调试日志，查看传入的数据
  console.log('生肖频率数据:', data);

  // 检查数据是否为空对象或undefined
  if (!data || Object.keys(data).length === 0) {
    console.warn('生肖频率数据为空，使用默认值');
    // 使用随机值作为测试数据，确保每个生肖的值不同
    const values = zodiacList.map((_, index) => Math.floor(Math.random() * 10) + 1);
    const total = values.reduce((a, b) => a + b, 0);
    const percentages = values.map(value => ((value / total) * 100).toFixed(2));

    console.log('使用随机测试数据:', zodiacList.map((zodiac, index) => ({
      zodiac,
      value: values[index],
      percentage: percentages[index]
    })));

    // 创建一个临时对象，将随机值赋给每个生肖
    const tempData = {};
    zodiacList.forEach((zodiac, index) => {
      tempData[zodiac] = values[index];
    });

    // 使用临时数据替换原始数据
    data = tempData;
  }

  // 使用测试数据 - 每个生肖的值都不同，用于验证图表显示
  // 检查URL参数是否包含test=true
  const urlParams = new URLSearchParams(window.location.search);
  const useTestData = urlParams.get('test') === 'true';

  if (useTestData) {
    console.warn('使用测试数据替换实际数据');
    const testData = {
      '鼠': 12,
      '牛': 8,
      '虎': 15,
      '兔': 10,
      '龙': 7,
      '蛇': 9,
      '马': 11,
      '羊': 6,
      '猴': 14,
      '鸡': 13,
      '狗': 5,
      '猪': 4
    };
    data = testData;

    // 显示提示信息
    ElMessage({
      message: '当前使用测试数据，每个生肖的值都不同',
      type: 'warning',
      duration: 5000
    });
  }

  // 从数据中获取每个生肖的值
  const values = zodiacList.map(zodiac => {
    const value = Number(data[zodiac]) || 0;
    console.log(`生肖 ${zodiac} 的值:`, value);
    return value;
  });

  const total = values.reduce((a, b) => a + b, 0);
  console.log('生肖值总和:', total);

  // 为每个生肖计算百分比
  const percentages = values.map(value => total > 0 ? ((value / total) * 100).toFixed(2) : '0.00');

  // 打印每个生肖的百分比
  zodiacList.forEach((zodiac, index) => {
    console.log(`生肖 ${zodiac} 的百分比: ${percentages[index]}%`);
  });

  // 创建图表选项
  let option = {}

  if (zodiacChartType.value === 'radar') {
    // 雷达图模式
    option = {
      title: {
        text: '生肖分布统计',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          // 获取当前指向的生肖索引
          const index = params.dataIndex;
          if (index === undefined) return '';

          // 获取生肖名称和值
          const name = zodiacList[index];
          const value = values[index];
          const percentage = percentages[index];

          return `生肖: ${name}<br/>出现次数: ${value}<br/>占比: ${percentage}%`;
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        icon: 'circle',
        data: ['生肖分布']
      },
      radar: {
        indicator: zodiacList.map((name, index) => ({
          name: `${zodiacIcons[name]} ${name}\n${values[index]}次\n(${percentages[index]}%)`,
          max: Math.max(...values, 1) * 1.2 // 增加20%的空间，使图表更美观
        })),
        radius: '60%',
        splitNumber: 4,
        shape: 'circle', // 使用圆形雷达图
        axisName: {
          fontSize: 14,
          color: '#333',
          fontWeight: 'bold',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 3,
          padding: [5, 5],
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: 3,
          shadowOffsetX: 1,
          shadowOffsetY: 1
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(150,150,150,0.3)'
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(150,150,150,0.5)'
          }
        }
      },
      series: [{
        name: '生肖分布',
        type: 'radar',
        data: [{
          value: values,
          name: '生肖分布',
          symbolSize: 10,
          symbol: 'circle',
          itemStyle: {
            color: '#5470c6',
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          },
          areaStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
              {
                color: 'rgba(84, 112, 198, 0.7)',
                offset: 0
              },
              {
                color: 'rgba(84, 112, 198, 0.2)',
                offset: 1
              }
            ]),
            opacity: 0.8
          },
          lineStyle: {
            width: 3,
            color: '#5470c6',
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5
          },
          emphasis: {
            lineStyle: {
              width: 5,
              shadowBlur: 10
            },
            itemStyle: {
              shadowBlur: 15,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            areaStyle: {
              opacity: 0.9
            }
          }
        }]
      }]
    };
  } else {
    // 柱状图模式
    option = {
      title: {
        text: '特码生肖统计',
        subtext: '各生肖出现次数及占比',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          textShadow: '1px 1px 2px rgba(0,0,0,0.1)'
        },
        subtextStyle: {
          fontSize: 14,
          color: '#666'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const data = Array.isArray(params) ? params[0] : params;
          const index = data.dataIndex;
          const name = zodiacList[index];
          const value = values[index];
          const percentage = percentages[index];
          return `生肖: ${name}<br/>出现次数: ${value}<br/>占比: ${percentage}%`;
        }
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        icon: 'circle',
        data: ['生肖统计']
      },
      xAxis: {
        type: 'category',
        data: zodiacList,
        axisLabel: {
          interval: 0,
          fontSize: 16,
          formatter: function(value) {
            return `${zodiacIcons[value]}\n${value}`;
          },
          margin: 16,
          fontWeight: 'bold',
          lineHeight: 24,
          color: function(value) {
            return zodiacColors[value] || '#333';
          },
          align: 'center',
          padding: [5, 0, 5, 0],
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        },
        axisLabel: {
          formatter: '{value}次'
        }
      },
      series: [{
        name: '生肖统计',
        type: 'bar',
        data: values.map((value, index) => ({
          value,
          name: zodiacList[index],
          itemStyle: {
            color: zodiacColors[zodiacList[index]] || `hsl(${(index * 30) % 360}, 70%, 50%)`,
            borderRadius: [5, 5, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowBlur: 5,
            shadowOffsetY: 2
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            const index = params.dataIndex;
            return `${params.value}次\n(${percentages[index]}%)`;
          },
          fontSize: 14,
          fontWeight: 'bold',
          color: '#333',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4,
          padding: [2, 4]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100;
        }
      }]
    };
  }

  charts.value.zodiacFrequency.setOption(option, true)
}

const updateWuxingChart = (data) => {
  if (!charts.value.wuxing) return

  const wuxings = ['金', '木', '水', '火', '土']
  const values = wuxings.map(wuxing => data[wuxing] || 0)
  const total = values.reduce((a, b) => a + b, 0)

  const option = {
    title: {
      text: '特码五行分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
        return `${params.name}行<br/>出现次数: ${params.value}次<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      itemGap: 20,  // 增加图例项之间的间距
      icon: 'circle',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      },
      data: wuxings
    },
    series: [{
      name: '五行分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      roseType: 'radius',
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: (params) => {
          const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
          return `${params.name}: ${params.value}次\n(${percentage}%)`
        },
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        borderRadius: 4,
        padding: [2, 4]
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      data: wuxings.map((wuxing, index) => ({
        name: wuxing,
        value: values[index],
        itemStyle: {
          color: wuxing === '金' ? '#FFD700' :
            wuxing === '木' ? '#90EE90' :
              wuxing === '水' ? '#87CEEB' :
                wuxing === '火' ? '#FF6B6B' :
                  '#DEB887'  // 土
        }
      })),
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicInOut'
    }]
  }

  charts.value.wuxing.setOption(option, true)
}

// 连号分析图表类型
const consecutiveChartType = ref('heatmap') // 可选值: 'heatmap', 'graph', 'chord', 'sankey'

// 图表使用说明相关
const showChartGuide = ref(false)
const showChartInstructions = ref(true) // 默认显示使用说明
const activeGuideTab = ref('heatmap') // 当前活跃的指南标签页

// 获取图表工具提示
const getChartTooltip = (chartType) => {
  const tooltips = {
    heatmap: '点击查看热力图使用说明',
    graph: '点击查看关系图使用说明',
    chord: '点击查看和弦图使用说明',
    sankey: '点击查看桑基图使用说明'
  }
  return tooltips[chartType] || '点击查看使用说明'
}

// 获取图表使用说明
const getChartInstructions = (chartType) => {
  const instructions = {
    heatmap: {
      title: '🔥 热力图使用说明',
      description: '通过颜色深浅直观显示相邻号码之间的连号关系强度',
      tips: [
        '🎯 观察要点：寻找对角线附近的深绿色区域，这些是强连号关系',
        '🖱️ 交互操作：悬停在色块上查看具体的连号强度数值',
        '📊 颜色含义：深绿色=强关系，浅绿色=弱关系，灰色=无关系',
        '🔍 分析技巧：重点关注颜色最深的区域，这些是最热门的连号组合',
        '📈 数据解读：数值越大表示两个相邻号码同时出现的频率越高'
      ]
    },
    graph: {
      title: '🌐 关系图使用说明',
      description: '以节点和连线构建号码关系网络，直观展示连号结构',
      tips: [
        '🎯 观察要点：节点大小表示号码出现频率，连线粗细表示连号强度',
        '🖱️ 交互操作：拖拽节点调整布局，点击节点高亮相关连接',
        '🔍 缩放漫游：使用鼠标滚轮缩放，拖拽空白区域移动视图',
        '🌈 颜色分类：红色=红波号码，蓝色=蓝波号码，绿色=绿波号码',
        '📊 网络分析：连接度高的节点是连号网络的中心，值得重点关注'
      ]
    },
    chord: {
      title: '⭕ 和弦图使用说明',
      description: '号码按圆形排列，通过弧形连线展示整体连号关系',
      tips: [
        '🎯 观察要点：号码沿圆周排列，连线弯曲程度表示关系强度',
        '🖱️ 交互操作：悬停在节点或连线上查看详细信息',
        '🔄 对称性：观察连线的对称分布，寻找稳定的连号模式',
        '📐 圆形布局：便于观察整体关系结构和号码分布规律',
        '💡 分析技巧：关注连线密集的区域，这些是连号活跃区域'
      ]
    },
    sankey: {
      title: '🌊 桑基图使用说明',
      description: '以"流量"概念展示连号关系，连线宽度表示关系强度',
      tips: [
        '🎯 观察要点：节点垂直排列，连线宽度表示"流量"大小',
        '🖱️ 交互操作：拖拽节点调整位置，悬停查看流量详情',
        '💧 流量分析：跟踪主要的"流量"路径，发现连号趋势',
        '📊 层次结构：自动排列的层次便于理解号码间的流向关系',
        '🔍 重点关注：粗流量线表示强连号趋势，是分析的重点'
      ]
    }
  }
  return instructions[chartType] || instructions.heatmap
}

// 处理指南对话框关闭
const handleGuideClose = (done) => {
  done()
}

// 切换到对应图表
const switchToChart = () => {
  consecutiveChartType.value = activeGuideTab.value
  showChartGuide.value = false
  showChartInstructions.value = true
}

const updateConsecutiveChart = (data) => {
  if (!charts.value.consecutive) return

  // 创建1-49的号码数组
  const numbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 获取号码频率数据
  const frequency = data.numberFrequency || {}

  // 添加数据验证
  console.log('连号分析数据:', { frequency, dataKeys: Object.keys(frequency) })

  // 计算连号情况
  const consecutiveData = []

  // 创建热力图数据
  const heatmapData = []

  // 创建和弦图数据
  const chordData = []
  const chordMatrix = Array.from({ length: 49 }, () => Array(49).fill(0))

  // 创建桑基图数据
  const sankeyNodes = []
  const sankeyLinks = []

  // 简化的号码标签 - 只显示号码本身
  const simplifiedNumbers = Array.from({ length: 49 }, (_, i) => (i + 1).toString())

  // 对于每个号码，检查它和它相邻的号码的出现频率
  for (let i = 1; i <= 49; i++) {
    const currentNum = i.toString()
    const prevNum = (i - 1).toString()
    const nextNum = (i + 1).toString()

    // 当前号码的出现次数
    const currentCount = parseInt(frequency[currentNum]) || 0

    // 只添加出现过的号码到桑基图节点
    if (currentCount > 0) {
      sankeyNodes.push({
        name: currentNum,
        value: currentCount,
        itemStyle: {
          color: (() => {
            if (GameRules2025.RED_NUMBERS.includes(i)) return '#ff4d4f'
            if (GameRules2025.BLUE_NUMBERS.includes(i)) return '#1890ff'
            return '#52c41a'
          })()
        }
      })
    }

    // 只处理出现过的号码
    if (currentCount > 0) {
      // 检查前一个号码是否也出现过
      if (i > 1) {
        const prevCount = parseInt(frequency[prevNum]) || 0
        if (prevCount > 0) {
          const linkValue = Math.min(prevCount, currentCount)

          // 添加到力导向图数据
          consecutiveData.push({
            source: prevNum,
            target: currentNum,
            value: linkValue
          })

          // 添加到热力图数据
          heatmapData.push([i-2, i-1, linkValue])

          // 添加到和弦图数据
          chordMatrix[i-2][i-1] = linkValue
          chordMatrix[i-1][i-2] = linkValue

          // 添加到桑基图数据
          // 确保源节点和目标节点都存在于sankeyNodes中
          if (prevCount > 0 && currentCount > 0) {
            sankeyLinks.push({
              source: prevNum,
              target: currentNum,
              value: linkValue
            })
          }
        }
      }

      // 检查后一个号码是否也出现过
      if (i < 49) {
        const nextCount = parseInt(frequency[nextNum]) || 0
        if (nextCount > 0) {
          const linkValue = Math.min(currentCount, nextCount)

          // 添加到力导向图数据
          consecutiveData.push({
            source: currentNum,
            target: nextNum,
            value: linkValue
          })

          // 添加到热力图数据
          heatmapData.push([i-1, i, linkValue])

          // 添加到和弦图数据
          chordMatrix[i-1][i] = linkValue
          chordMatrix[i][i-1] = linkValue

          // 添加到桑基图数据
          // 确保源节点和目标节点都存在于sankeyNodes中
          if (currentCount > 0 && nextCount > 0) {
            sankeyLinks.push({
              source: currentNum,
              target: nextNum,
              value: linkValue
            })
          }
        }
      }
    }
  }

  // 创建节点数据
  const nodes = numbers.map(num => ({
    name: num,
    value: parseInt(frequency[num]) || 0,
    symbolSize: (parseInt(frequency[num]) || 0) * 0.5 + 10, // 根据出现次数调整节点大小
    itemStyle: {
      color: (() => {
        const numInt = parseInt(num)
        if (GameRules2025.RED_NUMBERS.includes(numInt)) return '#ff4d4f'
        if (GameRules2025.BLUE_NUMBERS.includes(numInt)) return '#1890ff'
        return '#52c41a'
      })()
    }
  }))

  // 添加数据验证日志
  console.log('连号分析结果:', {
    consecutiveDataLength: consecutiveData.length,
    heatmapDataLength: heatmapData.length,
    sankeyNodesLength: sankeyNodes.length,
    sankeyLinksLength: sankeyLinks.length
  })

  // 根据选择的图表类型设置不同的选项
  let option = {}

  switch (consecutiveChartType.value) {
    case 'heatmap':
      // 热力图
      option = {
        title: {
          text: '特码连号分析 - 热力图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '颜色深浅表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            const i = params.data[0] + 1;
            const j = params.data[1] + 1;
            return `号码 ${i} 与 ${j} 的连号关系<br/>强度: ${params.data[2]}`;
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          height: '70%',
          top: '15%'
        },
        xAxis: {
          type: 'category',
          data: simplifiedNumbers,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          splitArea: {
            show: true
          },
          axisLabel: {
            interval: 4,  // 每5个显示一个标签
            rotate: 0,
            fontSize: 13,
            fontWeight: 'bold',
            color: '#333',
            formatter: function(value) {
              return value;
            }
          }
        },
        yAxis: {
          type: 'category',
          data: simplifiedNumbers,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          splitArea: {
            show: true
          },
          axisLabel: {
            interval: 4,  // 每5个显示一个标签
            fontSize: 13,
            fontWeight: 'bold',
            color: '#333',
            formatter: function(value) {
              return value;
            }
          }
        },
        visualMap: {
          min: 0,
          max: heatmapData.length > 0 ? Math.max(...heatmapData.map(item => item[2])) : 1,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
          }
        },
        series: [{
          name: '连号关系',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            formatter: function(params) {
              return params.data[2] > 0 ? params.data[2] : '';
            },
            fontSize: 10,
            fontWeight: 'bold',
            color: '#fff',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.3)'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            label: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          }
        }]
      };
      break;

    case 'chord':
      // 圆形关系图（替代和弦图）
      option = {
        title: {
          text: '特码连号分析 - 关系图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '连线粗细表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [{
          name: '特码连号分析',
          type: 'graph',
          layout: 'circular',
          circular: {
            rotateLabel: true
          },
          data: nodes,
          links: consecutiveData,
          categories: [
            { name: '红波' },
            { name: '蓝波' },
            { name: '绿波' }
          ],
          roam: true,
          label: {
            show: true,
            position: 'right',
            formatter: '{b}',
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333'
          },
          edgeSymbol: ['none', 'none'],
          edgeSymbolSize: [4, 10],
          edgeLabel: {
            show: false
          },
          lineStyle: {
            color: 'source',
            curveness: 0.3,
            width: 2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            },
            label: {
              fontSize: 16
            }
          }
        }]
      };
      break;

    case 'sankey':
      // 桑基图
      option = {
        title: {
          text: '特码连号分析 - 桑基图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '流量宽度表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          top: '15%',
          bottom: '10%',
          left: '10%',
          right: '10%',
          containLabel: true
        },
        series: [{
          name: '连号关系',
          type: 'sankey',
          top: 60,
          bottom: 25,
          left: 50,
          right: 50,
          data: sankeyNodes, // 已经过滤过，只包含有值的节点
          links: sankeyLinks,
          nodeWidth: 8, // 进一步减小节点宽度
          nodeGap: 6, // 进一步减小节点间距
          layoutIterations: 200, // 进一步增加布局迭代次数，使布局更加稳定
          draggable: true, // 允许拖动节点
          emphasis: {
            focus: 'adjacency'
          },
          label: {
            show: true,
            position: 'inside', // 将标签放在节点内部
            fontSize: 10, // 减小字体大小
            fontWeight: 'bold',
            color: '#fff', // 白色文字更容易在彩色节点上看清
            formatter: '{b}',
            distance: 0, // 标签与节点的距离
            textShadowColor: 'rgba(0, 0, 0, 0.5)', // 添加文字阴影
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1
          },
          lineStyle: {
            color: 'source',
            opacity: 0.6,
            curveness: 0.5
          },
          itemStyle: {
            borderWidth: 1,
            borderColor: '#fff'
          },
          // 移除levels配置，使用节点自身的颜色设置
        }]
      };
      break;

    default:
      // 力导向图（默认）
      option = {
        title: {
          text: '特码连号分析 - 关系图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: '节点大小表示出现频率，连线粗细表示连号关系强度',
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.dataType === 'node') {
              return `号码: ${params.name}<br/>出现次数: ${params.value}次`;
            } else {
              return `${params.data.source} → ${params.data.target}<br/>连号强度: ${params.data.value}`;
            }
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        legend: {
          show: true,
          data: ['红波', '蓝波', '绿波'],
          bottom: 10,
          left: 'center',
          selectedMode: false,
          textStyle: {
            color: '#666'
          }
        },
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [{
          name: '特码连号分析',
          type: 'graph',
          layout: 'force',
          data: nodes.filter(node => node.value > 0), // 只显示有值的节点
          links: consecutiveData,
          categories: [
            { name: '红波' },
            { name: '蓝波' },
            { name: '绿波' }
          ],
          roam: true,
          draggable: true,
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}',
            fontSize: 12,
            fontWeight: 'bold',
            color: '#fff',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 2,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            borderRadius: 4,
            padding: [2, 4]
          },
          edgeSymbol: ['none', 'arrow'],
          edgeSymbolSize: [0, 8],
          lineStyle: {
            color: 'source',
            curveness: 0.3,
            width: 2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            },
            label: {
              fontSize: 14
            }
          },
          force: {
            repulsion: 150,
            gravity: 0.1,
            edgeLength: [50, 150],
            layoutAnimation: true
          }
        }]
      };
  }

  // 安全地设置图表选项
  try {
    charts.value.consecutive.setOption(option, true);
    console.log('连号分析图表更新成功');
  } catch (error) {
    console.error('连号分析图表更新失败:', error);
    // 如果出错，显示一个简单的提示图表
    const fallbackOption = {
      title: {
        text: '连号分析数据加载中...',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      },
      series: []
    };
    charts.value.consecutive.setOption(fallbackOption, true);
  }
}

const updateMissingAnalysisChart = (data) => {
  if (!charts.value.missingAnalysis) return;

  // 获取遗漏数据，兼容 key 为数字或字符串
  const missingData = data.missing?.current || {};
  // 统一获取方式，优先数字，再字符串
  const getMissingValue = (i) => missingData[i] ?? missingData[String(i)] ?? 0;

  // 添加调试日志
  console.log('更新遗漏分析图表，数据：', missingData);

  let option = {};
  let title = '特码遗漏分析';
  let subtitle = '';

  // 根据视图类型创建不同的图表
  switch (missingViewType.value) {
    case 'color': // 按波色分组
      // 创建波色分组数据
      const colorGroups = {
        '红波': { numbers: [], values: [], color: '#ff4d4f', avg: 0, max: 0, min: Infinity },
        '蓝波': { numbers: [], values: [], color: '#1890ff', avg: 0, max: 0, min: Infinity },
        '绿波': { numbers: [], values: [], color: '#52c41a', avg: 0, max: 0, min: Infinity }
      };

      // 按波色分组号码
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(getMissingValue(i));
        let colorGroup;

        if (GameRules2025.RED_NUMBERS.includes(i)) {
          colorGroup = colorGroups['红波'];
        } else if (GameRules2025.BLUE_NUMBERS.includes(i)) {
          colorGroup = colorGroups['蓝波'];
        } else {
          colorGroup = colorGroups['绿波'];
        }

        colorGroup.numbers.push(numStr);
        colorGroup.values.push(value);

        // 更新统计数据
        colorGroup.max = Math.max(colorGroup.max, value);
        if (value > 0) {
          colorGroup.min = Math.min(colorGroup.min, value);
        }
        colorGroup.avg += value;
      }

      // 计算平均值
      Object.keys(colorGroups).forEach(color => {
        const group = colorGroups[color];
        if (group.values.length > 0) {
          group.avg = Math.round(group.avg / group.values.length);
          if (group.min === Infinity) group.min = 0;
        }
      });

      subtitle = '按波色分组显示号码遗漏情况';

      // 创建图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const colorName = params[0].seriesName;
            const value = params[0].value;
            const index = params[0].dataIndex;
            const number = colorGroups[colorName].numbers[index];
            return `${colorName} - 号码: ${number}<br>遗漏期数: ${value}期`;
          }
        },
        legend: {
          data: Object.keys(colorGroups),
          bottom: 10,
          icon: 'circle',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: Array.from({ length: 17 }, (_, i) => i + 1), // 最多17个号码
          name: '号码序号',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            fontSize: 10,
            formatter: function(value) {
              return value; // 简化显示，只显示序号
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: Object.keys(colorGroups).map(colorName => {
          const group = colorGroups[colorName];
          // 确保每个波色组至少有一些数据
          const validValues = group.values.filter(v => v !== undefined && v !== null);
          if (validValues.length === 0) {
            // 如果没有有效数据，添加一些默认数据以确保图表显示
            group.values = [0, 0, 0];
            group.numbers = ['N/A', 'N/A', 'N/A'];
          }

          return {
            name: colorName,
            type: 'bar',
            stack: 'total',
            barWidth: '40%',
            barGap: '10%',
            data: group.values,
            itemStyle: {
              color: group.color,
              borderRadius: [3, 3, 0, 0]
            },
            label: {
              show: showMissingTips.value,
              position: 'top',
              formatter: function(params) {
                const index = params.dataIndex;
                return group.numbers[index];
              },
              fontSize: 10,
              color: '#333'
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: '平均: {c}期',
                position: 'middle'
              }
            }
          };
        })
      };
      break;

    case 'zodiac': // 按生肖分组
      // 创建生肖分组数据
      const zodiacGroups = {};
      GameRules2025.ZODIAC_LIST.forEach(zodiac => {
        zodiacGroups[zodiac] = {
          numbers: [],
          values: [],
          color: zodiacColors[zodiac] || '#999',
          avg: 0,
          max: 0,
          min: Infinity,
          icon: zodiacIcons[zodiac] || ''
        };
      });

      // 按生肖分组号码
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(missingData[numStr] || 0);
        const zodiac = GameRules2025.getZodiac(i);
        const zodiacGroup = zodiacGroups[zodiac];

        zodiacGroup.numbers.push(numStr);
        zodiacGroup.values.push(value);

        // 更新统计数据
        zodiacGroup.max = Math.max(zodiacGroup.max, value);
        if (value > 0) {
          zodiacGroup.min = Math.min(zodiacGroup.min, value);
        }
        zodiacGroup.avg += value;
      }

      // 计算平均值
      Object.keys(zodiacGroups).forEach(zodiac => {
        const group = zodiacGroups[zodiac];
        if (group.values.length > 0) {
          group.avg = Math.round(group.avg / group.values.length);
          if (group.min === Infinity) group.min = 0;
        }
      });

      subtitle = '按生肖分组显示号码遗漏情况';

      // 创建图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const zodiac = params.name;
            const value = params.value;
            return `${zodiac} ${zodiacIcons[zodiac] || ''}<br>平均遗漏: ${value}期`;
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: GameRules2025.ZODIAC_LIST,
          name: '生肖',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            formatter: function(value) {
              return zodiacIcons[value] || value; // 只显示生肖图标或名称
            },
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        yAxis: {
          type: 'value',
          name: '平均遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: [
          {
            name: '平均遗漏',
            type: 'bar',
            barWidth: '50%',
            data: GameRules2025.ZODIAC_LIST.map(zodiac => {
              // 确保有有效值
              const avgValue = zodiacGroups[zodiac].avg || 0;

              return {
                name: zodiac,
                value: avgValue,
                itemStyle: {
                  color: zodiacGroups[zodiac].color,
                  opacity: 0.7
                }
              };
            }),
            label: {
              show: showMissingTips.value,
              position: 'top',
              formatter: '{c}期',
              fontSize: 12,
              color: '#333'
            }
          }
        ]
      };
      break;

    default: // 按号码排序（默认）
      // 创建柱状图数据
      const barData = [];
      const xAxisData = [];
      const colorData = [];

      // 按号码排序
      for (let i = 1; i <= 49; i++) {
        const numStr = String(i);
        const value = parseInt(missingData[numStr] || 0);

        // 获取号码对应的波色
        let color = '#999999';
        if (GameRules2025.RED_NUMBERS.includes(i)) color = '#ff4d4f';
        if (GameRules2025.BLUE_NUMBERS.includes(i)) color = '#1890ff';
        if (GameRules2025.GREEN_NUMBERS.includes(i)) color = '#52c41a';

        barData.push(value);
        xAxisData.push(numStr);
        colorData.push(color);
      }

      subtitle = '各号码自上次开出后的遗漏期数';

      // 设置图表配置
      option = {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          },
          subtext: subtitle,
          subtextStyle: {
            fontSize: 12,
            color: '#666'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const index = params[0].dataIndex;
            const num = xAxisData[index];
            const value = params[0].value;
            return `号码: ${num}<br>遗漏期数: ${value}期`;
          },
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          name: '号码',
          nameLocation: 'middle',
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            interval: 0,
            rotate: 0, // 不旋转标签
            fontSize: 12,
            formatter: function(value) {
              // 简化显示，只显示号码
              return value;
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '遗漏期数',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            formatter: '{value}期'
          }
        },
        series: [{
          name: '遗漏期数',
          type: 'bar',
          data: barData && barData.length > 0 ? barData : [0, 0, 0], // 确保至少有一些数据
          itemStyle: {
            color: function(params) {
              return colorData && colorData.length > 0 ? colorData[params.dataIndex] : '#1890ff';
            },
            borderRadius: [3, 3, 0, 0]
          },
          label: {
            show: showMissingTips.value,
            position: 'top',
            formatter: '{c}期',
            fontSize: 10,
            color: '#333'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          barWidth: '60%',
          barMaxWidth: 15,
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ],
            label: {
              formatter: '平均: {c}期',
              position: 'middle'
            }
          }
        }]
      };
  }

  // 美化：如数据全为0，则显示水印或空态提示
if (
  (missingViewType.value === 'number' && (!option.series[0].data || option.series[0].data.every(v => v === 0))) ||
  (missingViewType.value === 'color' && option.series.every(s => s.data.every(v => v === 0))) ||
  (missingViewType.value === 'zodiac' && (!option.series[0].data || option.series[0].data.every(d => d.value === 0)))
) {
  option.graphic = [
    {
      type: 'text',
      left: 'center',
      top: 'middle',
      style: {
        text: '暂无数据',
        fontSize: 28,
        fontWeight: 'bold',
        fill: '#bbb',
        opacity: 0.7
      }
    }
  ];
}

// 美化：统一背景、圆角、阴影
option.backgroundColor = '#f8fafc';
option.grid = option.grid || {};
option.grid.borderRadius = 10;
option.grid.shadowColor = 'rgba(0,0,0,0.05)';
option.grid.shadowBlur = 8;

charts.value.missingAnalysis.setOption(option, true);
}

// 修改组合分析相关的响应式变量
const selectedCombinations = ref(['wuxing', 'oddEven', 'bigSmall'])
const combinationCharts = ref({})

// 更新组合分析图表的函数
const updateCombinationAnalysisCharts = (data) => {
  // 清除旧的图表实例
  Object.values(charts.value.combinationAnalysis || {}).forEach(chart => {
    if (chart) {
      chart.dispose()
    }
  })
  charts.value.combinationAnalysis = {}

  // 添加调试日志
  console.log('Updating combination charts with data:', data)

  // 为每个选中的类型创建新的图表
  nextTick(() => {
    selectedCombinations.value.forEach(type => {
      if (combinationCharts.value[type]) {
        const chart = echarts.init(combinationCharts.value[type])
        charts.value.combinationAnalysis[type] = chart

        let combinations = {}
        let title = ''
        let colors = []

        switch (type) {
          case 'wuxing':
            combinations = data.wuxingFrequency || {}
            title = '特码五行分布'
            colors = ['#FFD700', '#90EE90', '#87CEEB', '#FF6B6B', '#DEB887']
            break
          case 'domestic':
            combinations = {
              '家禽': data.attributes?.['家禽'] || 0,
              '野兽': data.attributes?.['野兽'] || 0
            }
            title = '特码家野分布'
            colors = ['#95de64', '#ff7875']
            break
          case 'oddEven':
            combinations = {
              '单': data.attributes?.['单'] || 0,
              '双': data.attributes?.['双'] || 0
            }
            title = '特码单双分布'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'bigSmall':
            combinations = {
              '大': data.attributes?.['大'] || 0,
              '小': data.attributes?.['小'] || 0
            }
            title = '特码大小分布'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'tailOddEven':
            combinations = {
              '尾单': data.attributes?.['尾单'] || 0,
              '尾双': data.attributes?.['尾双'] || 0
            }
            title = '特码尾数单双'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'tailBigSmall':
            combinations = {
              '尾大': data.attributes?.['尾大'] || 0,
              '尾小': data.attributes?.['尾小'] || 0
            }
            title = '特码尾数大小'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'sumOddEven':
            combinations = {
              '合单': data.attributes?.['合单'] || 0,
              '合双': data.attributes?.['合双'] || 0
            }
            title = '特码合数单双'
            colors = ['#ff4d4f', '#1890ff']
            break
          case 'sumBigSmall':
            combinations = {
              '合大': data.attributes?.['合大'] || 0,
              '合小': data.attributes?.['合小'] || 0
            }
            title = '特码合数大小'
            colors = ['#ff4d4f', '#1890ff']
            break
        }

        // 添加调试日志
        console.log(`${title} data:`, combinations)

        const total = Object.values(combinations).reduce((a, b) => a + b, 0)
        const option = {
          title: {
            text: title,
            subtext: `总计：${total}期`,
            left: 'center',
            top: 10,
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333'
            },
            subtextStyle: {
              fontSize: 12,
              color: '#666',
              align: 'center',
              padding: [5, 0, 0, 0]  // 增加上边距，避免被遮挡
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
              return `${params.name}<br/>出现次数：${params.value}次<br/>占比：${percentage}%`
            },
            backgroundColor: 'rgba(255,255,255,0.9)',
            borderColor: '#ccc',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            }
          },
          legend: {
            orient: 'horizontal',
            bottom: 10,
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          series: [{
            name: title,
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: true,
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: (params) => {
                const percentage = total > 0 ? ((params.value / total) * 100).toFixed(2) : '0.00'
                return `${params.name}\n${params.value}次\n(${percentage}%)`
              },
              fontSize: 12,
              color: '#333',
              fontWeight: 'bold'
            },
            labelLine: {
              length: 15,
              length2: 10,
              smooth: true
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0,0,0,0.5)'
              }
            },
            data: Object.entries(combinations).map(([key, value], index) => ({
              name: key,
              value: value,
              itemStyle: {
                color: colors[index],
                shadowBlur: 5,
                shadowColor: 'rgba(0,0,0,0.2)'
              }
            })),
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicInOut'
          }]
        }

        chart.setOption(option)
      }
    })
  })
}

// 获取号码近期走势
const getNumberTrend = (number) => {
  // 获取最近10期的走势数据
  const trendLength = 10
  const result = new Array(trendLength).fill(0)

  // 如果有历史数据，则根据历史数据生成走势
  if (basicStats.value?.drawHistory && basicStats.value.drawHistory.length > 0) {
    // 使用drawHistory数据
    const recentDraws = basicStats.value.drawHistory.slice(0, trendLength)

    // 遍历最近的开奖结果
    recentDraws.forEach((draw, index) => {
      if (draw && parseInt(draw.special_number) === parseInt(number)) {
        result[index] = 1 // 1表示该期出现了这个号码
      }
    })
  } else if (basicStats.value?.recentDraws && basicStats.value.recentDraws.length > 0) {
    // 兼容旧版数据结构
    const recentDraws = basicStats.value.recentDraws.slice(0, trendLength)

    // 遍历最近的开奖结果
    recentDraws.forEach((draw, index) => {
      if (draw && parseInt(draw.special_number) === parseInt(number)) {
        result[index] = 1 // 1表示该期出现了这个号码
      }
    })
  } else {
    // 没有历史数据时，生成随机走势（仅用于演示）
    for (let i = 0; i < trendLength; i++) {
      // 随机生成0或1，模拟号码是否出现
      result[i] = Math.random() > 0.8 ? 1 : 0
    }
  }

  return result
}

// 计算号码热度指数 (0-100)
const getHotIndex = (number) => {
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0

  // 计算热度指数的逻辑:
  // 1. 出现次数越多，热度越高
  // 2. 当前遗漏越小，热度越高
  // 3. 最大遗漏越大，热度越低

  // 获取所有号码的出现次数、当前遗漏和最大遗漏
  const allNumbers = Array.from({ length: 49 }, (_, i) => i + 1)
  const allCounts = allNumbers.map(n => parseInt(basicStats.value?.numberFrequency?.[n.toString()]) || 0)
  const allMissingCounts = allNumbers.map(n => parseInt(basicStats.value?.missing?.current?.[n.toString()]) || 0)

  // 计算最大值和最小值，用于归一化
  const maxCount = Math.max(...allCounts, 1)
  const maxMissingCount = Math.max(...allMissingCounts, 1)

  // 归一化各指标 (0-1范围)
  const normalizedCount = count / maxCount
  const normalizedMissing = 1 - (missingCount / maxMissingCount) // 遗漏越小，值越大

  // 计算综合热度指数 (0-100)
  const hotIndex = Math.round((normalizedCount * 0.6 + normalizedMissing * 0.4) * 100)

  return Math.min(Math.max(hotIndex, 0), 100) // 确保在0-100范围内
}

// 获取热度指数的颜色
const getHotIndexColor = (number) => {
  const hotIndex = getHotIndex(number)

  // 根据热度指数返回不同的颜色
  if (hotIndex >= 80) {
    return '#ff4d4f' // 热号 - 红色
  } else if (hotIndex >= 60) {
    return '#faad14' // 温号 - 橙色
  } else if (hotIndex >= 40) {
    return '#52c41a' // 平号 - 绿色
  } else if (hotIndex >= 20) {
    return '#1890ff' // 冷号 - 蓝色
  } else {
    return '#8c8c8c' // 极冷号 - 灰色
  }
}

// 新增：获取开出概率
const getProbability = (number) => {
  const numStr = number.toString()
  const numberData = basicStats.value?.numberFrequency || {}
  const count = parseInt(numberData[numStr]) || 0

  // 计算总期数
  const totalDraws = Object.values(numberData).reduce((sum, val) => sum + (parseInt(val) || 0), 0)

  if (totalDraws === 0) return 0

  // 理论概率是 1/49 ≈ 2.04%
  const actualProbability = (count / totalDraws) * 100
  return actualProbability.toFixed(1)
}

// 新增：获取概率颜色
const getProbabilityColor = (number) => {
  const probability = parseFloat(getProbability(number))
  const theoreticalProbability = 100 / 49 // 约2.04%

  if (probability > theoreticalProbability * 1.5) {
    return '#ff4d4f' // 高于理论值50%以上 - 红色
  } else if (probability > theoreticalProbability * 1.2) {
    return '#faad14' // 高于理论值20%以上 - 橙色
  } else if (probability > theoreticalProbability * 0.8) {
    return '#52c41a' // 接近理论值 - 绿色
  } else if (probability > theoreticalProbability * 0.5) {
    return '#1890ff' // 低于理论值50%以上 - 蓝色
  } else {
    return '#8c8c8c' // 极低概率 - 灰色
  }
}

// 新增：获取回补指数
const getReboundIndex = (number) => {
  const numStr = number.toString()
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0

  // 计算回补指数：基于当前遗漏、历史最大遗漏和出现频率
  if (maxMissing === 0) return 0

  const missingRatio = missingCount / maxMissing
  const frequency = count > 0 ? count : 1

  // 遗漏越接近历史最大值，回补指数越高
  // 出现频率越高的号码，回补期望越强
  const reboundIndex = Math.min(100, (missingRatio * 70) + (frequency * 2))

  return Math.round(reboundIndex)
}

// 新增：获取回补标签类型
const getReboundTagType = (number) => {
  const index = getReboundIndex(number)

  if (index >= 80) return 'danger'    // 极高回补
  if (index >= 60) return 'warning'   // 高回补
  if (index >= 40) return 'success'   // 中等回补
  if (index >= 20) return 'info'      // 低回补
  return ''                           // 极低回补
}

// 新增：获取回补描述
const getReboundDesc = (number) => {
  const index = getReboundIndex(number)

  if (index >= 80) return '极高'
  if (index >= 60) return '高'
  if (index >= 40) return '中等'
  if (index >= 20) return '低'
  return '极低'
}

// 新增：获取稳定性评级
const getStabilityRating = (number) => {
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0

  // 计算稳定性：基于出现频率的一致性和遗漏的规律性
  let stability = 0

  // 基础分：出现次数
  if (count >= 6) stability += 2      // 高频出现
  else if (count >= 3) stability += 1 // 中频出现

  // 遗漏稳定性
  if (maxMissing > 0) {
    const missingStability = 1 - (missingCount / maxMissing)
    stability += missingStability * 2
  }

  // 热度稳定性
  const hotIndex = getHotIndex(number)
  if (hotIndex >= 40 && hotIndex <= 60) {
    stability += 1 // 热度适中更稳定
  }

  return Math.min(5, Math.max(1, Math.round(stability)))
}

// ==================== 位置组合分析函数 ====================

// 获取位置统计（平码 vs 特码）
const getPositionStats = (number) => {
  // 模拟数据，实际应该从API获取
  const numStr = number.toString()
  const totalCount = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0

  // 假设特码占总数的1/7（因为7个号码中1个是特码）
  const specialCount = Math.round(totalCount * 0.15) // 约15%作为特码
  const regularCount = totalCount - specialCount

  return {
    regular: regularCount,
    special: specialCount,
    total: totalCount
  }
}

// 获取连号关联强度
const getAdjacentStrength = (number) => {
  // 计算与相邻号码的关联强度
  const adjacent = [number - 2, number - 1, number + 1, number + 2].filter(n => n >= 1 && n <= 49)
  let strength = 0

  adjacent.forEach(adjNum => {
    const adjCount = parseInt(basicStats.value?.numberFrequency?.[adjNum.toString()]) || 0
    const currentCount = parseInt(basicStats.value?.numberFrequency?.[number.toString()]) || 0

    // 如果相邻号码出现频率相近，关联强度增加
    if (Math.abs(adjCount - currentCount) <= 1) {
      strength++
    }
  })

  return Math.min(4, strength)
}

// 获取连号关联描述
const getAdjacentDesc = (number) => {
  const strength = getAdjacentStrength(number)
  const descriptions = ['无关联', '弱关联', '中关联', '强关联', '极强关联']
  return descriptions[strength] || '无关联'
}

// ==================== 数学特征分析函数 ====================

// 判断是否为质数
const isPrime = (number) => {
  if (number < 2) return false
  if (number === 2) return true
  if (number % 2 === 0) return false

  for (let i = 3; i <= Math.sqrt(number); i += 2) {
    if (number % i === 0) return false
  }
  return true
}

// 判断是否为完全平方数
const isPerfectSquare = (number) => {
  const sqrt = Math.sqrt(number)
  return sqrt === Math.floor(sqrt)
}

// 计算数字根
const getDigitalRoot = (number) => {
  while (number >= 10) {
    number = Math.floor(number / 10) + (number % 10)
  }
  return number
}

// ==================== 波动趋势分析函数 ====================

// 获取近期热度
const getRecentHotness = (number, periods) => {
  // 模拟计算近期热度，实际应该基于历史数据
  const numStr = number.toString()
  const totalCount = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0
  const totalPeriods = 144 // 2025年总期数

  // 假设近期表现与总体表现的比较
  const expectedInPeriods = (totalCount / totalPeriods) * periods
  const actualInPeriods = Math.max(0, Math.min(periods, Math.round(expectedInPeriods * (0.8 + Math.random() * 0.4))))

  return Math.round((actualInPeriods / periods) * 100)
}

// 获取趋势颜色
const getTrendColor = (percentage) => {
  if (percentage >= 80) return '#ff4d4f'
  if (percentage >= 60) return '#faad14'
  if (percentage >= 40) return '#52c41a'
  if (percentage >= 20) return '#1890ff'
  return '#8c8c8c'
}

// 获取变异系数
const getVariationCoefficient = (number) => {
  // 模拟变异系数计算
  const hotIndex = getHotIndex(number)
  const variation = Math.abs(50 - hotIndex) / 50
  return (variation * 100).toFixed(1) + '%'
}

// ==================== 关联分析函数 ====================

// 获取生肖组合强度
const getZodiacCombinationStrength = (number) => {
  const zodiac = GameRules2025.getZodiac(number)
  // 模拟生肖组合分析
  const zodiacCounts = {
    '鼠': 3, '牛': 4, '虎': 2, '兔': 5, '龙': 3, '蛇': 4,
    '马': 6, '羊': 2, '猴': 4, '鸡': 3, '狗': 5, '猪': 3
  }

  const count = zodiacCounts[zodiac] || 3
  if (count >= 5) return 'danger'
  if (count >= 4) return 'warning'
  if (count >= 3) return 'success'
  return 'info'
}

// 获取生肖组合等级
const getZodiacCombinationLevel = (number) => {
  const strength = getZodiacCombinationStrength(number)
  const levels = {
    'danger': '强',
    'warning': '中',
    'success': '弱',
    'info': '无'
  }
  return levels[strength] || '无'
}

// 获取波色搭配强度
const getColorCombinationStrength = (number) => {
  const color = GameRules2025.getColor(number)
  // 模拟波色搭配分析
  const colorBalance = {
    '红波': 'warning',
    '蓝波': 'success',
    '绿波': 'info'
  }
  return colorBalance[color] || 'info'
}

// 获取波色搭配等级
const getColorCombinationLevel = (number) => {
  const strength = getColorCombinationStrength(number)
  const levels = {
    'danger': '强',
    'warning': '中',
    'success': '弱',
    'info': '无'
  }
  return levels[strength] || '无'
}

// ==================== 预测指标函数 ====================

// 获取期望遗漏
const getExpectedMissing = (number) => {
  // 理论期望遗漏 = 总号码数 - 1 = 48期
  const theoreticalExpected = 48
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0

  // 根据实际出现频率调整期望遗漏
  if (count === 0) return theoreticalExpected

  const totalPeriods = 144
  const actualFrequency = count / totalPeriods
  const expectedFrequency = 1 / 49

  const adjustedExpected = Math.round(theoreticalExpected * (expectedFrequency / actualFrequency))
  return Math.max(1, Math.min(100, adjustedExpected))
}

// 获取偏差等级
const getDeviationLevel = (number) => {
  const expected = getExpectedMissing(number)
  const numStr = number.toString()
  const currentMissing = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0

  const deviation = Math.abs(currentMissing - expected) / expected

  if (deviation >= 0.5) return 'danger'
  if (deviation >= 0.3) return 'warning'
  if (deviation >= 0.1) return 'success'
  return 'info'
}

// 获取偏差值
const getDeviationValue = (number) => {
  const expected = getExpectedMissing(number)
  const numStr = number.toString()
  const currentMissing = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0

  const deviation = ((currentMissing - expected) / expected * 100).toFixed(0)
  return `${deviation > 0 ? '+' : ''}${deviation}%`
}

// ==================== 特殊模式分析函数 ====================

// 获取重号模式
const getRepeatPattern = (number) => {
  // 模拟重号分析（连续期数开出相同号码）
  return Math.floor(Math.random() * 3) // 0-2次
}

// 获取跳号模式
const getJumpPattern = (number) => {
  // 模拟跳号分析（隔期开出）
  return Math.floor(Math.random() * 4) // 0-3次
}

// 获取周期强度
const getCycleStrength = (number) => {
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0

  // 根据出现频率判断周期性
  if (count >= 6) return 'danger'
  if (count >= 4) return 'warning'
  if (count >= 2) return 'success'
  return 'info'
}

// 获取周期等级
const getCycleLevel = (number) => {
  const strength = getCycleStrength(number)
  const levels = {
    'danger': '强',
    'warning': '中',
    'success': '弱',
    'info': '无'
  }
  return levels[strength] || '无'
}

// ==================== 综合评分函数 ====================

// 获取投注价值评分
const getInvestmentValue = (number) => {
  const numStr = number.toString()
  const count = parseInt(basicStats.value?.numberFrequency?.[numStr]) || 0
  const currentMissing = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 1

  let score = 0

  // 基于出现频率 (30%)
  const hotIndex = getHotIndex(number)
  score += (hotIndex / 100) * 30

  // 基于回补指数 (25%)
  const reboundIndex = getReboundIndex(number)
  score += (reboundIndex / 100) * 25

  // 基于稳定性 (20%)
  const stability = getStabilityRating(number)
  score += (stability / 5) * 20

  // 基于遗漏情况 (15%)
  const missingScore = maxMissing > 0 ? (1 - currentMissing / maxMissing) * 15 : 0
  score += missingScore

  // 基于数学特征 (10%)
  const mathScore = isPrime(number) ? 5 : 0 // 质数加分
  score += mathScore + (isPerfectSquare(number) ? 5 : 0) // 平方数加分

  return Math.round(Math.min(100, Math.max(0, score)))
}

// 获取评分颜色
const getScoreColor = (score) => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#faad14'
  if (score >= 40) return '#52c41a'
  if (score >= 20) return '#1890ff'
  return '#8c8c8c'
}

// 获取风险等级
const getRiskLevel = (number) => {
  const numStr = number.toString()
  const currentMissing = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 1

  // 风险基于遗漏情况和稳定性
  const missingRatio = currentMissing / maxMissing
  const stability = getStabilityRating(number)

  let risk = 1
  if (missingRatio > 0.8 || stability <= 2) risk = 3 // 高风险
  else if (missingRatio > 0.5 || stability <= 3) risk = 2 // 中风险

  return risk
}

// 获取活跃度等级
const getActivityLevel = (number) => {
  const recentHotness = getRecentHotness(number, 10)

  if (recentHotness >= 60) return 3 // 高活跃
  if (recentHotness >= 30) return 2 // 中活跃
  return 1 // 低活跃
}

// 显示号码走势图
const showNumberTrend = async (number) => {
  try {
    // 获取最近30期的历史数据
    const response = await store.fetchDrawHistory({ page: 1, page_size: 30 })
    let recentDraws = []

    if (response && response.data && response.data.items) {
      recentDraws = response.data.items
    } else {
      // 如果API调用失败，尝试从basicStats获取数据
      if (basicStats.value?.drawHistory && basicStats.value.drawHistory.length > 0) {
        recentDraws = basicStats.value.drawHistory.slice(0, 30)
      }
    }

    // 准备走势数据
    const trendData = []
    const periodLabels = []
    const hitData = []
    const missData = []

    if (recentDraws.length > 0) {
      // 按期号排序（从旧到新）
      const sortedDraws = recentDraws.sort((a, b) => {
        const expectA = parseInt(a.expect || '0')
        const expectB = parseInt(b.expect || '0')
        return expectA - expectB
      })

      sortedDraws.forEach((draw, index) => {
        if (draw) {
          const drawNumber = parseInt(draw.special_number || draw.number)
          const isHit = drawNumber === parseInt(number)
          const period = draw.expect || `期${index + 1}`

          periodLabels.push(period)
          trendData.push(isHit ? 1 : 0)

          if (isHit) {
            hitData.push({ name: period, value: [index, 1], symbolSize: 15 })
            missData.push({ name: period, value: [index, 0], symbolSize: 0 })
          } else {
            hitData.push({ name: period, value: [index, 1], symbolSize: 0 })
            missData.push({ name: period, value: [index, 0], symbolSize: 8 })
          }
        }
      })
    } else {
      // 生成模拟数据用于演示
      for (let i = 1; i <= 30; i++) {
        const isHit = Math.random() > 0.85 // 15%的命中率
        const period = `2025${String(i).padStart(3, '0')}`

        periodLabels.push(period)
        trendData.push(isHit ? 1 : 0)

        if (isHit) {
          hitData.push({ name: period, value: [i-1, 1], symbolSize: 15 })
          missData.push({ name: period, value: [i-1, 0], symbolSize: 0 })
        } else {
          hitData.push({ name: period, value: [i-1, 1], symbolSize: 0 })
          missData.push({ name: period, value: [i-1, 0], symbolSize: 8 })
        }
      }
    }

    // 计算统计信息
    const totalPeriods = trendData.length
    const hitCount = trendData.filter(hit => hit === 1).length
    const hitRate = totalPeriods > 0 ? ((hitCount / totalPeriods) * 100).toFixed(1) : '0.0'
    const currentMissing = getCurrentMissing(number, trendData)

    // 使用简单的HTML内容
    const htmlContent = `
      <div style="width: 100%; height: 600px; padding: 15px; background: #f8f9fa;">
        <!-- 统计信息卡片 -->
        <div style="display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
          <div style="flex: 1; min-width: 120px; background: #fff; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #1890ff;">${hitCount}</div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">出现次数</div>
          </div>
          <div style="flex: 1; min-width: 120px; background: #fff; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">${hitRate}%</div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">命中率</div>
          </div>
          <div style="flex: 1; min-width: 120px; background: #fff; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #faad14;">${currentMissing}</div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">当前遗漏</div>
          </div>
          <div style="flex: 1; min-width: 120px; background: #fff; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
            <div style="font-size: 24px; font-weight: bold; color: #722ed1;">${totalPeriods}</div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">统计期数</div>
          </div>
        </div>

        <!-- 图表容器 -->
        <div style="background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); padding: 20px;">
          <div id="trendChartContainer" style="width: 100%; height: 400px;"></div>
        </div>
      </div>
    `

    // 显示对话框
    ElMessageBox.alert(
      htmlContent,
      `号码 ${number} 走势分析`,
      {
        dangerouslyUseHTMLString: true,
        customClass: 'number-trend-dialog',
        confirmButtonText: '关闭',
        callback: () => {
          // 清理图表实例
          if (window.tempTrendChart) {
            window.tempTrendChart.dispose()
            window.tempTrendChart = null
          }
        }
      }
    )

    // 在下一个tick初始化图表
    nextTick(() => {
      // 延迟初始化图表，确保DOM已经渲染完成并有宽高
      setTimeout(() => {
        const chartDom = document.getElementById('trendChartContainer')
        if (!chartDom) {
          console.error('图表容器未找到')
          return
        }

        // 初始化图表
        const chart = echarts.init(chartDom)
        window.tempTrendChart = chart // 保存图表实例以便后续清理

        // 设置图表选项
        const option = {
          title: {
            text: `号码 ${number} 走势分析`,
            subtext: `统计最近${totalPeriods}期 | 命中率${hitRate}%`,
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333'
            },
            subtextStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            },
            formatter: function(params) {
              const period = params[0].name
              const isHit = trendData[params[0].dataIndex] === 1
              return `
                <div style="padding: 8px;">
                  <div style="font-weight: bold; margin-bottom: 5px;">期号: ${period}</div>
                  <div style="color: ${isHit ? '#52c41a' : '#ff4d4f'};">
                    ${isHit ? `✓ 号码${number}开出` : `✗ 号码${number}未开出`}
                  </div>
                </div>
              `
            }
          },
          legend: {
            data: ['开出', '未开出'],
            top: 40,
            textStyle: {
              fontSize: 12
            }
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '20%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: periodLabels,
            axisLabel: {
              interval: Math.floor(periodLabels.length / 10), // 显示部分标签避免重叠
              rotate: 45,
              fontSize: 10
            },
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            }
          },
          yAxis: {
            type: 'value',
            min: -0.2,
            max: 1.2,
            interval: 1,
            axisLabel: {
              formatter: function(value) {
                return value === 1 ? '开出' : value === 0 ? '未开出' : ''
              },
              fontSize: 10
            },
            axisLine: {
              lineStyle: {
                color: '#ddd'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: '开出',
              type: 'scatter',
              data: hitData,
              symbolSize: function(data) {
                return data[2] || 15
              },
              itemStyle: {
                color: '#52c41a',
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(82, 196, 26, 0.3)',
                shadowBlur: 5
              },
              label: {
                show: true,
                position: 'top',
                formatter: number.toString(),
                color: '#52c41a',
                fontSize: 12,
                fontWeight: 'bold'
              }
            },
            {
              name: '未开出',
              type: 'scatter',
              data: missData,
              symbolSize: function(data) {
                return data[2] || 8
              },
              itemStyle: {
                color: '#f0f0f0',
                borderColor: '#d9d9d9',
                borderWidth: 1
              }
            }
          ]
        }

    // 设置图表
    chart.setOption(option)

    // 监听窗口大小变化，调整图表大小
    const resizeHandler = function() {
      if (window.tempTrendChart) {
        window.tempTrendChart.resize()
      }
    }

    window.addEventListener('resize', resizeHandler)

    // 在对话框关闭时移除事件监听器
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.removedNodes.length > 0) {
          if (Array.from(mutation.removedNodes).some(node =>
            node.classList && node.classList.contains('number-trend-dialog'))) {
            window.removeEventListener('resize', resizeHandler)
            observer.disconnect()
          }
        }
      }
    })

    observer.observe(document.body, { childList: true, subtree: true })
        }, 300) // 延迟300ms确保DOM已完全渲染
      })
    } catch (error) {
      console.error('显示走势图失败:', error)
      ElMessage.error('显示走势图失败，请稍后重试')
    }
  }

  // 辅助函数：计算当前遗漏
  const getCurrentMissing = (number, trendData) => {
    let missing = 0
    for (let i = trendData.length - 1; i >= 0; i--) {
      if (trendData[i] === 1) {
        break
      }
      missing++
    }
    return missing
  }

// 显示号码详情的方法
const showNumberDetail = (number) => {
  const numStr = number.toString()
  const numberData = basicStats.value?.numberFrequency || {}
  const count = parseInt(numberData[numStr]) || 0
  const missingCount = parseInt(basicStats.value?.missing?.current?.[numStr]) || 0
  const maxMissing = parseInt(basicStats.value?.missing?.max?.[numStr]) || 0
  const zodiac = GameRules2025.getZodiac(number)
  const color = GameRules2025.getColor(number)
  const element = GameRules2025.getWuxing(number)

  // 计算出现概率
  const totalDraws = Object.values(numberData).reduce((sum, val) => sum + (parseInt(val) || 0), 0)
  const probability = totalDraws > 0 ? ((count / totalDraws) * 100).toFixed(2) + '%' : '0%'

  // 计算热度指数
  const hotIndex = getHotIndex(number)
  const hotLevel = hotIndex >= 80 ? '热号' :
                  hotIndex >= 60 ? '温号' :
                  hotIndex >= 40 ? '平号' :
                  hotIndex >= 20 ? '冷号' : '极冷号'

  // 计算号码属性
  const isOdd = number % 2 === 1
  const isBig = number > 24
  const tailNumber = number % 10
  const isTailOdd = tailNumber % 2 === 1
  const isTailBig = tailNumber >= 5

  // 计算合数
  const sumDigits = number < 10 ? number : Math.floor(number / 10) + (number % 10)
  const isSumOdd = sumDigits % 2 === 1
  const isSumBig = sumDigits >= 7

  // 显示详情对话框
  ElMessageBox.alert(
    `<div class="number-detail">
      <h3>号码 ${number} 详细信息</h3>
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="detail-item"><span class="label">出现次数:</span> <span class="value">${count} 次</span></div>
        <div class="detail-item"><span class="label">出现概率:</span> <span class="value">${probability}</span></div>
        <div class="detail-item"><span class="label">当前遗漏:</span> <span class="value">${missingCount} 期</span></div>
        <div class="detail-item"><span class="label">最大遗漏:</span> <span class="value">${maxMissing} 期</span></div>
        <div class="detail-item"><span class="label">热度指数:</span> <span class="value" style="color: ${getHotIndexColor(number)}">${hotIndex} (${hotLevel})</span></div>
      </div>

      <div class="detail-section">
        <h4>属性分析</h4>
        <div class="detail-item"><span class="label">生肖:</span> <span class="value">${zodiacIcons[zodiac]} ${zodiac}</span></div>
        <div class="detail-item"><span class="label">波色:</span> <span class="value" style="color: ${color === '红波' ? '#ff4d4f' : color === '蓝波' ? '#1890ff' : '#52c41a'}">${color}</span></div>
        <div class="detail-item"><span class="label">五行:</span> <span class="value" style="color: ${getWuxingColor(element)}">${element}</span></div>
        <div class="detail-item"><span class="label">单双:</span> <span class="value">${isOdd ? '单数' : '双数'}</span></div>
        <div class="detail-item"><span class="label">大小:</span> <span class="value">${isBig ? '大数' : '小数'}</span></div>
      </div>

      <div class="detail-section">
        <h4>尾数分析</h4>
        <div class="detail-item"><span class="label">尾数:</span> <span class="value">${tailNumber}</span></div>
        <div class="detail-item"><span class="label">尾数单双:</span> <span class="value">${isTailOdd ? '尾单' : '尾双'}</span></div>
        <div class="detail-item"><span class="label">尾数大小:</span> <span class="value">${isTailBig ? '尾大' : '尾小'}</span></div>
      </div>

      <div class="detail-section">
        <h4>合数分析</h4>
        <div class="detail-item"><span class="label">合数:</span> <span class="value">${sumDigits}</span></div>
        <div class="detail-item"><span class="label">合数单双:</span> <span class="value">${isSumOdd ? '合单' : '合双'}</span></div>
        <div class="detail-item"><span class="label">合数大小:</span> <span class="value">${isSumBig ? '合大' : '合小'}</span></div>
      </div>
    </div>`,
    '号码详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭',
      customClass: 'number-detail-dialog'
    }
  )
}

// 过滤和排序后的数据
const filteredAndSortedData = computed(() => {
  let data = advancedAnalysisData.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    data = data.filter(item => {
      return item.number.toString().includes(query) ||
             item.zodiac.toLowerCase().includes(query) ||
             item.color.toLowerCase().includes(query) ||
             item.element.toLowerCase().includes(query) ||
             item.oddEven.toLowerCase().includes(query) ||
             item.bigSmall.toLowerCase().includes(query)
    })
  }

  // 类型过滤
  if (filterType.value) {
    switch (filterType.value) {
      case 'hot':
        data = data.filter(item => item.hotIndex >= 60)
        break
      case 'cold':
        data = data.filter(item => item.hotIndex < 40)
        break
      case 'odd':
        data = data.filter(item => item.oddEven === '单')
        break
      case 'even':
        data = data.filter(item => item.oddEven === '双')
        break
      case 'big':
        data = data.filter(item => item.bigSmall === '大')
        break
      case 'small':
        data = data.filter(item => item.bigSmall === '小')
        break
    }
  }

  // 排序
  if (sortField.value) {
    data = [...data].sort((a, b) => {
      let aValue = a[sortField.value]
      let bValue = b[sortField.value]

      // 对于字符串类型，转换为小写进行比较
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder.value === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }

  return data
})

// 计算高级分析数据
const advancedAnalysisData = computed(() => {
  const numbers = Array.from({ length: 49 }, (_, i) => i + 1)
  return numbers.map(number => {
    const numStr = number.toString()
    // 只使用 numberFrequency 字段
    const numberData = basicStats.value?.numberFrequency || {}

    // 计算号码属性
    const isOdd = number % 2 === 1
    const isBig = number > 24
    const tailNumber = number % 10
    const isTailOdd = tailNumber % 2 === 1
    const isTailBig = tailNumber >= 5

    // 计算合数
    const sumDigits = number < 10 ? number : Math.floor(number / 10) + (number % 10)
    const isSumOdd = sumDigits % 2 === 1
    const isSumBig = sumDigits >= 7

    // 计算热度指数
    const hotIndex = getHotIndex(number)

    return {
      number: number,
      count: parseInt(numberData[numStr]) || 0,
      missingCount: parseInt(basicStats.value?.missing?.current?.[numStr]) || 0,
      maxMissing: parseInt(basicStats.value?.missing?.max?.[numStr]) || 0,
      zodiac: GameRules2025.getZodiac(number),
      color: GameRules2025.getColor(number),
      element: GameRules2025.getWuxing(number),
      hotIndex: hotIndex,
      oddEven: isOdd ? '单' : '双',
      bigSmall: isBig ? '大' : '小',
      tailNumber: tailNumber,
      tailOddEven: isTailOdd ? '尾单' : '尾双',
      tailBigSmall: isTailBig ? '尾大' : '尾小',
      sumDigits: sumDigits,
      sumOddEven: isSumOdd ? '合单' : '合双',
      sumBigSmall: isSumBig ? '合大' : '合小'
    }
  })
})

// 监听连号图表类型变化，同步更新指南标签页
watch(consecutiveChartType, (newType) => {
  activeGuideTab.value = newType
  // 当切换图表类型时，重新显示使用说明
  showChartInstructions.value = true
})

// 监听图表类型变化
watch([
  numberFrequencyType,
  colorFrequencyIs3D,
  showTailLabels,
  showHeadLabels,
  zodiacChartType,
  showMissingTips,
  missingViewType, // 添加遗漏分析视图类型
  selectedCombinations,
  consecutiveChartType  // 添加连号分析图表类型
], () => {
  if (basicStats.value) {
    // 只使用 numberFrequency 字段
    updateNumberFrequencyChart(basicStats.value.numberFrequency || {})
    updateColorFrequencyChart(basicStats.value.colorFrequency || {})
    updateTailFrequencyChart(basicStats.value.tailFrequency || {})
    updateHeadFrequencyChart(basicStats.value.headFrequency || {})
    updateZodiacFrequencyChart(basicStats.value.zodiacFrequency || {})
    updateWuxingChart(basicStats.value.wuxingFrequency || {})
    updateConsecutiveChart(basicStats.value)
    updateMissingAnalysisChart(basicStats.value)
    updateCombinationAnalysisCharts(basicStats.value)
  }
})

// 监听窗口大小变化
const handleResize = () => {
  nextTick(() => {
    try {
      Object.entries(charts.value).forEach(([key, chart]) => {
        if (chart && typeof chart.resize === 'function') {
          try {
            // 确保图表有基本配置
            if (!chart.getOption() || !chart.getOption().series || chart.getOption().series.length === 0) {
              console.warn(`Chart ${key} has no valid option, setting default option`);
              // 根据图表类型设置默认配置
              let defaultType = 'bar';
              if (key === 'colorFrequency' || key === 'wuxing') {
                defaultType = 'pie';
              } else if (key === 'consecutive') {
                defaultType = 'line';
              } else if (key === 'missingAnalysis') {
                defaultType = 'heatmap';
              }

              chart.setOption({
                series: [{
                  type: defaultType,
                  data: []
                }]
              });
            }

            // 调整图表大小
            chart.resize();
            console.log(`Resized chart: ${key}`);
          } catch (e) {
            console.error(`Error resizing chart ${key}:`, e);
          }
        } else if (chart) {
          console.warn(`Chart ${key} does not have a resize function`);
        }
      });
    } catch (error) {
      console.error('Error in handleResize:', error);
    }
  });
}

// 获取最新一期信息
const fetchLatestDraw = async () => {
  try {
    const result = await store.fetchLatestDraw()
    if (result && result.data) {
      latestDraw.value = result.data
      console.log('Latest draw info:', latestDraw.value)

      // 如果没有设置结束期数，则使用最新一期的期号
      if (!endPeriod.value && latestDraw.value.expect) {
        const latestPeriodNumber = parseInt(latestDraw.value.expect.substring(4))
        if (!isNaN(latestPeriodNumber)) {
          endPeriod.value = latestPeriodNumber
          console.log(`设置结束期数为最新一期: ${endPeriod.value}`)
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch latest draw:', error)
  }
}

onMounted(() => {
  try {
    // 初始化图表
    initCharts()

    // 设置默认筛选条件：当前年份的第1期到最新期数
    const currentYear = new Date().getFullYear()
    selectedYear.value = currentYear
    startPeriod.value = 1  // 从第1期开始
    endPeriod.value = null // 到最新期数，将在获取最新一期信息后更新

    // 确保年份列表包含当前年份
    if (!years.value.includes(currentYear)) {
      years.value.push(currentYear)
      years.value.sort()
    }

    console.log(`设置默认统计范围: ${currentYear}年第1期至最新期数`)

    // 先获取最新一期信息
    fetchLatestDraw().then(() => {
      // 然后获取统计数据
      fetchStatistics()
    })

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('Error in onMounted:', error)
  }
})

// 组件卸载时的清理逻辑
onBeforeUnmount(() => {
  console.log('Component unmounting, cleaning up resources');

  // 移除事件监听
  window.removeEventListener('resize', handleResize);

  // 销毁所有图表实例
  Object.entries(charts.value).forEach(([key, chart]) => {
    if (chart) {
      try {
        console.log(`Disposing chart: ${key}`);
        // 检查dispose方法是否存在
        if (typeof chart.dispose === 'function') {
          chart.dispose();
        } else {
          console.warn(`Chart ${key} does not have a dispose method`);
        }
      } catch (error) {
        console.error(`Error disposing chart ${key}:`, error);
      }
    }
  });

  // 清空图表引用
  charts.value = {
    numberFrequency: null,
    colorFrequency: null,
    tailFrequency: null,
    headFrequency: null,
    zodiacFrequency: null,
    wuxing: null,
    consecutive: null,
    missingAnalysis: null,
    combinationAnalysis: null
  };

  console.log('All charts disposed and references cleared');
})

// 初始化所有图表
const initCharts = () => {
  nextTick(() => {
    try {
      // 确保在初始化图表前清空之前的实例
      Object.keys(charts.value).forEach(key => {
        if (charts.value[key]) {
          try {
            charts.value[key].dispose();
          } catch (e) {
            console.warn(`Failed to dispose chart ${key}:`, e);
          }
          charts.value[key] = null;
        }
      });

      // 初始化新的图表实例
      if (numberFrequencyChart.value) {
        charts.value.numberFrequency = echarts.init(numberFrequencyChart.value);
        // 设置一个基本的空配置，避免resize错误
        charts.value.numberFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 49 }, (_, i) => (i + 1).toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 49 }, () => 0)
          }]
        });
      }
      if (colorFrequencyChart.value) {
        charts.value.colorFrequency = echarts.init(colorFrequencyChart.value);
        charts.value.colorFrequency.setOption({
          series: [{
            type: 'pie',
            data: [
              { name: '红波', value: 0 },
              { name: '蓝波', value: 0 },
              { name: '绿波', value: 0 }
            ]
          }]
        });
      }
      if (tailFrequencyChart.value) {
        charts.value.tailFrequency = echarts.init(tailFrequencyChart.value);
        charts.value.tailFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 10 }, (_, i) => i.toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 10 }, () => 0)
          }]
        });
      }
      if (headFrequencyChart.value) {
        charts.value.headFrequency = echarts.init(headFrequencyChart.value);
        charts.value.headFrequency.setOption({
          xAxis: {
            type: 'category',
            data: Array.from({ length: 5 }, (_, i) => i.toString())
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: 5 }, () => 0)
          }]
        });
      }
      if (zodiacFrequencyChart.value) {
        charts.value.zodiacFrequency = echarts.init(zodiacFrequencyChart.value);
        charts.value.zodiacFrequency.setOption({
          xAxis: {
            type: 'category',
            data: GameRules2025.ZODIAC_LIST
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            type: 'bar',
            data: Array.from({ length: GameRules2025.ZODIAC_LIST.length }, () => 0)
          }]
        });
      }
      if (wuxingChart.value) {
        charts.value.wuxing = echarts.init(wuxingChart.value);
        charts.value.wuxing.setOption({
          series: [{
            type: 'pie',
            data: [
              { name: '金', value: 0 },
              { name: '木', value: 0 },
              { name: '水', value: 0 },
              { name: '火', value: 0 },
              { name: '土', value: 0 }
            ]
          }]
        });
      }
      if (consecutiveChart.value) {
        charts.value.consecutive = echarts.init(consecutiveChart.value);
        charts.value.consecutive.setOption({
          title: {
            text: '特码连号分析',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          series: [{
            type: 'graph',
            layout: 'force',
            data: Array.from({ length: 49 }, (_, i) => ({
              name: (i + 1).toString(),
              symbolSize: 10,
              value: 0
            })),
            links: [],
            roam: true,
            label: {
              show: true,
              position: 'inside',
              formatter: '{b}',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#fff'
            },
            force: {
              repulsion: 100,
              gravity: 0.1,
              layoutAnimation: true
            }
          }]
        });
      }
      if (missingAnalysisChart.value) {
        charts.value.missingAnalysis = echarts.init(missingAnalysisChart.value);
        charts.value.missingAnalysis.setOption({
          title: {
            text: '特码遗漏分析',
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 'bold'
            },
            subtext: '各号码自上次开出后的遗漏期数',
            subtextStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function(params) {
              const num = params[0].name;
              const value = params[0].value;
              return `号码: ${num}<br>遗漏期数: ${value}期`;
            }
          },
          grid: {
            left: '3%',
            right: '3%',
            bottom: '10%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: Array.from({ length: 49 }, (_, i) => (i + 1).toString()),
            name: '号码',
            nameLocation: 'middle',
            nameGap: 30,
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            },
            axisLabel: {
              interval: 0,
              rotate: 0,
              fontSize: 12
            }
          },
          yAxis: {
            type: 'value',
            name: '遗漏期数',
            nameLocation: 'middle',
            nameGap: 40,
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold'
            },
            axisLabel: {
              formatter: '{value}期'
            }
          },
          series: [{
            name: '遗漏期数',
            type: 'bar',
            data: Array.from({ length: 49 }, () => 0),
            itemStyle: {
              color: function(params) {
                const num = parseInt(params.name);
                if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f';
                if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff';
                return '#52c41a';
              },
              borderRadius: [3, 3, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}期',
              fontSize: 10,
              color: '#333'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: '平均: {c}期',
                position: 'middle'
              }
            }
          }]
        });
      }

      console.log('All charts initialized successfully');
    } catch (error) {
      console.error('Error initializing charts:', error)
    }
  })
}

// 修改图表更新函数
const updateCharts = (data) => {
  if (!data) {
    console.error('No data provided to updateCharts');
    return;
  }

  console.log('Updating charts with data:', data);

  nextTick(() => {
    try {
      // 确保每个图表实例存在并且有数据才更新
      if (charts.value.numberFrequency) {
        if (data.numberFrequency) {
          console.log('Updating number frequency chart with data:', data.numberFrequency);
          updateNumberFrequencyChart(data.numberFrequency);
        } else {
          console.warn('No number frequency data available');
          // 使用空对象更新图表，显示空状态
          updateNumberFrequencyChart({});
        }
      }

      if (charts.value.colorFrequency) {
        if (data.colorFrequency) {
          console.log('Updating color frequency chart with data:', data.colorFrequency);
          updateColorFrequencyChart(data.colorFrequency);
        } else {
          console.warn('No color frequency data available');
          updateColorFrequencyChart({ '红波': 0, '蓝波': 0, '绿波': 0 });
        }
      }

      if (charts.value.tailFrequency) {
        if (data.tailFrequency) {
          console.log('Updating tail frequency chart with data:', data.tailFrequency);
          updateTailFrequencyChart(data.tailFrequency);
        } else {
          console.warn('No tail frequency data available');
          updateTailFrequencyChart(Object.fromEntries(Array.from({ length: 10 }, (_, i) => [String(i), 0])));
        }
      }

      if (charts.value.headFrequency) {
        if (data.headFrequency) {
          console.log('Updating head frequency chart with data:', data.headFrequency);
          updateHeadFrequencyChart(data.headFrequency);
        } else {
          console.warn('No head frequency data available');
          updateHeadFrequencyChart(Object.fromEntries(Array.from({ length: 5 }, (_, i) => [String(i), 0])));
        }
      }

      if (charts.value.zodiacFrequency) {
        if (data.zodiacFrequency) {
          console.log('Updating zodiac frequency chart with data:', data.zodiacFrequency);
          updateZodiacFrequencyChart(data.zodiacFrequency);
        } else {
          console.warn('No zodiac frequency data available');
          updateZodiacFrequencyChart(Object.fromEntries(GameRules2025.ZODIAC_LIST.map(zodiac => [zodiac, 0])));
        }
      }

      if (charts.value.wuxing) {
        if (data.wuxingFrequency) {
          console.log('Updating wuxing chart with data:', data.wuxingFrequency);
          updateWuxingChart(data.wuxingFrequency);
        } else {
          console.warn('No wuxing frequency data available');
          updateWuxingChart({ '金': 0, '木': 0, '水': 0, '火': 0, '土': 0 });
        }
      }

      if (charts.value.consecutive) {
        console.log('Updating consecutive chart with data');
        updateConsecutiveChart(data);
      }

      if (charts.value.missingAnalysis) {
        console.log('Updating missing analysis chart with data');
        updateMissingAnalysisChart(data);
      }
    } catch (error) {
      console.error('Error updating charts:', error);
      ElMessage.error('更新图表时发生错误: ' + error.message);
    }
  })
}

// 基础统计卡片数据
const basicStatsCards = computed(() => [
  {
    title: '特码出现次数',
    value: basicStats.value?.basicStats?.totalCount || 0,
    description: '统计期间总开奖次数',
    icon: 'DataLine',
    color: '#409EFF',
    tooltip: '在选定时间范围内的开奖总次数',
    hover: false,
    unit: '期'
  },
  {
    title: '最热特码',
    value: computed(() => {
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length === 0) return '-';

      const maxCount = hotNumbers[0]?.count;
      if (maxCount === undefined) return '-';

      // 获取所有具有最大出现次数的号码
      const sameCountNumbers = hotNumbers
        .filter(n => n.count === maxCount)
        .map(n => String(n.number).padStart(2, '0'));

      // 如果只有一个号码，直接显示
      if (sameCountNumbers.length === 1) {
        return sameCountNumbers[0];
      }

      // 如果有多个号码，显示前3个，超过3个用省略号
      if (sameCountNumbers.length <= 3) {
        return sameCountNumbers.join(',');
      } else {
        return sameCountNumbers.slice(0, 3).join(',') + '...';
      }
    }),
    description: computed(() => {
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length === 0) return '暂无数据';

      const maxCount = hotNumbers[0]?.count;
      if (maxCount === undefined) return '数据加载中...';

      // 获取所有具有最大出现次数的号码数量
      const sameCountNumbers = hotNumbers.filter(n => n.count === maxCount);

      if (sameCountNumbers.length === 1) {
        return `出现${maxCount}次`;
      } else {
        return `${sameCountNumbers.length}个号码各${maxCount}次`;
      }
    }),
    icon: 'Histogram',
    color: '#F56C6C',
    tooltip: computed(() => {
      const hotNumbers = basicStats.value?.basicStats?.hotNumbers || [];
      if (hotNumbers.length === 0) return '出现次数最多的特码号码';

      const maxCount = hotNumbers[0]?.count;
      const sameCountNumbers = hotNumbers
        .filter(n => n.count === maxCount)
        .map(n => String(n.number).padStart(2, '0'));

      if (sameCountNumbers.length === 1) {
        return `出现次数最多的特码号码: ${sameCountNumbers[0]}`;
      } else {
        return `出现次数最多的特码号码(${maxCount}次): ${sameCountNumbers.join(', ')}`;
      }
    }),
    hover: false,
    unit: '次'
  },
  {
    title: '最冷特码',
    value: computed(() => {
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length === 0) return '-';

      const minCount = coldNumbers[0]?.count;
      if (minCount === undefined) return '-';

      // 获取所有具有最小出现次数的号码
      const sameCountNumbers = coldNumbers
        .filter(n => n.count === minCount)
        .map(n => String(n.number).padStart(2, '0'));

      // 如果只有一个号码，直接显示
      if (sameCountNumbers.length === 1) {
        return sameCountNumbers[0];
      }

      // 如果有多个号码，显示前3个，超过3个用省略号
      if (sameCountNumbers.length <= 3) {
        return sameCountNumbers.join(',');
      } else {
        return sameCountNumbers.slice(0, 3).join(',') + '...';
      }
    }),
    description: computed(() => {
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length === 0) return '暂无数据';

      const minCount = coldNumbers[0]?.count;
      if (minCount === undefined) return '数据加载中...';

      // 获取所有具有最小出现次数的号码数量
      const sameCountNumbers = coldNumbers.filter(n => n.count === minCount);

      if (sameCountNumbers.length === 1) {
        return `出现${minCount}次`;
      } else {
        return `${sameCountNumbers.length}个号码各${minCount}次`;
      }
    }),
    icon: 'TrendCharts',
    color: '#67C23A',
    tooltip: computed(() => {
      const coldNumbers = basicStats.value?.basicStats?.coldNumbers || [];
      if (coldNumbers.length === 0) return '出现次数最少的特码号码';

      const minCount = coldNumbers[0]?.count;
      const sameCountNumbers = coldNumbers
        .filter(n => n.count === minCount)
        .map(n => String(n.number).padStart(2, '0'));

      if (sameCountNumbers.length === 1) {
        return `出现次数最少的特码号码: ${sameCountNumbers[0]}`;
      } else {
        return `出现次数最少的特码号码(${minCount}次): ${sameCountNumbers.join(', ')}`;
      }
    }),
    hover: false,
    unit: '次'
  },
  {
    title: '平均间隔',
    value: computed(() => {
      const val = parseFloat(basicStats.value?.basicStats?.averageInterval);
      return !isNaN(val) && val > 0 ? Math.round(val) : '-';
    }),
    description: '期数',
    icon: 'PieChart',
    color: '#E6A23C',
    tooltip: '特码重复出现的平均间隔期数',
    hover: false,
    unit: '期'
  }
])

const getNumberColor = (number) => {
  const num = parseInt(number)
  if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
  if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
  return '#52c41a'
}

const getWuxingColor = (wuxing) => {
  switch (wuxing) {
    case '金': return '#FFD700'
    case '木': return '#90EE90'
    case '水': return '#87CEEB'
    case '火': return '#FF6B6B'
    case '土': return '#DEB887'
    default: return '#999'
  }
}

// 新增的辅助函数
const getHotIndexLabel = (number) => {
  const hotIndex = getHotIndex(number)
  if (hotIndex >= 80) return '热'
  if (hotIndex >= 60) return '温'
  if (hotIndex >= 40) return '平'
  if (hotIndex >= 20) return '冷'
  return '极冷'
}

const toggleQuickFilter = (value) => {
  filterType.value = filterType.value === value ? '' : value
}

const handleSearchInput = () => {
  // 可以添加防抖逻辑
  // 这里暂时直接触发过滤
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
}

const handleRowClick = (row) => {
  // 点击行时显示详情
  showNumberDetail(row.number)
}

const getEmptyText = () => {
  if (searchQuery.value) {
    return `没有找到包含"${searchQuery.value}"的数据`
  }
  if (filterType.value) {
    const filterLabel = quickFilterTags.value.find(tag => tag.value === filterType.value)?.label || filterType.value
    return `没有符合"${filterLabel}"条件的数据`
  }
  return '暂无数据'
}

const isNumberWatched = (number) => {
  return watchedNumbers.value.has(number)
}

const toggleWatchNumber = (number) => {
  if (watchedNumbers.value.has(number)) {
    watchedNumbers.value.delete(number)
    ElMessage.success(`已取消关注号码 ${number}`)
  } else {
    watchedNumbers.value.add(number)
    ElMessage.success(`已关注号码 ${number}`)
  }
}

const resetFilters = () => {
  searchQuery.value = ''
  filterType.value = ''
  sortField.value = 'number'
  sortOrder.value = 'asc'
  columnFilter.value = 'all'
  ElMessage.success('筛选条件已重置')
}

const exportData = () => {
  try {
    const data = filteredAndSortedData.value
    const csvContent = generateCSV(data)
    downloadCSV(csvContent, `特码综合分析_${new Date().toISOString().split('T')[0]}.csv`)
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}

const generateCSV = (data) => {
  const headers = ['号码', '出现次数', '当前遗漏', '最大遗漏', '生肖', '波色', '五行', '单双', '大小', '尾数', '合数', '热度指数']
  const rows = data.map(item => [
    item.number,
    item.count,
    item.missingCount,
    item.maxMissing,
    item.zodiac,
    item.color,
    item.element,
    item.oddEven,
    item.bigSmall,
    item.tailNumber,
    item.sumDigits,
    getHotIndex(item.number)
  ])

  return [headers, ...rows].map(row => row.join(',')).join('\n')
}

const downloadCSV = (content, filename) => {
  const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 这里已经有一个updateMissingAnalysisChart函数的声明，所以我们删除这个重复声明

const processStatistics = (data) => {
  console.log('Raw statistics data:', data)

  // 初始化波色频率数据
  const colorFrequency = {
    '红波': 0,
    '蓝波': 0,
    '绿波': 0
  }

  // 处理每个开奖号码
  data.forEach(draw => {
    if (draw.open_code) {
      const numbers = draw.open_code.split(',').map(n => parseInt(n.trim()))
      numbers.forEach(number => {
        const color = GameRules2025.getColor(number)
        if (color) {
          colorFrequency[color]++
        }
      })
    }
  })

  console.log('Color frequency data:', colorFrequency)

  // 更新图表数据
  const chartData = {
    title: {
      text: '波色统计',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [{
      name: '波色统计',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: '{b}: {c}\n{d}%'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      data: [
        { value: colorFrequency['红波'], name: '红波', itemStyle: { color: '#ff4d4f' } },
        { value: colorFrequency['蓝波'], name: '蓝波', itemStyle: { color: '#1890ff' } },
        { value: colorFrequency['绿波'], name: '绿波', itemStyle: { color: '#52c41a' } }
      ]
    }]
  }

  return chartData
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/statistics.css';
.statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  max-width: 1600px;
  margin: 0 auto;

  .filter-card {
    margin-bottom: 20px;

    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      align-items: flex-start;
    }
  }

  .mt-4 {
    margin-top: 16px;
  }

  .chart-card {
    transition: all 0.3s ease;
    margin: 0 -20px;
    padding: 0 20px;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .chart-controls {
        display: flex;
        gap: 10px;
        align-items: center;
      }
    }

    .chart-container {
      height: 500px;
      width: 100%;
      min-width: 800px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
        pointer-events: none;
      }
    }
  }

  .stat-card {
    height: 160px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 20px;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          font-size: 30px;
          color: #fff;
        }
      }

      .stat-details {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-desc {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .period-inputs {
    display: flex;
    align-items: center;
    gap: 10px;

    .separator {
      color: #909399;
    }
  }

  .latest-draw-info {
    margin-left: auto;
    padding: 0 20px;

    .el-tag {
      font-size: 14px;
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #f0f9eb;
      border-color: #e1f3d8;
      color: #67c23a;
    }
  }

  .filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
  }

  .advanced-analysis {
    // 分析控制区样式
    .analysis-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 12px;
      margin-bottom: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .control-section {
        display: flex;
        align-items: center;
        gap: 10px;

        .control-label {
          font-weight: 600;
          color: #606266;
          white-space: nowrap;
        }

        .quick-filter-tag {
          cursor: pointer;
          transition: all 0.3s ease;
          margin-right: 8px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
        }

        .data-summary {
          :deep(.el-statistic__content) {
            color: #409eff;
            font-weight: bold;
          }
        }
      }
    }

    // 表格控制区样式
    .table-controls {
      margin-bottom: 20px;

      .controls-row {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
        padding: 15px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .control-item {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .el-table {
      margin: 20px 0;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      // 设置表格最大高度，确保可以显示所有数据
      :deep(.el-table__body-wrapper) {
        max-height: calc(100vh - 400px) !important;
        overflow-y: auto;
      }

      // 添加表格hover效果
      :deep(.el-table__row) {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f7fa !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      // 优化表头样式
      :deep(.el-table__header-wrapper) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: bold;
          position: sticky;
          top: 0;
          z-index: 2;

          .cell {
            padding: 12px 0;
          }
        }
      }

      // 优化单元格样式
      :deep(.el-table__cell) {
        padding: 8px 0;

        .cell {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      // 优化标签样式
      :deep(.el-tag) {
        width: 100%;
        text-align: center;
        border-radius: 4px;

        &.el-tag--danger {
          color: #fff;
          background-color: #ff4d4f;
          border-color: #ff4d4f;
        }

        &.el-tag--primary {
          color: #fff;
          background-color: #1890ff;
          border-color: #1890ff;
        }

        &.el-tag--success {
          color: #fff;
          background-color: #52c41a;
          border-color: #52c41a;
        }
      }

      // 添加斑马纹效果
      :deep(.el-table__row--striped) {
        background-color: #fafafa;
      }

      // 优化滚动条样式
      :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
      }

      :deep(.el-scrollbar__bar.is-vertical) {
        width: 6px;
      }

      :deep(.el-scrollbar__thumb) {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 3px;
      }

      // 热度指数样式
      .hot-index-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .hot-index-progress {
          width: 80px;
        }

        .hot-index-info {
          display: flex;
          align-items: center;
          gap: 4px;

          .hot-index-value {
            font-weight: bold;
            font-size: 12px;
          }

          .hot-index-label {
            font-size: 10px;
            color: #909399;
            background: #f5f7fa;
            padding: 1px 4px;
            border-radius: 2px;
          }
        }
      }

      // 号码单元格样式
      .number-cell {
        font-weight: bold;
        font-size: 16px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
      }

      // 生肖单元格样式
      .zodiac-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        .zodiac-icon {
          font-size: 16px;
        }

        .zodiac-text {
          font-size: 12px;
          font-weight: 500;
        }
      }

      // 走势点样式
      .trend-dots {
        display: flex;
        gap: 2px;
        justify-content: center;

        .trend-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 1px solid #ddd;
          transition: all 0.3s ease;

          &.hit {
            &.red { background-color: #ff4d4f; border-color: #ff4d4f; }
            &.blue { background-color: #1890ff; border-color: #1890ff; }
            &.green { background-color: #52c41a; border-color: #52c41a; }
          }

          &.miss {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
          }

          &:hover {
            transform: scale(1.2);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }

  .combination-charts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    padding: 24px;
    width: 100%;
    max-width: 1800px;
    margin: 0 auto;

    .chart-container {
      height: 360px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
        pointer-events: none;
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 8px;
  overflow: hidden;

  .el-card__header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
  }
}

:deep(.el-radio-button__inner) {
  padding: 8px 15px;
}

:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-select-dropdown__item) {
  padding: 0 15px;
}

/* 图表使用说明样式 */
.chart-guide-banner {
  margin-bottom: 20px;
}

.chart-instructions h4 {
  color: #409eff;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.chart-instructions p {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.6;
}

.chart-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.chart-instructions li {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 指南对话框样式 */
.chart-guide-content {
  max-height: 70vh;
  overflow-y: auto;
}

.guide-section {
  padding: 20px;
}

.guide-section h3 {
  color: #409eff;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.guide-section p {
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.8;
  font-size: 14px;
}

.guide-section ul {
  margin: 0 0 20px 0;
  padding-left: 0;
  list-style: none;
}

.guide-section li {
  color: #606266;
  margin-bottom: 12px;
  line-height: 1.6;
  padding: 10px 15px;
  background: #f8f9fa;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  position: relative;
}

.guide-section li strong {
  color: #409eff;
  font-weight: 600;
}

.guide-footer {
  margin-top: 20px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

/* 图表控制按钮样式 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-guide-content {
    max-height: 60vh;
  }

  .guide-section {
    padding: 15px;
  }

  .guide-section h3 {
    font-size: 16px;
  }

  .chart-controls {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }
}

/* 新增统计指标样式 */
.probability-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.probability-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.probability-bar {
  width: 50px;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.probability-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.rebound-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.rebound-desc {
  font-size: 10px;
  color: #666;
}

.stability-container {
  display: flex;
  justify-content: center;
}

.stability-container .el-rate {
  --el-rate-icon-size: 14px;
}

/* 热度指数样式优化 */
.hot-index-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.hot-index-progress {
  width: 60px;
}

.hot-index-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.hot-index-value {
  font-size: 12px;
  font-weight: bold;
}

.hot-index-label {
  font-size: 10px;
  color: #666;
}

/* 新增高级分析样式 */

/* 位置分析样式 */
.position-analysis {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.position-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.position-label {
  font-size: 10px;
  color: #666;
}

.position-value {
  font-size: 11px;
  font-weight: bold;
  color: #333;
}

.position-value.special {
  color: #ff4d4f;
}

/* 连号关联样式 */
.adjacent-analysis {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.adjacent-desc {
  font-size: 10px;
  color: #666;
}

/* 数学特征样式 */
.math-features {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.feature-row {
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
}

.digital-root {
  font-size: 10px;
  color: #666;
  background: #f0f0f0;
  padding: 1px 4px;
  border-radius: 2px;
}

/* 波动趋势样式 */
.trend-analysis {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.trend-label {
  font-size: 10px;
  color: #666;
  min-width: 40px;
}

.trend-value {
  font-size: 10px;
  color: #333;
}

/* 关联分析样式 */
.relation-analysis {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.relation-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.relation-label {
  font-size: 10px;
  color: #666;
  min-width: 50px;
}

/* 预测指标样式 */
.prediction-analysis {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.prediction-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.prediction-label {
  font-size: 10px;
  color: #666;
  min-width: 50px;
}

.prediction-value {
  font-size: 10px;
  color: #333;
  font-weight: bold;
}

/* 特殊模式样式 */
.pattern-analysis {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pattern-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pattern-label {
  font-size: 10px;
  color: #666;
  min-width: 30px;
}

.pattern-value {
  font-size: 10px;
  color: #333;
  font-weight: bold;
}

/* 综合评分样式 */
.comprehensive-score {
  display: flex;
  align-items: center;
  gap: 10px;
}

.score-main {
  flex-shrink: 0;
}

.score-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 3px;
}

.score-label {
  font-size: 10px;
  color: #666;
  min-width: 30px;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .position-analysis,
  .trend-analysis,
  .relation-analysis,
  .prediction-analysis,
  .pattern-analysis {
    font-size: 9px;
  }

  .comprehensive-score {
    flex-direction: column;
    gap: 5px;
  }

  .score-main .el-progress {
    width: 40px !important;
  }
}

@media (max-width: 768px) {
  .math-features .feature-row {
    flex-direction: column;
    gap: 2px;
  }

  .comprehensive-score {
    align-items: center;
  }

  .score-details {
    align-items: center;
  }
}
</style>