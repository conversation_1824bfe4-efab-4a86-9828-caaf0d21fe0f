<template>
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <img src="../assets/logo.png" alt="Logo" class="nav-logo">
        <span class="brand-name">Mark Six Prediction</span>
      </div>
      <div class="nav-links">
        <router-link to="/" class="nav-link" active-class="active">首页</router-link>
        <router-link to="/history" class="nav-link" active-class="active">历史记录</router-link>
        <router-link to="/statistics" class="nav-link" active-class="active">统计分析</router-link>
      </div>
      <div class="nav-right">
        <span class="admin-link">Admin</span>
      </div>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'NavBar'
}
</script>

<style scoped>
.navbar {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-logo {
  height: 32px;
  width: auto;
}

.brand-name {
  font-size: 1.2em;
  font-weight: bold;
  color: #2c3e50;
}

.nav-links {
  display: flex;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: #666;
  font-size: 1.1em;
  padding: 5px 0;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #1890ff;
}

.nav-link.active {
  color: #1890ff;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #1890ff;
}

.nav-right {
  color: #666;
  cursor: pointer;
}

.admin-link:hover {
  color: #1890ff;
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 10px;
  }

  .brand-name {
    display: none;
  }

  .nav-links {
    gap: 15px;
  }

  .nav-link {
    font-size: 1em;
  }
}
</style> 