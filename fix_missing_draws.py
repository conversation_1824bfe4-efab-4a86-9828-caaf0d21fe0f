#!/usr/bin/env python3
"""
修复缺失的开奖记录数据
"""
from app.database import SessionLocal
from app.utils.data_manager import get_data_manager
from app.models import Draw
import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_database_status():
    """检查数据库状态"""
    logger.info("检查数据库状态...")

    db = SessionLocal()
    try:
        # 检查总记录数
        total_count = db.query(Draw).count()
        logger.info(f"数据库中总共有 {total_count} 条开奖记录")

        if total_count == 0:
            logger.warning("数据库为空，需要初始化数据")
            return False

        # 按年份统计
        from sqlalchemy import text
        years = db.execute(text("""
            SELECT substr(expect, 1, 4) as year, COUNT(*) as count
            FROM draws
            GROUP BY substr(expect, 1, 4)
            ORDER BY year DESC
        """)).fetchall()

        logger.info("按年份统计:")
        for year, count in years:
            logger.info(f"  {year}年: {count} 条记录")

        # 检查是否缺少近期数据
        current_year = datetime.now().year
        current_year_count = db.query(Draw).filter(
            Draw.expect.like(f"{current_year}%")
        ).count()

        if current_year_count < 10:  # 如果当前年份记录少于10条，认为数据不完整
            logger.warning(
                f"{current_year}年只有 {current_year_count} 条记录，数据可能不完整")
            return False

        return True

    except Exception as e:
        logger.error(f"检查数据库状态失败: {e}")
        return False
    finally:
        db.close()


def sync_historical_data():
    """同步历史数据"""
    logger.info("开始同步历史数据...")

    db = SessionLocal()
    try:
        data_manager = get_data_manager(db)

        # 同步近几年的数据
        years_to_sync = [2023, 2024, 2025]

        for year in years_to_sync:
            logger.info(f"同步 {year} 年的数据...")

            # 检查该年份是否已有数据
            existing_count = db.query(Draw).filter(
                Draw.expect.like(f"{year}%")
            ).count()

            if existing_count > 0:
                logger.info(f"{year} 年已有 {existing_count} 条记录，跳过")
                continue

            # 同步该年份的数据
            success_count, fail_count = data_manager.sync_historical_data(year)
            logger.info(
                f"{year} 年同步完成: 成功 {success_count} 条，失败 {fail_count} 条")

            if success_count == 0:
                logger.warning(f"{year} 年同步失败，可能是API问题")

        # 同步最新数据
        logger.info("同步最新开奖数据...")
        latest_draw = data_manager.sync_latest_draw(force_update=True)
        if latest_draw:
            logger.info(f"成功同步最新开奖数据: 期号 {latest_draw.expect}")
        else:
            logger.warning("同步最新开奖数据失败")

    except Exception as e:
        logger.error(f"同步历史数据失败: {e}")
    finally:
        db.close()


def main():
    """主函数"""
    logger.info("开始修复缺失的开奖记录数据...")

    # 1. 检查数据库状态
    if check_database_status():
        logger.info("数据库状态正常，无需修复")
        return

    # 2. 同步历史数据
    sync_historical_data()

    # 3. 再次检查数据库状态
    logger.info("修复完成，再次检查数据库状态...")
    check_database_status()

    logger.info("修复过程完成")


if __name__ == "__main__":
    main()
