import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 基础配置
BASE_CONFIG = {
    'host': os.getenv('FLASK_HOST', 'localhost'),
    'port': int(os.getenv('FLASK_PORT', 5000)),
    'debug': os.getenv('FLASK_DEBUG', 'True').lower() == 'true',
    'SQLALCHEMY_DATABASE_URI': os.getenv('DATABASE_URL', 'sqlite:///data/lottery.db'),
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SECRET_KEY': os.getenv('SECRET_KEY', 'your-secret-key-here'),
}

# API相关配置
API_CONFIG = {
    'version': 'v1',
    'prefix': '/api',
    'rate_limit': {
        'default': '100/hour',
        'auth': '1000/hour'
    },
    'cors_origins': os.getenv('CORS_ORIGINS', '*').split(','),
    'api_key_header': 'X-API-Key',
}
