import pytest
import time
from datetime import datetime
from app.models.draw import Draw
from app.services.statistics_service import (
    get_number_frequency,
    get_zodiac_statistics,
    get_color_statistics,
    get_hot_numbers,
    get_cold_numbers,
    get_recent_trends
)
from app.services.prediction_service import (
    prepare_training_data,
    train_model,
    get_next_prediction
)


def create_test_data(db_session, count):
    """创建测试数据"""
    for i in range(count):
        draw = Draw(
            expect=f"2025{i+1:03d}",
            numbers=[1, 2, 3, 4, 5, 6],
            draw_time=datetime.now(),
            special_number=7,
            zodiac="鼠",
            color="红",
            odd_even="单",
            big_small="大",
            tail_big_small="尾大",
            sum_odd_even="和单",
            wuxing="金"
        )
        db_session.add(draw)
    db_session.commit()


def test_statistics_performance(db_session):
    """测试统计服务性能"""
    # 创建1000条测试数据
    create_test_data(db_session, 1000)

    # 测试号码频率统计性能
    start_time = time.time()
    frequency = get_number_frequency(db_session)
    frequency_time = time.time() - start_time
    assert frequency_time < 1.0  # 应该在1秒内完成

    # 测试生肖统计性能
    start_time = time.time()
    zodiac_stats = get_zodiac_statistics(db_session)
    zodiac_time = time.time() - start_time
    assert zodiac_time < 1.0

    # 测试波色统计性能
    start_time = time.time()
    color_stats = get_color_statistics(db_session)
    color_time = time.time() - start_time
    assert color_time < 1.0

    # 测试热门号码统计性能
    start_time = time.time()
    hot_numbers = get_hot_numbers(db_session)
    hot_time = time.time() - start_time
    assert hot_time < 1.0

    # 测试冷门号码统计性能
    start_time = time.time()
    cold_numbers = get_cold_numbers(db_session)
    cold_time = time.time() - start_time
    assert cold_time < 1.0

    # 测试近期走势统计性能
    start_time = time.time()
    trends = get_recent_trends(db_session)
    trends_time = time.time() - start_time
    assert trends_time < 2.0  # 近期走势统计可能较慢，允许2秒


def test_prediction_performance(db_session):
    """测试预测服务性能"""
    # 创建1000条测试数据
    create_test_data(db_session, 1000)

    # 测试训练数据准备性能
    start_time = time.time()
    X, y = prepare_training_data(db_session)
    prepare_time = time.time() - start_time
    assert prepare_time < 2.0  # 数据准备应该在2秒内完成

    # 测试模型训练性能
    start_time = time.time()
    success = train_model(db_session)
    train_time = time.time() - start_time
    assert success is True
    assert train_time < 10.0  # 模型训练应该在10秒内完成

    # 测试预测性能
    start_time = time.time()
    prediction = get_next_prediction(db_session)
    predict_time = time.time() - start_time
    assert prediction is not None
    assert predict_time < 1.0  # 预测应该在1秒内完成


def test_concurrent_requests(client, db_session):
    """测试并发请求性能"""
    # 创建1000条测试数据
    create_test_data(db_session, 1000)

    # 测试并发获取开奖记录
    start_time = time.time()
    responses = []
    for _ in range(10):  # 模拟10个并发请求
        response = client.get("/api/v1/draws/")
        responses.append(response)
    concurrent_time = time.time() - start_time

    # 验证响应
    for response in responses:
        assert response.status_code == 200
    assert concurrent_time < 5.0  # 10个并发请求应该在5秒内完成
