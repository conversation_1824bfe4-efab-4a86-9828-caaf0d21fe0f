import axios from 'axios'
import { ElMessage } from 'element-plus'
import ErrorHandler from './error-handler'

// 获取环境变量
const apiUrl = 'http://localhost:8000';  // 修改为8002端口
const apiTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');
const apiRetryCount = parseInt(import.meta.env.VITE_API_RETRY_COUNT || '3');

// 创建 axios 实例
const service = axios.create({
  baseURL: apiUrl, // 使用环境变量中的后端服务器URL
  timeout: apiTimeout, // 使用环境变量中的超时时间
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': '*'
  },
  withCredentials: false, // 禁用跨域请求携带凭证，与CORS代理配置保持一致
  // 添加重试配置
  retry: apiRetryCount, // 使用环境变量中的重试次数
  retryDelay: 2000, // 重试延迟时间
  // 添加默认的错误处理
  validateStatus: function (status) {
    // 允许所有状态码通过，由拦截器处理错误
    return true;
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 调试信息
    console.log('API Request:', config.method.toUpperCase(), config.url, config.params || config.data);
    return config
  },
  error => {
    ErrorHandler.handleApiError(error, '请求配置')
    return Promise.reject(error)
  }
)

// 添加重试机制
axios.interceptors.response.use(undefined, async (err) => {
  const { config } = err;
  if (!config || !config.retry) return Promise.reject(err);

  // 设置变量跟踪重试次数
  config.__retryCount = config.__retryCount || 0;

  // 检查是否已经达到最大重试次数
  if (config.__retryCount >= config.retry) {
    return Promise.reject(err);
  }

  // 增加重试计数
  config.__retryCount += 1;
  console.log(`重试请求 (${config.__retryCount}/${config.retry}): ${config.url}`);

  // 创建新的Promise来处理延迟
  const delayRetry = new Promise((resolve) => {
    setTimeout(() => {
      console.log(`延迟${config.retryDelay}ms后重试...`);
      resolve();
    }, config.retryDelay || 1000);
  });

  // 在延迟后重试
  await delayRetry;
  return axios(config);
});

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 调试信息
    console.log('API Response:', response.config.url, res);

    // 如果响应是文件流，直接返回
    if (response.config.responseType === 'blob') {
      return response
    }

    // 这里可以根据后端的约定判断响应是否成功
    if (res.code && res.code !== 200) {
      ElMessage.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }

    // 如果响应是数组形式，构造一个符合前端期望的格式
    if (Array.isArray(res)) {
      return {
        code: 200,
        data: res,
        total: res.length,
        message: 'success'
      }
    }

    // 处理带有 total 和 items 的分页响应
    if (res.items && res.total !== undefined) {
      return {
        code: 200,
        data: res.items,
        total: res.total,
        message: 'success'
      }
    }

    // 处理已经符合标准格式的响应
    if (res.code === 200 && res.data !== undefined) {
      console.log('标准格式响应:', res);
      return res;
    }

    // 处理其他格式的响应
    console.log('非标准格式响应，尝试适配:', res);

    // 如果响应中包含data字段，但没有code字段
    if (res.data !== undefined && res.code === undefined) {
      return {
        code: 200,
        data: res.data,
        total: res.total || (Array.isArray(res.data) ? res.data.length : 0),
        message: res.message || 'success'
      }
    }

    return res
  },
  error => {
    // 检查是否是取消请求的错误
    if (axios.isCancel(error)) {
      console.log('请求已取消:', error.message);
      return Promise.reject(error);
    }

    // 检查是否是网络错误
    if (error.message && error.message.includes('Network Error')) {
      ElMessage.error('网络错误，请检查您的网络连接');
      console.error('网络错误:', error);
      return Promise.reject(error);
    }

    // 检查是否是超时错误
    if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
      ElMessage.error('请求超时，请稍后再试');
      console.error('请求超时:', error);
      return Promise.reject(error);
    }

    // 处理HTTP状态码错误
    if (error.response) {
      const status = error.response.status;
      let message = '';

      switch (status) {
        case 400:
          message = '请求错误';
          break;
        case 401:
          message = '未授权，请重新登录';
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器错误';
          break;
        default:
          message = `未知错误(${status})`;
      }

      ElMessage.error(message);
      console.error(`HTTP错误 ${status}:`, error.response.data || message);
    }

    ErrorHandler.handleApiError(error, '响应错误');
    return Promise.reject(error);
  }
)

export default service
