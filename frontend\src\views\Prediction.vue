<template>
  <div class="prediction-page">
    <div class="page-header">
      <h1>智能预测系统</h1>
      <div class="system-status">
        <el-tag :type="modelStatus === 'ready' ? 'success' : 'warning'" effect="dark" size="large">
          <el-icon><CircleCheck v-if="modelStatus === 'ready'" /><Warning v-else /></el-icon>
          {{ modelStatus === 'ready' ? '模型已就绪' : '模型需要训练' }}
        </el-tag>
        <el-button @click="fetchModelStatus" :loading="loading" circle>
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 模型训练卡片 -->
    <el-card class="mb-4 model-card">
      <template #header>
        <div class="card-header">
          <h2>模型训练</h2>
          <div class="header-actions">
            <el-button type="primary" @click="showTrainingDialog" :loading="trainingLoading">
              <el-icon><Operation /></el-icon> 训练模型
            </el-button>
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="stat-panel">
            <div class="stat-title">模型状态</div>
            <div class="stat-content">
              <el-progress
                type="dashboard"
                :percentage="modelStatus === 'ready' ? 100 : 30"
                :color="modelStatus === 'ready' ? '#67C23A' : '#E6A23C'"
                :status="modelStatus === 'ready' ? 'success' : 'warning'"
              >
                <template #default>
                  <div class="progress-content">
                    <span class="progress-value">{{ modelStatus === 'ready' ? '已就绪' : '需要训练' }}</span>
                    <span class="progress-label">状态</span>
                  </div>
                </template>
              </el-progress>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="stat-panel">
            <div class="stat-title">模型准确率</div>
            <div class="stat-content">
              <el-progress
                type="dashboard"
                :percentage="modelAccuracy ? Math.round(modelAccuracy * 100) : 0"
                :color="getAccuracyColor(modelAccuracy || 0)"
              >
                <template #default>
                  <div class="progress-content">
                    <span class="progress-value" v-if="modelAccuracy !== null && modelAccuracy !== undefined">
                      {{ (modelAccuracy * 100).toFixed(1) + '%' }}
                    </span>
                    <span class="progress-value" v-else>未知</span>
                    <span class="progress-label">准确率</span>
                  </div>
                </template>
              </el-progress>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-divider content-position="center">训练信息</el-divider>

      <el-descriptions :column="2" border size="large">
        <el-descriptions-item label="最近训练时间">
          <el-tag size="large" effect="plain" type="info">
            <el-icon><Timer /></el-icon>
            {{ lastTrainingTime || '暂无训练记录' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="训练数据量">
          <el-tag size="large" effect="plain" type="info">
            <el-icon><DataLine /></el-icon>
            {{ trainingDataCount > 0 ? trainingDataCount + ' 条' : '暂无数据' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 智能预测卡片 -->
    <el-card class="prediction-card">
      <template #header>
        <div class="card-header">
          <h2>智能预测</h2>
          <div class="header-actions">
            <el-input
              v-model="customExpect"
              placeholder="输入期号（留空使用下一期）"
              class="expect-input"
              clearable
              :disabled="predicting"
            >
              <template #prepend>
                <el-icon><Calendar /></el-icon> 期号
              </template>
            </el-input>
            <el-button type="success" @click="getPredictionResult" :loading="predicting" :disabled="modelStatus !== 'ready'">
              <el-icon><Search /></el-icon> 获取预测
            </el-button>
            <el-button type="primary" @click="evaluateLastPrediction" :disabled="!currentPrediction">
              <el-icon><TrendCharts /></el-icon> 评估结果
            </el-button>
          </div>
        </div>
      </template>

      <template v-if="currentPrediction">
        <el-row :gutter="20" class="mb-4">
          <el-col :span="8">
            <el-card shadow="hover" class="info-card">
              <template #header>
                <div class="prediction-header">
                  <span>期号</span>
                  <el-tag size="large" effect="dark" type="info">{{ currentPrediction.expect }}</el-tag>
                </div>
              </template>
              <div class="prediction-numbers">
                <div
                  v-for="(number, index) in currentPrediction.numbers"
                  :key="index"
                  class="number-circle"
                  :class="[getBallColor(number), { 'selected': selectedNumbers.includes(number) }]"
                  @click="toggleNumberSelection(number)"
                  :title="getNumberColor(number)"
                >
                  {{ number }}
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="info-card">
              <template #header>
                <div class="prediction-header">
                  <span>预测可信度</span>
                </div>
              </template>
              <div class="confidence-container">
                <div class="confidence-chart">
                  <el-progress
                    type="dashboard"
                    :percentage="Math.round((currentPrediction.confidence_scores?.combined || 0) * 100)"
                    :color="getAccuracyColor(currentPrediction.confidence_scores?.combined || 0)"
                  >
                    <template #default="{ percentage }">
                      <div class="dashboard-content">
                        <span class="percentage">{{ percentage }}%</span>
                        <span class="label">综合置信度</span>
                      </div>
                    </template>
                  </el-progress>
                </div>

                <div class="confidence-details">
                  <div class="confidence-item">
                    <span class="confidence-label">RF模型:</span>
                    <el-progress
                      :percentage="(currentPrediction.confidence_scores?.rf || 0) * 100"
                      :color="getAccuracyColor(currentPrediction.confidence_scores?.rf || 0)"
                      :format="format => `${format}%`"
                    />
                  </div>
                  <div class="confidence-item">
                    <span class="confidence-label">XGBoost模型:</span>
                    <el-progress
                      :percentage="(currentPrediction.confidence_scores?.xgboost || 0) * 100"
                      :color="getAccuracyColor(currentPrediction.confidence_scores?.xgboost || 0)"
                      :format="format => `${format}%`"
                    />
                  </div>
                  <div class="confidence-item" v-if="currentPrediction.confidence_scores?.lstm">
                    <span class="confidence-label">LSTM模型:</span>
                    <el-progress
                      :percentage="(currentPrediction.confidence_scores?.lstm || 0) * 100"
                      :color="getAccuracyColor(currentPrediction.confidence_scores?.lstm || 0)"
                      :format="format => `${format}%`"
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="info-card">
              <template #header>
                <div class="prediction-header">
                  <span>预测分析</span>
                </div>
              </template>
              <div class="prediction-analysis">
                <div class="analysis-item">
                  <el-icon><Star /></el-icon>
                  <span>预测时间: {{ formatDateTime(currentPrediction.prediction_time) }}</span>
                </div>
                <div class="analysis-item">
                  <el-icon><Pointer /></el-icon>
                  <span>准确率: {{ (currentPrediction.accuracy * 100).toFixed(1) }}%</span>
                </div>
                <div class="analysis-item">
                  <el-icon><Histogram /></el-icon>
                  <span>实际结果: {{ currentPrediction.actual_result && Array.isArray(currentPrediction.actual_result) ? currentPrediction.actual_result.join(', ') : '未知' }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-card shadow="hover" class="mb-4">
          <template #header>
            <div class="prediction-header">
              <span>预测建议</span>
            </div>
          </template>
          <div class="suggestion-content">
            <el-icon class="suggestion-icon"><SuccessFilled /></el-icon>
            <div class="suggestion-text">
              <pre>{{ currentPrediction.suggestion }}</pre>
            </div>
          </div>
        </el-card>

        <!-- 预测详情卡片 -->
        <el-card shadow="hover" class="mb-4 prediction-detail-card">
          <template #header>
            <div class="prediction-header">
              <span>预测号码详情</span>
              <el-button type="text" @click="showDetailExpanded = !showDetailExpanded">
                {{ showDetailExpanded ? '收起' : '展开' }}
              </el-button>
            </div>
          </template>
          <div v-if="showDetailExpanded" class="prediction-details">
            <!-- 特码预测 -->
            <div class="detail-section">
              <div class="prediction-code-header">
                <div class="code-title">1、特码预测</div>
                <div class="code-subtitle">号码球颜色对应波色</div>
              </div>

              <div class="prediction-numbers-container">
                <div v-if="predictionData && predictionData.special_numbers_5 && predictionData.special_numbers_5.length > 0" class="number-section">
                  <div class="number-section-title">5码:</div>
                  <div class="number-balls-container">
                    <div
                      v-for="(num, index) in predictionData.special_numbers_5"
                      :key="'5-' + num"
                      class="number-ball"
                      :class="getBallColor(num)"
                    >
                      {{ String(num).padStart(2, '0') }}
                    </div>
                  </div>
                </div>

                <div v-if="predictionData && predictionData.special_numbers_10 && predictionData.special_numbers_10.length > 0" class="number-section">
                  <div class="number-section-title">10码:</div>
                  <div class="number-balls-container">
                    <div
                      v-for="(num, index) in predictionData.special_numbers_10"
                      :key="'10-' + num"
                      class="number-ball"
                      :class="getBallColor(num)"
                    >
                      {{ String(num).padStart(2, '0') }}
                    </div>
                  </div>
                </div>

                <div class="number-section">
                  <div class="number-section-title">15码:</div>
                  <div class="number-balls-container">
                    <div
                      v-for="(num, index) in (predictionData && predictionData.special_numbers_15 && predictionData.special_numbers_15.length > 0 ? predictionData.special_numbers_15 : [])"
                      :key="'15-' + num"
                      class="number-ball"
                      :class="getBallColor(num)"
                    >
                      {{ String(num).padStart(2, '0') }}
                    </div>
                    <div v-if="!predictionData || !predictionData.special_numbers_15 || predictionData.special_numbers_15.length === 0" class="no-data-message">
                      暂无数据
                    </div>
                  </div>
                </div>

                <div class="number-section">
                  <div class="number-section-title">20码:</div>
                  <div class="number-balls-container">
                    <div
                      v-for="(num, index) in (predictionData && predictionData.special_numbers_20 && predictionData.special_numbers_20.length > 0 ? predictionData.special_numbers_20 : [])"
                      :key="'20-' + num"
                      class="number-ball"
                      :class="getBallColor(num)"
                    >
                      {{ String(num).padStart(2, '0') }}
                    </div>
                    <div v-if="!predictionData || !predictionData.special_numbers_20 || predictionData.special_numbers_20.length === 0" class="no-data-message">
                      暂无数据
                    </div>
                  </div>
                </div>

                <div class="number-section">
                  <div class="number-section-title">30码:</div>
                  <div class="number-balls-container">
                    <div
                      v-for="(num, index) in (predictionData && predictionData.special_numbers_30 && predictionData.special_numbers_30.length > 0 ? predictionData.special_numbers_30 : [])"
                      :key="'30-' + num"
                      class="number-ball"
                      :class="getBallColor(num)"
                    >
                      {{ String(num).padStart(2, '0') }}
                    </div>
                    <div v-if="!predictionData || !predictionData.special_numbers_30 || predictionData.special_numbers_30.length === 0" class="no-data-message">
                      暂无数据
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 属性预测 -->
            <div class="detail-section">
              <div class="prediction-code-header">
                <div class="code-title">2、属性预测</div>
              </div>
              <div class="attribute-predictions" v-if="predictionData && predictionData.attribute_predictions">
                <el-table :data="attributeTableData" border stripe class="attribute-table">
                  <el-table-column prop="label" label="属性" width="180" />
                  <el-table-column prop="value" label="预测结果" />
                </el-table>
              </div>

            </div>

            <!-- 生肖预测详情 -->
            <div class="detail-section">
              <div class="prediction-code-header">
                <div class="code-title">3、生肖预测</div>
              </div>
              <div class="zodiac-predictions">
                <div class="zodiac-section" v-if="predictionData && predictionData.zodiac_3">
                  <div class="zodiac-title">3肖:</div>
                  <div class="zodiac-content">
                    <el-tag
                      v-for="(zodiac, index) in predictionData.zodiac_3"
                      :key="'z3-' + index"
                      class="zodiac-tag"
                      type="warning"
                      effect="light"
                    >
                      {{ zodiac }}
                    </el-tag>
                  </div>
                </div>

                <div class="zodiac-section" v-if="predictionData && predictionData.zodiac_5">
                  <div class="zodiac-title">5肖:</div>
                  <div class="zodiac-content">
                    <el-tag
                      v-for="(zodiac, index) in predictionData.zodiac_5"
                      :key="'z5-' + index"
                      class="zodiac-tag"
                      type="warning"
                      effect="light"
                    >
                      {{ zodiac }}
                    </el-tag>
                  </div>
                </div>

                <div class="zodiac-section" v-if="predictionData && predictionData.zodiac_7">
                  <div class="zodiac-title">7肖:</div>
                  <div class="zodiac-content">
                    <el-tag
                      v-for="(zodiac, index) in predictionData.zodiac_7"
                      :key="'z7-' + index"
                      class="zodiac-tag"
                      type="warning"
                      effect="light"
                    >
                      {{ zodiac }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 竞猜策略 -->
            <div class="detail-section">
              <div class="prediction-code-header">
                <div class="code-title">4、本期竞猜策略</div>
              </div>
              <div class="strategy-content">
                <p v-if="predictionData && predictionData.strategy">{{ predictionData.strategy }}</p>
                <p v-else class="no-data-message">暂无策略建议</p>
              </div>
            </div>

            <!-- 预测置信度详情 -->
            <div class="detail-section">
              <div class="prediction-code-header">
                <div class="code-title">5、预测置信度</div>
              </div>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-card shadow="hover" class="confidence-card">
                    <template #header>
                      <div class="confidence-header">LSTM模型</div>
                    </template>
                    <div class="confidence-value">
                      <el-progress
                        type="dashboard"
                        :percentage="predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.lstm === 'number' ? Math.round(predictionData.confidence_scores.lstm * 100) : 0"
                        :color="getAccuracyColor(predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.lstm === 'number' ? predictionData.confidence_scores.lstm : 0)"
                      >
                        <template #default="{ percentage }">
                          <span class="progress-value">{{ percentage }}%</span>
                        </template>
                      </el-progress>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card shadow="hover" class="confidence-card">
                    <template #header>
                      <div class="confidence-header">随机森林模型</div>
                    </template>
                    <div class="confidence-value">
                      <el-progress
                        type="dashboard"
                        :percentage="predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.rf === 'number' ? Math.round(predictionData.confidence_scores.rf * 100) : 0"
                        :color="getAccuracyColor(predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.rf === 'number' ? predictionData.confidence_scores.rf : 0)"
                      >
                        <template #default="{ percentage }">
                          <span class="progress-value">{{ percentage }}%</span>
                        </template>
                      </el-progress>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card shadow="hover" class="confidence-card">
                    <template #header>
                      <div class="confidence-header">XGBoost模型</div>
                    </template>
                    <div class="confidence-value">
                      <el-progress
                        type="dashboard"
                        :percentage="predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.xgboost === 'number' ? Math.round(predictionData.confidence_scores.xgboost * 100) : 0"
                        :color="getAccuracyColor(predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.xgboost === 'number' ? predictionData.confidence_scores.xgboost : 0)"
                      >
                        <template #default="{ percentage }">
                          <span class="progress-value">{{ percentage }}%</span>
                        </template>
                      </el-progress>
                    </div>
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card shadow="hover" class="confidence-card">
                    <template #header>
                      <div class="confidence-header">综合置信度</div>
                    </template>
                    <div class="confidence-value">
                      <el-progress
                        type="dashboard"
                        :percentage="predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.combined === 'number' ? Math.round(predictionData.confidence_scores.combined * 100) : 0"
                        :color="getAccuracyColor(predictionData && predictionData.confidence_scores && typeof predictionData.confidence_scores.combined === 'number' ? predictionData.confidence_scores.combined : 0)"
                      >
                        <template #default="{ percentage }">
                          <span class="progress-value">{{ percentage }}%</span>
                        </template>
                      </el-progress>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </div>
          <div v-else class="prediction-details-collapsed">
            <el-button type="primary" link @click="showDetailExpanded = true">点击查看详细预测信息</el-button>
          </div>
        </el-card>
      </template>

      <el-empty v-else description="暂无预测结果，请点击'获取预测'按钮" />
    </el-card>

    <!-- 训练对话框 -->
    <el-dialog v-model="trainingDialogVisible" title="模型训练" width="50%" destroy-on-close>
      <div class="dialog-header-actions" style="position: absolute; right: 50px; top: 20px;">
        <el-button v-if="lastTrainingInfo" type="info" size="small" @click="showTrainingInfoDialog">
          <el-icon><InfoFilled /></el-icon> 查看训练详情
        </el-button>
      </div>
      <el-form :model="trainingParams" label-width="120px">
        <el-form-item label="训练数据范围">
          <div class="data-range-selector">
            <!-- 主要选择项 -->
            <el-radio-group v-model="trainingParams.data_range" class="data-range-radio-group">
              <el-radio :value="'all'">
                <div class="radio-label-content">
                  <span>全部数据</span>
                  <el-tag v-if="trainingDataCount > 0" size="small" type="info" class="ml-1">
                    {{ trainingDataCount }} 条
                  </el-tag>
                </div>
              </el-radio>
              <el-radio :value="'recent'">
                <div class="radio-label-content">
                  <span>最近数据</span>
                  <el-tag v-if="trainingParams.recent_count > 0" size="small" type="info" class="ml-1">
                    {{ trainingParams.recent_count }} 条
                  </el-tag>
                </div>
              </el-radio>
              <el-radio :value="'custom'">
                <div class="radio-label-content">
                  <span>自定义范围</span>
                  <el-tag v-if="trainingParams.data_range === 'custom' && trainingParams.start_expect && trainingParams.end_expect" size="small" type="info" class="ml-1">
                    {{ trainingParams.start_expect }} 至 {{ trainingParams.end_expect }}
                  </el-tag>
                </div>
              </el-radio>
            </el-radio-group>

            <!-- 快捷选择按钮 -->
            <div class="quick-data-range-buttons" v-if="trainingParams.data_range === 'recent'">
              <el-button-group size="small">
                <el-button type="primary" plain :class="{ 'is-active': trainingParams.recent_count === 46 }" @click="trainingParams.recent_count = 46">46条</el-button>
                <el-button type="primary" plain :class="{ 'is-active': trainingParams.recent_count === 100 }" @click="trainingParams.recent_count = 100">100条</el-button>
                <el-button type="primary" plain :class="{ 'is-active': trainingParams.recent_count === 150 }" @click="trainingParams.recent_count = 150">150条</el-button>
              </el-button-group>
            </div>
          </div>
        </el-form-item>

        <el-form-item v-if="trainingParams.data_range === 'recent'" label="最近期数">
          <div class="training-data-range">
            <!-- 快捷选择按钮组 -->
            <div class="quick-select-buttons">
              <el-radio-group v-model="trainingParams.recent_count" size="small">
                <el-radio-button :value="30">30条</el-radio-button>
                <el-radio-button :value="50">50条</el-radio-button>
                <el-radio-button :value="100">100条</el-radio-button>
                <el-radio-button :value="200">200条</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 自定义输入框 -->
            <div class="input-number-with-controls">
              <el-button @click="decreaseRecentCount" :disabled="trainingParams.recent_count <= 10">
                <el-icon><Minus /></el-icon>
              </el-button>
              <el-input-number v-model="trainingParams.recent_count" :min="10" :max="1000" controls-position="right" :step="10" />
              <el-button @click="increaseRecentCount" :disabled="trainingParams.recent_count >= 1000">
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item v-if="trainingParams.data_range === 'custom'" label="开始期号">
          <el-input v-model="trainingParams.start_expect" placeholder="例如: 2023001" />
        </el-form-item>

        <el-form-item v-if="trainingParams.data_range === 'custom'" label="结束期号">
          <el-input v-model="trainingParams.end_expect" placeholder="例如: 2023050" />
        </el-form-item>

        <el-divider content-position="center">高级筛选选项</el-divider>

        <el-form-item label="数据筛选">
          <el-switch
            v-model="trainingParams.use_advanced_filters"
            active-text="启用高级筛选"
            inactive-text="不使用筛选"
          />
        </el-form-item>

        <template v-if="trainingParams.use_advanced_filters">
          <el-form-item label="特码范围">
            <el-row :gutter="10">
              <el-col :span="11">
                <el-input-number v-model="trainingParams.filters.special_number_min" :min="1" :max="49" placeholder="最小值" />
              </el-col>
              <el-col :span="2" class="text-center">
                <span>至</span>
              </el-col>
              <el-col :span="11">
                <el-input-number v-model="trainingParams.filters.special_number_max" :min="1" :max="49" placeholder="最大值" />
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item label="生肖筛选">
            <el-select
              v-model="trainingParams.filters.zodiac"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="选择生肖"
              style="width: 100%"
            >
              <el-option
                v-for="item in zodiacOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="波色筛选">
            <el-select
              v-model="trainingParams.filters.color"
              multiple
              collapse-tags
              collapse-tags-tooltip
              placeholder="选择波色"
              style="width: 100%"
            >
              <el-option
                v-for="item in colorOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="单双筛选">
            <el-radio-group v-model="trainingParams.filters.odd_even">
              <el-radio :value="null">不筛选</el-radio>
              <el-radio value="单">单数</el-radio>
              <el-radio value="双">双数</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="大小筛选">
            <el-radio-group v-model="trainingParams.filters.big_small">
              <el-radio :value="null">不筛选</el-radio>
              <el-radio value="大">大数</el-radio>
              <el-radio value="小">小数</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <el-form-item label="训练模型">
          <el-checkbox-group v-model="trainingParams.models">
            <el-checkbox :value="'rf'">随机森林</el-checkbox>
            <el-checkbox :value="'xgboost'">XGBoost</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="特征工程">
          <el-switch
            v-model="trainingParams.use_feature_engineering"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="交叉验证">
          <el-switch
            v-model="trainingParams.use_cross_validation"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item v-if="trainingParams.use_cross_validation" label="验证折数">
          <el-input-number v-model="trainingParams.cv_folds" :min="2" :max="10" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="trainingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startTraining" :loading="trainingLoading">
            开始训练
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 评估对话框 -->
    <el-dialog v-model="evaluateDialogVisible" title="评估预测结果" width="50%" destroy-on-close>
      <el-form label-width="120px">
        <el-form-item label="期号">
          <el-input v-model="evaluateExpect" placeholder="输入实际开奖期号" />
        </el-form-item>

        <el-form-item label="实际结果">
          <el-input v-model="evaluateResult" placeholder="输入实际开奖号码，用逗号分隔" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="evaluateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEvaluation" :loading="evaluating">
            提交评估
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 训练详情对话框 -->
    <el-dialog v-model="trainingInfoDialogVisible" title="训练数据详情" width="60%">
      <div v-if="lastTrainingInfo" class="training-info-container">
        <el-descriptions title="训练数据信息" :column="1" border>
          <el-descriptions-item label="数据范围类型">
            <el-tag type="primary">
              {{ lastTrainingInfo.dataRange === 'all' ? '全部数据' :
                 lastTrainingInfo.dataRange === 'recent' ? '最近数据' : '自定义范围' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item v-if="lastTrainingInfo.dataRange === 'recent'" label="最近数据条数">
            {{ lastTrainingInfo.recentCount }} 条
          </el-descriptions-item>

          <el-descriptions-item v-if="lastTrainingInfo.dataRange === 'custom'" label="设置的期号范围">
            {{ lastTrainingInfo.customRange.start }} 至 {{ lastTrainingInfo.customRange.end }}
          </el-descriptions-item>

          <el-descriptions-item label="实际期号范围">
            {{ lastTrainingInfo.periodRange.start }} 至 {{ lastTrainingInfo.periodRange.end }}
          </el-descriptions-item>

          <el-descriptions-item label="实际使用数据条数">
            <el-tag type="success">{{ lastTrainingInfo.dataCount }} 条</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="period-list-container" v-if="lastTrainingInfo.periodRange.periods && lastTrainingInfo.periodRange.periods.length > 0">
          <h3>包含的期号列表</h3>
          <el-scrollbar height="200px">
            <div class="period-list">
              <el-tag
                v-for="period in lastTrainingInfo.periodRange.periods"
                :key="period"
                class="period-tag"
                effect="plain"
              >
                {{ period }}
              </el-tag>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <div v-else class="no-training-info">
        <el-empty description="暂无训练数据信息" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  trainModel,
  trainModels,
  predictNext,
  getNextExpect,
  evaluatePrediction,
  getModelStatus
} from '@/api/prediction'
import { formatDateTime } from '@/utils/format'
import { Refresh, DataLine, Timer, Operation, Search, TrendCharts, Histogram, Pointer, Star, Warning, SuccessFilled, CircleCheck, Calendar, Minus, Plus } from '@element-plus/icons-vue'
import { DEFAULT_PARAMS } from '@/types/model-params'
import ErrorHandler from '@/utils/error-handler'
import GameRules2025 from '@/utils/GameRules2025'

// 辅助函数
function getAccuracyColor(accuracy) {
  if (accuracy >= 0.8) return '#67C23A' // 绿色 - 优秀
  if (accuracy >= 0.6) return '#409EFF' // 蓝色 - 良好
  if (accuracy >= 0.4) return '#E6A23C' // 黄色 - 一般
  return '#F56C6C' // 红色 - 较差
}

// 获取号码球颜色
function getBallColor(number) {
  const num = parseInt(number)
  if (GameRules2025.RED_NUMBERS.includes(num)) return 'red-ball'
  if (GameRules2025.BLUE_NUMBERS.includes(num)) return 'blue-ball'
  if (GameRules2025.GREEN_NUMBERS.includes(num)) return 'green-ball'
  return ''
}

// 获取号码的波色
function getNumberColor(number) {
  return GameRules2025.getColor(number)
}

// 状态变量
const trainingLoading = ref(false)
const loading = ref(false)
const predicting = ref(false)
const evaluating = ref(false)
const trainingDialogVisible = ref(false)
const trainingInfoDialogVisible = ref(false) // 训练详情对话框状态
const customExpect = ref('') // 自定义期号输入
const evaluateDialogVisible = ref(false)
const modelStatus = ref('not_ready')
const lastTrainingTime = ref(null)
const trainingDataCount = ref(0)
const modelAccuracy = ref(null)
const currentPrediction = ref(null)
const evaluateExpect = ref('')
const evaluateResult = ref('')
const selectedNumbers = ref([])
const lastTrainingInfo = ref(null) // 最近一次训练的详细信息

// 预测详情相关变量
const showDetailExpanded = ref(false) // 是否展开预测详情
const predictionData = ref(null) // 完整的预测数据

// 属性预测表格数据
const attributeTableData = computed(() => {
  if (!predictionData.value || !predictionData.value.attribute_predictions) return [];

  const ap = predictionData.value.attribute_predictions;
  return [
    { label: '特码的单双', value: ap.special_odd_even || '-' },
    { label: '特码的大小', value: ap.special_big_small || '-' },
    { label: '特码的波色', value: ap.special_color || '-' },
    { label: '特码是家禽还是野兽', value: ap.special_animal_type || '-' },
    { label: '特码所属的五行', value: ap.special_element || '-' },
    { label: '特码合数单双', value: ap.special_sum_odd_even || '-' },
    { label: '特码尾数大小', value: ap.special_tail_big_small || '-' }
  ];
})

// 生肖和波色选项
const zodiacOptions = [
  '鼠', '牛', '虎', '兔', '龙', '蛇',
  '马', '羊', '猴', '鸡', '狗', '猪'
]

const colorOptions = ['红波', '蓝波', '绿波']

// 训练参数
const trainingParams = ref({
  data_range: 'recent',
  recent_count: 100,
  start_expect: '',
  end_expect: '',
  models: ['rf', 'xgboost'],
  use_feature_engineering: true,
  use_cross_validation: true,
  cv_folds: 5,
  use_advanced_filters: false,
  filters: {
    special_number_min: 1,
    special_number_max: 49,
    zodiac: [],
    color: [],
    odd_even: null,
    big_small: null
  },
  ...DEFAULT_PARAMS
})

// 增减最近期数
function increaseRecentCount() {
  if (trainingParams.value.recent_count < 1000) {
    trainingParams.value.recent_count += 10
  }
}

function decreaseRecentCount() {
  if (trainingParams.value.recent_count > 10) {
    trainingParams.value.recent_count -= 10
  }
}

// 生命周期钩子
onMounted(async () => {
  await fetchModelStatus()
})

// 获取模型状态
async function fetchModelStatus() {
  try {
    loading.value = true
    const result = await getModelStatus()
    console.log('Model status:', result)

    if (result) {
      modelStatus.value = result.status || 'not_ready'
      // 修复字段名不匹配问题
      lastTrainingTime.value = result.lastTraining ? formatDateTime(new Date(result.lastTraining)) : null
      trainingDataCount.value = result.dataCount || 0
      modelAccuracy.value = result.accuracy || null

      // 调试日志
      console.log('处理后的模型状态:', {
        status: modelStatus.value,
        lastTrainingTime: lastTrainingTime.value,
        trainingDataCount: trainingDataCount.value,
        modelAccuracy: modelAccuracy.value
      })
    }
  } catch (error) {
    ErrorHandler.handleError(error, '获取模型状态')
  } finally {
    loading.value = false
  }
}

// 显示训练对话框
function showTrainingDialog() {
  trainingDialogVisible.value = true
}

// 显示训练详情对话框
function showTrainingInfoDialog() {
  trainingInfoDialogVisible.value = true
}

// 开始训练
async function startTraining() {
  if (!validateTrainingParams()) {
    return
  }

  try {
    trainingLoading.value = true

    // 准备训练参数
    const params = {
      ...trainingParams.value,
      // 确保数据范围参数正确传递
      data_range: trainingParams.value.data_range,
      recent_count: trainingParams.value.recent_count,
      // 标准化期号格式
      start_expect: trainingParams.value.start_expect ? String(trainingParams.value.start_expect).padStart(7, '0') : '',
      end_expect: trainingParams.value.end_expect ? String(trainingParams.value.end_expect).padStart(7, '0') : '',
      // 高级筛选参数
      use_advanced_filters: trainingParams.value.use_advanced_filters,
      filters: trainingParams.value.filters
    }

    // 打印标准化后的期号
    console.log('标准化后的期号:', {
      start_expect: params.start_expect,
      end_expect: params.end_expect
    })

    console.log('训练参数:', params)

    // 显示数据范围提示
    let dataRangeMsg = ''
    if (params.data_range === 'all') {
      dataRangeMsg = `使用全部数据 (${trainingDataCount.value} 条)`
    } else if (params.data_range === 'recent') {
      dataRangeMsg = `使用最近 ${params.recent_count} 条数据`
    } else {
      dataRangeMsg = `使用自定义范围数据: ${params.start_expect} 至 ${params.end_expect}`
    }

    ElMessage.info(`开始训练模型，${dataRangeMsg}`)

    let result
    if (params.models.length > 1) {
      // 训练多个模型
      result = await trainModels(params)
    } else {
      // 训练单个模型
      const model = params.models[0]
      result = await trainModel(model, params)
    }

    console.log('训练结果:', result)

    // 显示训练数据详细信息
    let trainingDataMsg = ''
    if (result.dataCount) {
      // 更新训练数据量
      trainingDataCount.value = result.dataCount

      // 构建详细的训练信息消息
      if (result.trainingInfo) {
        const info = result.trainingInfo

        // 根据数据范围类型构建消息
        let rangeMsg = ''
        if (info.dataRange === 'all') {
          rangeMsg = '全部数据'
        } else if (info.dataRange === 'recent') {
          rangeMsg = `最近 ${info.recentCount} 条数据`
        } else if (info.dataRange === 'custom') {
          rangeMsg = `自定义范围: ${info.customRange.start} 至 ${info.customRange.end}`
        }

        // 构建实际使用的期号范围信息
        let actualRangeMsg = ''
        if (info.periodRange && info.periodRange.start && info.periodRange.end) {
          actualRangeMsg = `\n实际期号范围: ${info.periodRange.start} 至 ${info.periodRange.end}`
        }

        trainingDataMsg = `\n训练数据范围: ${rangeMsg}\n实际使用了 ${result.dataCount} 条训练数据${actualRangeMsg}`
      } else {
        trainingDataMsg = `\n实际使用了 ${result.dataCount} 条训练数据`
      }
    }

    // 使用弹窗显示训练结果
    ElMessage({
      message: `模型训练成功${trainingDataMsg}`,
      type: 'success',
      duration: 8000,  // 显示8秒，因为信息量增加
      showClose: true
    })

    // 如果有详细的训练信息，显示在控制台并保存信息
    if (result.trainingInfo) {
      console.log('训练数据详细信息:', result.trainingInfo)

      // 保存训练信息以便后续显示
      lastTrainingInfo.value = result.trainingInfo

      // 显示详细信息按钮
      ElMessage({
        message: '点击“查看训练详情”按钮可以查看详细的训练数据信息',
        type: 'info',
        duration: 5000,
        showClose: true
      })
    }
    trainingDialogVisible.value = false

    // 刷新模型状态
    await fetchModelStatus()
  } catch (error) {
    ErrorHandler.handleTrainingError(error)
  } finally {
    trainingLoading.value = false
  }
}

// 验证训练参数
function validateTrainingParams() {
  if (trainingParams.value.models.length === 0) {
    ElMessage.warning('请至少选择一个模型进行训练')
    return false
  }

  if (trainingParams.value.data_range === 'custom') {
    if (!trainingParams.value.start_expect || !trainingParams.value.end_expect) {
      ElMessage.warning('请输入开始和结束期号')
      return false
    }
  }

  if (trainingParams.value.data_range === 'recent' && (!trainingParams.value.recent_count || trainingParams.value.recent_count < 10)) {
    ElMessage.warning('最近期数不能小于10')
    return false

  }
  return true
}

// 获取预测
async function getPredictionResult() {
  try {
    predicting.value = true

    // 判断是否使用自定义期号
    let expectToUse = '';

    if (customExpect.value && customExpect.value.trim() !== '') {
      // 使用用户输入的期号
      expectToUse = customExpect.value.trim();
      console.log('Using custom expect:', expectToUse); // 调试信息
    } else {
      // 获取下一期期号
      console.log('Getting next expect...'); // 调试信息
      const nextExpectResponse = await getNextExpect();
      console.log('Next Expect Response:', nextExpectResponse); // 调试信息

      // 确保我们获取到正确的期号字符串
      expectToUse = nextExpectResponse.expect || "2025001";
    }

    // 修复期号格式，确保是正确的格式：年份(4位)+期号(3位)
    if (expectToUse.length > 7) {
      // 如果期号格式错误（如20250101001），则修正为正确格式
      expectToUse = expectToUse.substring(0, 4) + expectToUse.substring(expectToUse.length - 3);
      console.log('Fixed expect format:', expectToUse);
    } else if (expectToUse.length < 7) {
      // 如果期号太短，尝试添加年份前缀
      const currentYear = new Date().getFullYear();
      if (expectToUse.length <= 3) {
        // 假设只输入了期号部分，添加年份
        expectToUse = `${currentYear}${expectToUse.padStart(3, '0')}`;
      } else {
        // 其他情况使用默认期号
        expectToUse = "2025001";
      }
      console.log('Formatted expect:', expectToUse);
    }

    console.log('Using expect for prediction:', expectToUse); // 调试信息

    // 使用predictNext而不是getPrediction，因为后端没有实现/api/prediction/{id}路由
    const result = await predictNext(expectToUse)
    console.log('Prediction Result:', result) // 调试信息

    // 确保结果数据格式正确
    console.log('Raw prediction result:', result) // 调试信息

    // 检查结果格式
    if (result) {
      // 处理新的 PredictionResponse 格式
      // 检查是否是新的 PredictionResponse 格式
      if (result.status && result.prediction) {
        // 新的 PredictionResponse 格式
        console.log('检测到新的 PredictionResponse 格式')
        const predictionDataObj = result.prediction

        // 保存完整的预测数据供详情展示使用
        predictionData.value = predictionDataObj
        console.log('保存完整预测数据:', predictionData.value)

        // 提取预测号码
        const numbers = predictionDataObj.special_numbers_5 || []
        console.log('Extracted numbers:', numbers) // 调试信息

        // 提取置信度分数
        const confidenceScores = predictionDataObj.confidence_scores || {
          lstm: 0.7,
          rf: 0.8,
          xgboost: 0.75,
          combined: 0.75
        }
        console.log('Extracted confidence scores:', confidenceScores) // 调试信息

        // 提取策略
        const strategy = predictionDataObj.strategy || "暂无建议"
        console.log('Extracted strategy:', strategy) // 调试信息
      } else if (result.data || result.special_numbers_5) {
        // 旧的格式
        console.log('检测到旧的预测结果格式')
        const predictionDataObj = result.data || result

        // 保存完整的预测数据供详情展示使用
        predictionData.value = predictionDataObj
        console.log('保存完整预测数据:', predictionData.value)

        // 提取预测号码
        const numbers = predictionDataObj.special_numbers_5 || []
        console.log('Extracted numbers:', numbers) // 调试信息

        // 提取置信度分数
        const confidenceScores = predictionDataObj.confidence_scores || {
          lstm: 0.7,
          rf: 0.8,
          xgboost: 0.75,
          combined: 0.75
        }
        console.log('Extracted confidence scores:', confidenceScores) // 调试信息

        // 提取策略
        const strategy = predictionDataObj.strategy || "暂无建议"
        console.log('Extracted strategy:', strategy) // 调试信息
      } else {
        // 未知格式，使用默认值
        console.warn('未知的预测结果格式:', result)

        // 创建一个模拟结果，避免UI崩溃
        const mockData = {
          expect: expectToUse,  // 使用实际用于预测的期号
          prediction_time: new Date(),
          special_numbers_5: [1, 2, 3, 4, 5],
          special_numbers_10: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
          special_numbers_15: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          special_numbers_20: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
          special_numbers_30: Array.from({length: 30}, (_, i) => i + 1),
          attribute_predictions: {
            special_odd_even: "单",
            special_big_small: "小",
            special_color: "红波",
            special_animal_type: "家禽",
            special_element: "金",
            special_tail_big_small: "尾小",
            special_sum_odd_even: "合单"
          },
          zodiac_3: ["鼠", "牛", "虎"],
          zodiac_5: ["鼠", "牛", "虎", "兔", "龙"],
          zodiac_7: ["鼠", "牛", "虎", "兔", "龙", "蛇", "马"],
          strategy: "示例策略：\n1. 重点关注号码：1, 2, 3, 4, 5\n2. 单数、小数走势强劲\n3. 红波、金属性值得关注",
          confidence_scores: {
            lstm: 0.7,
            rf: 0.8,
            xgboost: 0.75,
            combined: 0.75
          }
        };

        // 保存完整的预测数据供详情展示使用
        predictionData.value = mockData;

        // 提取预测号码
        const numbers = mockData.special_numbers_5;
        const confidenceScores = mockData.confidence_scores;
        const strategy = mockData.strategy;
        console.log('使用默认值');

        // 显示警告消息
        ElMessage.warning('无法解析预测结果，已加载测试数据');
      }

      // 更新预测结果
      if (result.status && result.prediction) {
        // 新的 PredictionResponse 格式
        const predictionDataObj = result.prediction;
        currentPrediction.value = {
          expect: expectToUse,  // 使用实际用于预测的期号
          prediction_time: new Date(),
          numbers: predictionDataObj.special_numbers_5 || [],
          confidence_scores: predictionDataObj.confidence_scores || {
            lstm: 0.7,
            rf: 0.8,
            xgboost: 0.75,
            combined: 0.75
          },
          accuracy: (predictionDataObj.confidence_scores && predictionDataObj.confidence_scores.combined) || 0.75,
          suggestion: predictionDataObj.strategy || "暂无建议",
          actual_result: null
        };
      } else if (result.data || result.special_numbers_5) {
        // 旧的格式
        const predictionDataObj = result.data || result;
        currentPrediction.value = {
          expect: expectToUse,  // 使用实际用于预测的期号
          prediction_time: new Date(),
          numbers: predictionDataObj.special_numbers_5 || [],
          confidence_scores: predictionDataObj.confidence_scores || {
            lstm: 0.7,
            rf: 0.8,
            xgboost: 0.75,
            combined: 0.75
          },
          accuracy: (predictionDataObj.confidence_scores && predictionDataObj.confidence_scores.combined) || 0.75,
          suggestion: predictionDataObj.strategy || "暂无建议",
          actual_result: null
        };
      } else {
        // 未知格式，使用默认值
        currentPrediction.value = {
          expect: expectToUse,  // 使用实际用于预测的期号
          prediction_time: new Date(),
          numbers: predictionData.value.special_numbers_5 || [1, 2, 3, 4, 5],
          confidence_scores: predictionData.value.confidence_scores || {
            lstm: 0.7,
            rf: 0.8,
            xgboost: 0.75,
            combined: 0.75
          },
          accuracy: (predictionData.value.confidence_scores && predictionData.value.confidence_scores.combined) || 0.75,
          suggestion: predictionData.value.strategy || "暂无建议",
          actual_result: null
        };
      }

      // 默认展开详情面板
      showDetailExpanded.value = true

      // 清空自定义期号输入，为下次预测做准备
      customExpect.value = '';

      console.log('Final prediction object:', currentPrediction.value) // 调试信息
      ElMessage.success('预测完成')
    }
  } catch (error) {
    ErrorHandler.handlePredictionError(error)
  } finally {
    predicting.value = false
  }
}

// 评估上次预测
function evaluateLastPrediction() {
  if (!currentPrediction.value) {
    ElMessage.warning('没有可评估的预测结果')
    return
  }

  evaluateExpect.value = currentPrediction.value.expect
  evaluateResult.value = ''
  evaluateDialogVisible.value = true
}

// 提交评估
async function submitEvaluation() {
  if (!evaluateExpect.value || !evaluateResult.value) {
    ElMessage.warning('请输入期号和实际结果')
    return
  }

  try {
    evaluating.value = true

    // 解析实际结果
    const actualNumbers = evaluateResult.value.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n))

    if (actualNumbers.length === 0) {
      ElMessage.warning('请输入有效的实际结果')
      return
    }

    // 更新当前预测的实际结果
    if (currentPrediction.value && currentPrediction.value.expect === evaluateExpect.value) {
      currentPrediction.value.actual_result = actualNumbers
    }

    // 调用评估API
    const result = await evaluatePrediction({
      expect: evaluateExpect.value,
      predicted_numbers: currentPrediction.value.numbers,
      actual_numbers: actualNumbers
    })

    console.log('Evaluation result:', result)

    if (result && result.accuracy) {
      ElMessage.success(`评估完成，准确率: ${(result.accuracy * 100).toFixed(2)}%`)
      evaluateDialogVisible.value = false
    } else {
      ElMessage.warning('评估结果无效')
    }
  } catch (error) {
    ErrorHandler.handleError(error, '评估预测')
  } finally {
    evaluating.value = false
  }
}

// 切换号码选择
function toggleNumberSelection(number) {
  const index = selectedNumbers.value.indexOf(number)
  if (index === -1) {
    selectedNumbers.value.push(number)
  } else {
    selectedNumbers.value.splice(index, 1)
  }
}
</script>

<style scoped>
.prediction-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.model-card, .prediction-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.expect-input {
  width: 220px;
  margin-right: 10px;
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-panel {
  padding: 10px;
  text-align: center;
}

.stat-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #606266;
}

.stat-content {
  display: flex;
  justify-content: center;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.progress-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.prediction-numbers {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.number-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
}

.number-circle:hover {
  background-color: #e6f7ff;
  transform: scale(1.1);
}

.number-circle.selected {
  background-color: #409EFF;
  color: white;
}

.confidence-bars {
  padding: 10px;
}

.confidence-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.confidence-label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
  font-weight: bold;
}

.confidence-value {
  width: 60px;
  text-align: left;
  padding-left: 10px;
}

.prediction-analysis {
  padding: 10px;
}

.analysis-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
}

.analysis-item .el-icon {
  margin-right: 10px;
  color: #409EFF;
}

.suggestion-content {
  display: flex;
  padding: 10px;
}

.suggestion-icon {
  font-size: 24px;
  color: #67C23A;
  margin-right: 15px;
  margin-top: 5px;
}

.suggestion-text {
  flex: 1;
}

.suggestion-text pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
  line-height: 1.6;
}

.info-card {
  height: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

/* 预测详情相关样式 */
.prediction-details {
  padding: 15px;
}

.prediction-details-collapsed {
  padding: 15px;
  text-align: center;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
  border-left: 3px solid #409EFF;
  padding-left: 10px;
}

/* 预测号码详情样式 */
.prediction-code-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.code-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.code-subtitle {
  font-size: 12px;
  color: #909399;
}

.number-section {
  margin-bottom: 20px;
}

.number-section-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 10px;
}

.number-balls-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.number-ball {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 属性预测样式 */
.attribute-predictions {
  margin-bottom: 20px;
}

.attribute-item {
  display: flex;
  margin-bottom: 10px;
  line-height: 24px;
}

.attribute-label {
  width: 180px;
  font-weight: bold;
  color: #606266;
}

.attribute-value {
  color: #303133;
}

/* 策略内容样式 */
.strategy-content {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  line-height: 1.6;
}

.strategy-content p {
  margin: 0;
  white-space: pre-line;
}

/* 波色相关样式 */
.red-ball {
  background-color: #ff4d4f !important;
  color: white !important;
}

.blue-ball {
  background-color: #1890ff !important;
  color: white !important;
}

.green-ball {
  background-color: #52c41a !important;
  color: white !important;
}

.number-circle.selected {
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.number-circle.small {
  width: 30px;
  height: 30px;
  font-size: 12px;
  margin: 3px;
}

.number-groups {
  margin-bottom: 15px;
}

.number-group {
  margin-bottom: 10px;
}

.group-label {
  display: inline-block;
  width: 60px;
  font-weight: bold;
  color: #606266;
}

/* 波色图例样式 */
.color-legend {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 15px;
}

.legend-ball {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-text {
  font-size: 14px;
  color: #606266;
}

/* 生肖预测相关样式 */
.zodiac-card {
  height: 100%;
  margin-bottom: 15px;
}

.zodiac-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zodiac-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 5px 0;
}

.zodiac-tag {
  margin-right: 5px;
  margin-bottom: 5px;
  font-size: 14px;
}

/* 置信度相关样式 */
.confidence-card {
  height: 100%;
  margin-bottom: 15px;
  text-align: center;
}

.confidence-header {
  text-align: center;
  font-weight: bold;
  color: #606266;
}

.confidence-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.confidence-chart {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dashboard-content .percentage {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.dashboard-content .label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.confidence-details {
  width: 100%;
}

.confidence-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.confidence-label {
  width: 120px;
  text-align: right;
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

.confidence-value {
  margin-left: 10px;
  font-weight: bold;
  color: #303133;
}

.confidence-value {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.progress-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.no-data-message {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  width: 100%;
}

.input-number-with-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-number-with-controls .el-input-number {
  width: 120px;
}

.expect-input {
  width: 220px; /* 增加期号输入框宽度 */
}

.ml-1 {
  margin-left: 4px;
}

/* 训练对话框样式 */
.el-dialog__body {
  padding-top: 10px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-radio {
  margin-right: 16px;
  line-height: 32px;
}

.el-tag {
  margin-left: 4px;
}

.training-info-container {
  padding: 10px;
}

.period-list-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

/* 数据范围选择器样式 */
.data-range-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-range-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.radio-label-content {
  display: flex;
  align-items: center;
  gap: 5px;
}

.quick-data-range-buttons {
  margin-top: 5px;
  display: flex;
  justify-content: flex-start;
}

.quick-data-range-buttons .el-button.is-active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

/* 训练数据范围样式 */
.training-data-range {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.quick-select-buttons {
  margin-bottom: 10px;
}

.quick-select-buttons .el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.period-list-container h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #606266;
}

.period-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.period-tag {
  margin: 4px;
}

.no-training-info {
  padding: 20px;
}

.text-center {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
