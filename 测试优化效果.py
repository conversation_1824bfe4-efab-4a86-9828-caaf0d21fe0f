#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特码综合分析功能优化效果测试脚本
"""

import requests
import json
import time
from datetime import datetime

def test_api_performance():
    """测试API性能"""
    print("🚀 测试API性能...")
    
    start_time = time.time()
    response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
    end_time = time.time()
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ API响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"📊 数据大小: {len(response.content)} bytes")
        return data
    else:
        print(f"❌ API调用失败: {response.status_code}")
        return None

def test_data_completeness(data):
    """测试数据完整性"""
    print("\n📋 测试数据完整性...")
    
    if not data:
        print("❌ 无数据")
        return False
    
    basic_stats = data['data']['basicStats']
    number_freq = data['data']['numberFrequency']
    
    # 检查基础统计
    required_fields = ['totalCount', 'hotNumbers', 'coldNumbers', 'averageInterval']
    missing_fields = [field for field in required_fields if field not in basic_stats]
    
    if missing_fields:
        print(f"❌ 缺少字段: {missing_fields}")
        return False
    
    # 检查号码频率完整性
    missing_numbers = []
    for i in range(1, 50):
        if str(i) not in number_freq:
            missing_numbers.append(i)
    
    if missing_numbers:
        print(f"❌ 缺少号码频率数据: {missing_numbers}")
        return False
    
    print("✅ 数据完整性检查通过")
    return True

def test_hot_cold_numbers(data):
    """测试热号冷号分析"""
    print("\n🔥 测试热号冷号分析...")
    
    basic_stats = data['data']['basicStats']
    hot_numbers = basic_stats['hotNumbers']
    cold_numbers = basic_stats['coldNumbers']
    
    print(f"📈 热门号码 (前5名):")
    for i, num_data in enumerate(hot_numbers[:5]):
        print(f"  {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次 ({num_data['percentage']:.2f}%)")
    
    print(f"\n📉 冷门号码 (前5名):")
    for i, num_data in enumerate(cold_numbers[:5]):
        print(f"  {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次 ({num_data['percentage']:.2f}%)")
    
    # 验证排序正确性
    hot_counts = [num['count'] for num in hot_numbers]
    cold_counts = [num['count'] for num in cold_numbers]
    
    if hot_counts != sorted(hot_counts, reverse=True):
        print("❌ 热号排序错误")
        return False
    
    if cold_counts != sorted(cold_counts):
        print("❌ 冷号排序错误")
        return False
    
    print("✅ 热号冷号分析正确")
    return True

def test_missing_analysis(data):
    """测试遗漏分析"""
    print("\n⏰ 测试遗漏分析...")
    
    missing_data = data['data']['missing']
    current_missing = missing_data['current']
    max_missing = missing_data['max']
    
    # 检查遗漏数据
    zero_missing = []
    high_missing = []
    
    for num_str, missing_count in current_missing.items():
        if missing_count == 0:
            zero_missing.append(num_str)
        elif missing_count > 100:
            high_missing.append((num_str, missing_count))
    
    print(f"🎯 当前遗漏为0的号码: {zero_missing}")
    print(f"⚠️ 高遗漏号码 (>100期): {high_missing}")
    
    # 检查数据一致性
    for num_str in current_missing:
        if num_str not in max_missing:
            print(f"❌ 号码{num_str}缺少最大遗漏数据")
            return False
    
    print("✅ 遗漏分析数据正确")
    return True

def test_frequency_distribution(data):
    """测试频率分布"""
    print("\n📊 测试频率分布...")
    
    number_freq = data['data']['numberFrequency']
    color_freq = data['data']['colorFrequency']
    zodiac_freq = data['data']['zodiacFrequency']
    
    # 验证号码频率总和
    total_freq = sum(int(count) for count in number_freq.values())
    expected_total = data['data']['basicStats']['totalCount']
    
    if total_freq != expected_total:
        print(f"❌ 号码频率总和不匹配: {total_freq} != {expected_total}")
        return False
    
    # 验证波色分布
    color_total = sum(color_freq.values())
    if color_total != expected_total:
        print(f"❌ 波色频率总和不匹配: {color_total} != {expected_total}")
        return False
    
    print(f"✅ 频率分布验证通过 (总期数: {expected_total})")
    
    # 显示分布情况
    print(f"🌈 波色分布: 红波{color_freq['红波']}期, 蓝波{color_freq['蓝波']}期, 绿波{color_freq['绿波']}期")
    
    return True

def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n🖥️ 测试前端兼容性...")
    
    try:
        # 测试前端是否可访问
        response = requests.get("http://localhost:5181/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*50)
    print("📋 特码综合分析功能优化测试报告")
    print("="*50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试项目: {len(results)}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"通过率: {success_rate:.1f}% ({passed}/{total})")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if success_rate >= 80:
        print(f"\n🎉 优化效果良好！通过率达到 {success_rate:.1f}%")
    elif success_rate >= 60:
        print(f"\n⚠️ 优化效果一般，通过率 {success_rate:.1f}%，需要进一步改进")
    else:
        print(f"\n❌ 优化效果不佳，通过率仅 {success_rate:.1f}%，需要重新优化")

def main():
    """主测试函数"""
    print("🔍 开始测试特码综合分析功能优化效果...")
    print("="*50)
    
    results = {}
    
    # 1. 测试API性能
    data = test_api_performance()
    results["API性能测试"] = data is not None
    
    if data:
        # 2. 测试数据完整性
        results["数据完整性测试"] = test_data_completeness(data)
        
        # 3. 测试热号冷号分析
        results["热号冷号分析测试"] = test_hot_cold_numbers(data)
        
        # 4. 测试遗漏分析
        results["遗漏分析测试"] = test_missing_analysis(data)
        
        # 5. 测试频率分布
        results["频率分布测试"] = test_frequency_distribution(data)
    
    # 6. 测试前端兼容性
    results["前端兼容性测试"] = test_frontend_compatibility()
    
    # 生成测试报告
    generate_test_report(results)

if __name__ == "__main__":
    main()
