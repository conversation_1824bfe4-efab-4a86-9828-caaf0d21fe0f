from fastapi import FastAPI, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uuid

app = FastAPI()

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源（仅用于开发环境）
    allow_credentials=False,  # 修改为False以避免与allow_origins=["*"]冲突
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 存储任务状态
task_status = {}

"""
异步执行模型训练
@param task_id: 任务ID
@param params: 训练参数
"""
def train_model_task(task_id: str, params: dict):
    # 更新任务状态为进行中
    task_status[task_id] = {"status": "processing", "progress": 0}
    
    try:
        # 这里是您的模型训练代码
        # ... 现有代码 ...
        
        # 训练完成后更新状态
        task_status[task_id] = {"status": "completed", "result": "训练结果"}
    except Exception as e:
        # 训练失败时更新状态
        task_status[task_id] = {"status": "failed", "error": str(e)}

@app.post("/api/advanced-prediction/train")
async def train_model(params: dict, background_tasks: BackgroundTasks):
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())
    
    # 将训练任务放入后台执行
    background_tasks.add_task(train_model_task, task_id, params)
    
    # 立即返回任务ID
    return {"task_id": task_id}

@app.get("/api/advanced-prediction/status/{task_id}")
async def get_task_status(task_id: str):
    # 返回任务状态
    if task_id not in task_status:
        return {"status": "not_found"}
    return task_status[task_id]
from fastapi import FastAPI, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
import uuid

app = FastAPI()

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源（仅用于开发环境）
    allow_credentials=False,  # 修改为False以避免与allow_origins=["*"]冲突
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 存储任务状态
task_status = {}

"""
异步执行模型训练
@param task_id: 任务ID
@param params: 训练参数
"""
def train_model_task(task_id: str, params: dict):
    # 更新任务状态为进行中
    task_status[task_id] = {"status": "processing", "progress": 0}
    
    try:
        # 这里是您的模型训练代码
        # ... 现有代码 ...
        
        # 训练完成后更新状态
        task_status[task_id] = {"status": "completed", "result": "训练结果"}
    except Exception as e:
        # 训练失败时更新状态
        task_status[task_id] = {"status": "failed", "error": str(e)}

@app.post("/api/advanced-prediction/train")
async def train_model(params: dict, background_tasks: BackgroundTasks):
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())
    
    # 将训练任务放入后台执行
    background_tasks.add_task(train_model_task, task_id, params)
    
    # 立即返回任务ID
    return {"task_id": task_id}

@app.get("/api/advanced-prediction/status/{task_id}")
async def get_task_status(task_id: str):
    # 返回任务状态
    if task_id not in task_status:
        return {"status": "not_found"}
    return task_status[task_id]