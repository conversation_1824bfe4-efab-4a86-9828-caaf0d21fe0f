from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime
from ..database import get_db
from ..models.draw import Draw  # 假设有 Draw 模型
from ..schemas.draw import DrawOut  # 假设有 DrawOut 输出模式

router = APIRouter(
    prefix="/api/draw",
    tags=["draw"]
)

@router.get("/history", response_model=dict)
def get_draw_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_period: Optional[int] = None,
    end_period: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    number: Optional[int] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取开奖历史，支持分页和多种筛选条件
    """
    try:
        query = db.query(Draw)

        if start_period:
            query = query.filter(Draw.expect >= start_period)
        if end_period:
            query = query.filter(Draw.expect <= end_period)

        # 转换日期字符串为datetime对象
        start_date_dt = None
        end_date_dt = None
        try:
            if start_date:
                start_date_dt = datetime.strptime(start_date, "%Y-%m-%d")
            if end_date:
                end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
        except Exception:
            pass

        if start_date_dt:
            query = query.filter(Draw.open_time >= start_date_dt)
        if end_date_dt:
            query = query.filter(Draw.open_time <= end_date_dt)
        if number:
            # 号码过滤，假设 open_code 是逗号分隔字符串，需模糊匹配
            query = query.filter(Draw.open_code.like(f"%{number}%"))
        if zodiac:
            query = query.filter(Draw.zodiac == zodiac)
        if color:
            query = query.filter(Draw.color == color)
        if odd_even:
            query = query.filter(Draw.odd_even == odd_even)
        if big_small:
            query = query.filter(Draw.big_small == big_small)
        if tail_big_small:
            query = query.filter(Draw.tail_big_small == tail_big_small)
        if sum_odd_even:
            query = query.filter(Draw.sum_odd_even == sum_odd_even)
        if animal_type:
            query = query.filter(Draw.animal_type == animal_type)
        if wuxing:
            query = query.filter(Draw.wuxing == wuxing)

        total = query.count()
        results = query.order_by(Draw.expect.desc()).offset((page - 1) * page_size).limit(page_size).all()

        data = [DrawOut.from_orm(draw).dict() for draw in results]

        return {
            "code": 200,
            "data": data,
            "total": total,
            "message": "success"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
