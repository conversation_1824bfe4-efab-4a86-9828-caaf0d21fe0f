from collections import defaultdict
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from datetime import datetime
import logging
import os
from ..models import Draw
from .game_rules import GameRules2025

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(
            'logs', f'statistics_{datetime.now().strftime("%Y%m%d")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class EnhancedStatistics:
    """增强版统计分析类"""

    def __init__(self):
        self.game_rules = GameRules2025()

    def calculate_statistics(self, draws: List[Draw]) -> Dict[str, Any]:
        """计算统计数据"""
        try:
            if not draws:
                return self._get_empty_statistics()

            # 按期号排序（升序）
            sorted_draws = sorted(draws, key=lambda x: x.expect)
            logger.info(
                f"Processing {len(sorted_draws)} draws for statistics calculation")

            # 初始化统计数据
            number_frequency = {str(i): 0 for i in range(
                1, 50)}   # 号码频率，确保包含未出现过的号码
            color_frequency = defaultdict(int)    # 波色频率
            tail_frequency = defaultdict(int)     # 尾数频率
            head_frequency = defaultdict(int)     # 头数频率
            zodiac_frequency = defaultdict(int)   # 生肖频率
            wuxing_frequency = defaultdict(int)   # 五行频率
            attributes_frequency = {              # 特码属性频率
                "单": 0, "双": 0, "大": 0, "小": 0,
                "家禽": 0, "野兽": 0,
                "尾单": 0, "尾双": 0,
                "尾大": 0, "尾小": 0,
                "合单": 0, "合双": 0,
                "合大": 0, "合小": 0
            }

            # 遗漏值统计 - 初始化
            # 当前遗漏值：初始化为0，表示每个号码当前的遗漏期数
            missing_current = {str(i): 0 for i in range(1, 50)}
            # 最大遗漏值：记录历史上最大的遗漏期数
            missing_max = {str(i): 0 for i in range(1, 50)}
            # 最后出现期数
            last_appearance = {str(i): None for i in range(1, 50)}
            # 临时遗漏值：用于计算过程中
            temp_missing = {str(i): 0 for i in range(1, 50)}

            # 连号统计
            consecutive_count = {str(i): 0 for i in range(1, 50)}
            max_consecutive = {str(i): 0 for i in range(1, 50)}

            # 遍历开奖记录
            for i, draw in enumerate(sorted_draws):
                try:
                    # 解析开奖号码
                    if not draw.open_code:
                        logger.warning(
                            f"Missing open_code for draw {draw.expect}")
                        continue

                    numbers = [int(n.strip())
                               for n in draw.open_code.split(',') if n.strip().isdigit()]
                    if not numbers:
                        logger.warning(
                            f"Invalid open_code format for draw {draw.expect}")
                        continue

                    special_number = numbers[-1]  # 特码
                    if special_number < 1 or special_number > 49:
                        logger.warning(
                            f"Invalid special number {special_number} for draw {draw.expect}")
                        continue

                    # 更新特码频率
                    number_str = str(special_number)
                    number_frequency[number_str] += 1

                    # 获取号码属性
                    attributes = self.game_rules.get_number_attributes(
                        special_number)

                    # 更新波色频率
                    if attributes['color']:
                        color_frequency[attributes['color']] += 1

                    # 更新尾数频率
                    if attributes['tail'] >= 0:
                        tail_frequency[str(attributes['tail'])] += 1

                    # 更新头数频率
                    if attributes['head'] >= 0:
                        head_frequency[str(attributes['head'])] += 1

                    # 更新生肖频率
                    if attributes['zodiac']:
                        zodiac_frequency[attributes['zodiac']] += 1

                    # 更新五行频率
                    if attributes['wuxing']:
                        wuxing_frequency[attributes['wuxing']] += 1

                    # 更新单双属性
                    if attributes['is_odd']:
                        attributes_frequency['单'] += 1
                    else:
                        attributes_frequency['双'] += 1

                    # 更新大小属性
                    if attributes['is_big']:
                        attributes_frequency['大'] += 1
                    else:
                        attributes_frequency['小'] += 1

                    # 更新家禽野兽属性
                    if attributes['is_domestic']:
                        attributes_frequency['家禽'] += 1
                    else:
                        attributes_frequency['野兽'] += 1

                    # 更新尾数单双属性
                    tail = attributes['tail']
                    if tail % 2 == 1:
                        attributes_frequency['尾单'] += 1
                    else:
                        attributes_frequency['尾双'] += 1

                    # 更新尾数大小属性
                    if attributes['is_tail_big']:
                        attributes_frequency['尾大'] += 1
                    else:
                        attributes_frequency['尾小'] += 1

                    # 更新合数单双属性
                    if attributes['is_sum_odd']:
                        attributes_frequency['合单'] += 1
                    else:
                        attributes_frequency['合双'] += 1

                    # 更新合数大小属性
                    if attributes['is_sum_big']:
                        attributes_frequency['合大'] += 1
                    else:
                        attributes_frequency['合小'] += 1

                    # 更新遗漏值统计 - 修复逻辑
                    for num in range(1, 50):
                        num_str = str(num)
                        if num == special_number:
                            # 如果当前号码是特码，记录最后出现期数
                            last_appearance[num_str] = draw.expect
                            # 检查是否需要更新最大遗漏值
                            if temp_missing[num_str] > missing_max[num_str]:
                                missing_max[num_str] = temp_missing[num_str]
                            # 重置临时遗漏值
                            temp_missing[num_str] = 0

                            # 更新连号统计
                            consecutive_count[num_str] += 1
                            if consecutive_count[num_str] > max_consecutive[num_str]:
                                max_consecutive[num_str] = consecutive_count[num_str]
                        else:
                            # 如果当前号码不是特码，增加临时遗漏值
                            temp_missing[num_str] += 1
                            consecutive_count[num_str] = 0

                except Exception as e:
                    logger.error(
                        f"Error processing draw {draw.expect}: {str(e)}")
                    continue

            # 处理最终的遗漏值
            for num in range(1, 50):
                num_str = str(num)
                # 更新当前遗漏值
                missing_current[num_str] = temp_missing[num_str]
                # 检查是否需要更新最大遗漏值
                if temp_missing[num_str] > missing_max[num_str]:
                    missing_max[num_str] = temp_missing[num_str]

            # 计算平均间隔
            avg_intervals = {}
            for num in range(1, 50):
                num_str = str(num)
                count = number_frequency[num_str]
                if count > 1:
                    avg_intervals[num_str] = (len(sorted_draws) - 1) / count
                else:
                    avg_intervals[num_str] = len(
                        sorted_draws) if count == 1 else 0

            # 计算热门号码
            hot_numbers_all = sorted(
                [{"number": k, "frequency": v}
                    for k, v in number_frequency.items()],
                key=lambda x: (-x["frequency"], int(x["number"]))
            )

            # 获取最大出现次数
            max_frequency = hot_numbers_all[0]["frequency"] if hot_numbers_all else 0

            # 获取所有具有最大出现次数的号码
            hot_numbers = [
                num for num in hot_numbers_all if num["frequency"] == max_frequency]

            # 如果热门号码不足10个，添加次高频率的号码直到达到10个
            if len(hot_numbers) < 10:
                remaining_numbers = [
                    num for num in hot_numbers_all if num["frequency"] < max_frequency]
                hot_numbers.extend(remaining_numbers[:10-len(hot_numbers)])
            # 如果热门号码太多，限制为前10个
            elif len(hot_numbers) > 10:
                hot_numbers = hot_numbers[:10]

            # 计算冷门号码（包括出现次数为0的号码）
            cold_numbers_all = sorted(
                [{"number": k, "frequency": v}
                    for k, v in number_frequency.items()],
                key=lambda x: (x["frequency"], int(x["number"]))
            )

            # 获取最小出现次数
            min_frequency = cold_numbers_all[0]["frequency"] if cold_numbers_all else 0

            # 获取所有具有最小出现次数的号码
            cold_numbers = [
                num for num in cold_numbers_all if num["frequency"] == min_frequency]

            # 如果冷门号码太多，限制为前10个
            if len(cold_numbers) > 10:
                cold_numbers = cold_numbers[:10]

            # 计算遗漏值最大的号码
            max_missing_numbers = sorted(
                [{"number": k, "missing": missing_current[k]}
                    for k in missing_current],
                key=lambda x: (-x["missing"], int(x["number"]))
            )[:10]

            # 计算生肖走势
            zodiac_trend = self._calculate_zodiac_trend(sorted_draws)

            # 计算波色走势
            color_trend = self._calculate_color_trend(sorted_draws)

            # 构建返回结果
            return {
                "basicStats": {
                    "totalCount": len(sorted_draws),
                    "hotNumbers": hot_numbers,
                    "coldNumbers": cold_numbers,
                    "hotNumber": hot_numbers[0]["number"] if hot_numbers else None,
                    "hotNumberCount": hot_numbers[0]["frequency"] if hot_numbers else 0,
                    "coldNumber": cold_numbers[0]["number"] if cold_numbers else None,
                    "coldNumberCount": cold_numbers[0]["frequency"] if cold_numbers else 0,
                    "maxMissingNumbers": max_missing_numbers,
                    "averageIntervals": avg_intervals
                },
                "numberFrequency": dict(number_frequency),
                "colorFrequency": dict(color_frequency),
                "tailFrequency": dict(tail_frequency),
                "headFrequency": dict(head_frequency),
                "zodiacFrequency": dict(zodiac_frequency),
                "wuxingFrequency": dict(wuxing_frequency),
                "attributes": attributes_frequency,
                "missing": {
                    "current": dict(missing_current),
                    "max": dict(missing_max),
                    "lastAppearance": dict(last_appearance)
                },
                "consecutive": {
                    "current": dict(consecutive_count),
                    "max": dict(max_consecutive)
                },
                "trends": {
                    "zodiac": zodiac_trend,
                    "color": color_trend
                },
                "analysis": self._generate_analysis_summary(number_frequency, zodiac_frequency, color_frequency, missing_current)
            }

        except Exception as e:
            logger.error(f"Error calculating statistics: {str(e)}")
            return self._get_empty_statistics()

    def _get_empty_statistics(self) -> Dict[str, Any]:
        """返回空的统计数据结构"""
        return {
            "basicStats": {
                "totalCount": 0,
                "hotNumbers": [],
                "coldNumbers": [],
                "hotNumber": None,
                "hotNumberCount": 0,
                "coldNumber": None,
                "coldNumberCount": 0,
                "maxMissingNumbers": [],
                "averageIntervals": {}
            },
            "numberFrequency": {},
            "colorFrequency": {'红波': 0, '蓝波': 0, '绿波': 0},
            "tailFrequency": {str(i): 0 for i in range(10)},
            "headFrequency": {str(i): 0 for i in range(5)},
            "zodiacFrequency": {zodiac: 0 for zodiac in GameRules2025.ZODIAC_LIST},
            "wuxingFrequency": {wuxing: 0 for wuxing in GameRules2025.WUXING_LIST},
            "attributes": {
                "单": 0, "双": 0, "大": 0, "小": 0,
                "家禽": 0, "野兽": 0,
                "尾单": 0, "尾双": 0,
                "尾大": 0, "尾小": 0,
                "合单": 0, "合双": 0,
                "合大": 0, "合小": 0
            },
            "missing": {
                "current": {str(i): 0 for i in range(1, 50)},
                "max": {str(i): 0 for i in range(1, 50)},
                "lastAppearance": {str(i): None for i in range(1, 50)}
            },
            "consecutive": {
                "current": {str(i): 0 for i in range(1, 50)},
                "max": {str(i): 0 for i in range(1, 50)}
            },
            "trends": {
                "zodiac": {},
                "color": {}
            },
            "analysis": ""
        }

    def _calculate_zodiac_trend(self, draws: List[Draw], window_size: int = 10) -> Dict[str, List[int]]:
        """计算生肖走势"""
        if not draws or len(draws) < window_size:
            return {zodiac: [] for zodiac in GameRules2025.ZODIAC_LIST}

        # 按期号排序（升序）
        sorted_draws = sorted(draws, key=lambda x: x.expect)

        # 只取最近的30期数据用于走势分析
        recent_draws = sorted_draws[-30:] if len(sorted_draws) > 30 else sorted_draws

        # 初始化结果
        result = {zodiac: [] for zodiac in GameRules2025.ZODIAC_LIST}

        # 计算滚动窗口内的生肖频率
        for i in range(len(recent_draws) - window_size + 1):
            window_draws = recent_draws[i:i+window_size]
            zodiac_counts = defaultdict(int)

            for draw in window_draws:
                if not draw.open_code:
                    continue

                try:
                    numbers = [int(n.strip()) for n in draw.open_code.split(',') if n.strip().isdigit()]
                    if not numbers:
                        continue

                    special_number = numbers[-1]  # 特码
                    if special_number < 1 or special_number > 49:
                        continue

                    zodiac = self.game_rules.get_zodiac(special_number)
                    if zodiac:
                        zodiac_counts[zodiac] += 1
                except Exception as e:
                    logger.error(f"Error processing draw {draw.expect} for zodiac trend: {str(e)}")
                    continue

            # 更新每个生肖的频率
            for zodiac in GameRules2025.ZODIAC_LIST:
                result[zodiac].append(zodiac_counts[zodiac])

        return result

    def _calculate_color_trend(self, draws: List[Draw], window_size: int = 10) -> Dict[str, List[int]]:
        """计算波色走势"""
        if not draws or len(draws) < window_size:
            return {"红波": [], "蓝波": [], "绿波": []}

        # 按期号排序（升序）
        sorted_draws = sorted(draws, key=lambda x: x.expect)

        # 只取最近的30期数据用于走势分析
        recent_draws = sorted_draws[-30:] if len(sorted_draws) > 30 else sorted_draws

        # 初始化结果
        result = {"红波": [], "蓝波": [], "绿波": []}

        # 计算滚动窗口内的波色频率
        for i in range(len(recent_draws) - window_size + 1):
            window_draws = recent_draws[i:i+window_size]
            color_counts = defaultdict(int)

            for draw in window_draws:
                if not draw.open_code:
                    continue

                try:
                    numbers = [int(n.strip()) for n in draw.open_code.split(',') if n.strip().isdigit()]
                    if not numbers:
                        continue

                    special_number = numbers[-1]  # 特码
                    if special_number < 1 or special_number > 49:
                        continue

                    color = self.game_rules.get_color(special_number)
                    if color:
                        color_counts[color] += 1
                except Exception as e:
                    logger.error(f"Error processing draw {draw.expect} for color trend: {str(e)}")
                    continue

            # 更新每个波色的频率
            for color in ["红波", "蓝波", "绿波"]:
                result[color].append(color_counts[color])

        return result

    def _generate_analysis_summary(self, number_frequency: Dict[str, int],
                                   zodiac_frequency: Dict[str, int],
                                   color_frequency: Dict[str, int],
                                   missing_current: Dict[str, int]) -> str:
        """生成分析摘要"""
        try:
            # 获取热门号码
            hot_numbers_all = sorted(
                [(k, v) for k, v in number_frequency.items()],
                key=lambda x: (-x[1], int(x[0]))
            )

            # 获取最大出现次数
            max_frequency = hot_numbers_all[0][1] if hot_numbers_all else 0

            # 获取所有具有最大出现次数的号码
            hot_numbers = [(k, v)
                           for k, v in hot_numbers_all if v == max_frequency]

            # 如果热门号码太多，限制为前5个
            if len(hot_numbers) > 5:
                hot_numbers = hot_numbers[:5]

            # 获取冷门号码（包括出现次数为0的号码）
            cold_numbers_all = sorted(
                [(k, v) for k, v in number_frequency.items()],
                key=lambda x: (x[1], int(x[0]))
            )

            # 获取最小出现次数
            min_frequency = cold_numbers_all[0][1] if cold_numbers_all else 0

            # 获取所有具有最小出现次数的号码
            cold_numbers = [(k, v)
                            for k, v in cold_numbers_all if v == min_frequency]

            # 如果冷门号码太多，限制为前5个
            if len(cold_numbers) > 5:
                cold_numbers = cold_numbers[:5]

            # 获取遗漏值最大的号码
            max_missing = sorted(
                [(k, v) for k, v in missing_current.items()],
                key=lambda x: (-x[1], int(x[0]))
            )[:5]

            # 获取最热门的生肖
            hot_zodiacs = sorted(
                [(k, v) for k, v in zodiac_frequency.items()],
                key=lambda x: (-x[1], x[0])
            )[:3]

            # 获取最热门的波色
            hot_colors = sorted(
                [(k, v) for k, v in color_frequency.items()],
                key=lambda x: (-x[1], x[0])
            )[:3]

            # 生成摘要
            summary = "数据分析摘要:\n"

            # 热门号码分析
            summary += "1. 热门号码: "
            summary += ", ".join([f"{num}({freq}次)" for num,
                                 freq in hot_numbers])
            summary += "\n"

            # 冷门号码分析
            summary += "2. 冷门号码: "
            summary += ", ".join([f"{num}({freq}次)" for num,
                                 freq in cold_numbers])
            summary += "\n"

            # 遗漏值分析
            summary += "3. 遗漏值最大的号码: "
            summary += ", ".join([f"{num}({miss}期)" for num,
                                 miss in max_missing])
            summary += "\n"

            # 生肖分析
            summary += "4. 热门生肖: "
            summary += ", ".join([f"{zodiac}({freq}次)" for zodiac,
                                 freq in hot_zodiacs])
            summary += "\n"

            # 波色分析
            summary += "5. 热门波色: "
            summary += ", ".join([f"{color}({freq}次)" for color,
                                 freq in hot_colors])
            summary += "\n"

            return summary

        except Exception as e:
            logger.error(f"Error generating analysis summary: {str(e)}")
            return "无法生成分析摘要，数据不足或处理出错。"

    def calculate_missing_values(self, draws: List[Draw]) -> Dict[str, Dict[str, int]]:
        """计算遗漏值"""
        if not draws:
            return {
                "numbers": {str(i): 0 for i in range(1, 50)},
                "zodiacs": {zodiac: 0 for zodiac in GameRules2025.ZODIAC_LIST},
                "colors": {"红波": 0, "蓝波": 0, "绿波": 0}
            }

        # 按期号排序
        sorted_draws = sorted(draws, key=lambda x: x.expect)

        # 初始化遗漏值
        number_missing = {str(i): len(sorted_draws) for i in range(1, 50)}
        zodiac_missing = {zodiac: len(sorted_draws)
                          for zodiac in GameRules2025.ZODIAC_LIST}
        color_missing = {"红波": len(sorted_draws), "蓝波": len(
            sorted_draws), "绿波": len(sorted_draws)}

        # 计算遗漏值
        for i, draw in enumerate(sorted_draws):
            try:
                if not draw.special_number or draw.special_number < 1 or draw.special_number > 49:
                    continue

                # 更新号码遗漏值
                number_missing[str(draw.special_number)] = 0

                # 更新生肖遗漏值
                zodiac = self.game_rules.get_zodiac(draw.special_number)
                if zodiac:
                    zodiac_missing[zodiac] = 0

                # 更新波色遗漏值
                color = self.game_rules.get_color(draw.special_number)
                if color:
                    color_missing[color] = 0

                # 增加其他号码的遗漏值
                for num in range(1, 50):
                    if num != draw.special_number:
                        number_missing[str(num)] += 1

                # 增加其他生肖的遗漏值
                for z in GameRules2025.ZODIAC_LIST:
                    if z != zodiac:
                        zodiac_missing[z] += 1

                # 增加其他波色的遗漏值
                for c in ["红波", "蓝波", "绿波"]:
                    if c != color:
                        color_missing[c] += 1

            except Exception as e:
                logger.error(
                    f"Error calculating missing values for draw {draw.expect}: {str(e)}")
                continue

        return {
            "numbers": number_missing,
            "zodiacs": zodiac_missing,
            "colors": color_missing
        }

    def calculate_probability_distribution(self, draws: List[Draw], window_size: int = 100) -> Dict[str, float]:
        """计算概率分布"""
        if not draws or len(draws) < window_size:
            return {str(i): 1/49 for i in range(1, 50)}

        # 使用最近的window_size期数据
        recent_draws = sorted(draws, key=lambda x: x.expect)[-window_size:]

        # 计算频率
        frequency = defaultdict(int)
        for draw in recent_draws:
            if draw.special_number and 1 <= draw.special_number <= 49:
                frequency[str(draw.special_number)] += 1

        # 计算概率（加入平滑因子）
        alpha = 0.5  # 平滑因子
        probabilities = {}
        for i in range(1, 50):
            num_str = str(i)
            probabilities[num_str] = (
                frequency[num_str] + alpha) / (window_size + 49 * alpha)

        return probabilities

    def evaluate_prediction_accuracy(self, predictions: List[Dict[str, Any]], actual_results: List[int]) -> Dict[str, Any]:
        """评估预测准确度"""
        if not predictions or not actual_results or len(predictions) != len(actual_results):
            return {
                "overall_accuracy": 0.0,
                "hit_rate_top5": 0.0,
                "hit_rate_top10": 0.0,
                "hit_rate_top20": 0.0,
                "attribute_accuracy": 0.0,
                "details": []
            }

        total_predictions = len(predictions)
        hits_top5 = 0
        hits_top10 = 0
        hits_top20 = 0
        attribute_hits = 0
        details = []

        for i, (prediction, actual) in enumerate(zip(predictions, actual_results)):
            # 检查是否在不同范围的预测中命中
            hit_top5 = actual in prediction.get("special_numbers_5", [])
            hit_top10 = actual in prediction.get("special_numbers_10", [])
            hit_top20 = actual in prediction.get("special_numbers_20", [])

            if hit_top5:
                hits_top5 += 1
            if hit_top10:
                hits_top10 += 1
            if hit_top20:
                hits_top20 += 1

            # 检查属性预测是否准确
            actual_attrs = self.game_rules.get_number_attributes(actual)
            attr_hits = 0
            attr_total = 0

            if "attribute_predictions" in prediction:
                attr_pred = prediction["attribute_predictions"]

                # 检查单双预测
                if "special_odd_even" in attr_pred:
                    attr_total += 1
                    if attr_pred["special_odd_even"] == ("单" if actual_attrs["is_odd"] else "双"):
                        attr_hits += 1

                # 检查大小预测
                if "special_big_small" in attr_pred:
                    attr_total += 1
                    if attr_pred["special_big_small"] == ("大" if actual_attrs["is_big"] else "小"):
                        attr_hits += 1

                # 检查波色预测
                if "special_color" in attr_pred:
                    attr_total += 1
                    if attr_pred["special_color"] == actual_attrs["color"]:
                        attr_hits += 1

            # 计算属性预测准确率
            attr_accuracy = attr_hits / attr_total if attr_total > 0 else 0
            attribute_hits += attr_accuracy

            # 添加详细信息
            details.append({
                "prediction_id": i + 1,
                "hit_top5": hit_top5,
                "hit_top10": hit_top10,
                "hit_top20": hit_top20,
                "attribute_accuracy": attr_accuracy,
                "actual_number": actual
            })

        return {
            "overall_accuracy": (hits_top20 / total_predictions) * 0.5 + (attribute_hits / total_predictions) * 0.5,
            "hit_rate_top5": hits_top5 / total_predictions,
            "hit_rate_top10": hits_top10 / total_predictions,
            "hit_rate_top20": hits_top20 / total_predictions,
            "attribute_accuracy": attribute_hits / total_predictions,
            "details": details
        }
