from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import Dict, List
from app.models.draw import Draw
from datetime import datetime, <PERSON><PERSON><PERSON>


def get_number_frequency(db: Session) -> Dict[str, int]:
    """获取号码出现频率统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化频率字典
    frequency = {str(i): 0 for i in range(1, 50)}

    # 统计每个号码出现的次数
    for draw in draws:
        if draw.numbers:
            for num in draw.numbers:
                frequency[str(num)] = frequency.get(str(num), 0) + 1

    return frequency


def get_zodiac_statistics(db: Session) -> Dict[str, int]:
    """获取生肖统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化生肖统计字典
    zodiac_stats = {
        "鼠": 0, "牛": 0, "虎": 0, "兔": 0, "龙": 0, "蛇": 0,
        "马": 0, "羊": 0, "猴": 0, "鸡": 0, "狗": 0, "猪": 0
    }

    # 统计每个生肖出现的次数
    for draw in draws:
        if draw.zodiac:
            zodiac_stats[draw.zodiac] = zodiac_stats.get(draw.zodiac, 0) + 1

    return zodiac_stats


def get_color_statistics(db: Session) -> Dict[str, int]:
    """获取波色统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化波色统计字典
    color_stats = {"红": 0, "蓝": 0, "绿": 0}

    # 统计每个波色出现的次数
    for draw in draws:
        if draw.color:
            color_stats[draw.color] = color_stats.get(draw.color, 0) + 1

    return color_stats


def get_odd_even_statistics(db: Session) -> Dict[str, int]:
    """获取单双统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化单双统计字典
    odd_even_stats = {"单": 0, "双": 0}

    # 统计单双出现的次数
    for draw in draws:
        if draw.odd_even:
            odd_even_stats[draw.odd_even] = odd_even_stats.get(
                draw.odd_even, 0) + 1

    return odd_even_stats


def get_big_small_statistics(db: Session) -> Dict[str, int]:
    """获取大小统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化大小统计字典
    big_small_stats = {"大": 0, "小": 0}

    # 统计大小出现的次数
    for draw in draws:
        if draw.big_small:
            big_small_stats[draw.big_small] = big_small_stats.get(
                draw.big_small, 0) + 1

    return big_small_stats


def get_tail_big_small_statistics(db: Session) -> Dict[str, int]:
    """获取尾大小统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化尾大小统计字典
    tail_stats = {"尾大": 0, "尾小": 0}

    # 统计尾大小出现的次数
    for draw in draws:
        if draw.tail_big_small:
            tail_stats[draw.tail_big_small] = tail_stats.get(
                draw.tail_big_small, 0) + 1

    return tail_stats


def get_sum_odd_even_statistics(db: Session) -> Dict[str, int]:
    """获取和单双统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化和单双统计字典
    sum_stats = {"和单": 0, "和双": 0}

    # 统计和单双出现的次数
    for draw in draws:
        if draw.sum_odd_even:
            sum_stats[draw.sum_odd_even] = sum_stats.get(
                draw.sum_odd_even, 0) + 1

    return sum_stats


def get_wuxing_statistics(db: Session) -> Dict[str, int]:
    """获取五行统计"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 初始化五行统计字典
    wuxing_stats = {"金": 0, "木": 0, "水": 0, "火": 0, "土": 0}

    # 统计五行出现的次数
    for draw in draws:
        if draw.wuxing:
            wuxing_stats[draw.wuxing] = wuxing_stats.get(draw.wuxing, 0) + 1

    return wuxing_stats


def get_hot_numbers(db: Session, limit: int = 10) -> List[Dict]:
    """获取热门号码"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 统计号码出现次数
    number_count = {}
    for draw in draws:
        if draw.numbers:
            for num in draw.numbers:
                number_count[num] = number_count.get(num, 0) + 1

    # 按出现次数排序
    hot_numbers = sorted(number_count.items(),
                         key=lambda x: x[1], reverse=True)[:limit]

    return [{"number": num, "count": count} for num, count in hot_numbers]


def get_cold_numbers(db: Session, limit: int = 10) -> List[Dict]:
    """获取冷门号码"""
    # 获取所有开奖记录
    draws = db.query(Draw).all()

    # 统计号码出现次数
    number_count = {}
    for draw in draws:
        if draw.numbers:
            for num in draw.numbers:
                number_count[num] = number_count.get(num, 0) + 1

    # 按出现次数排序
    cold_numbers = sorted(number_count.items(), key=lambda x: x[1])[:limit]

    return [{"number": num, "count": count} for num, count in cold_numbers]


def get_recent_trends(db: Session, days: int = 30) -> Dict:
    """获取近期走势"""
    # 计算起始日期
    start_date = datetime.now() - timedelta(days=days)

    # 获取近期开奖记录
    recent_draws = db.query(Draw).filter(Draw.draw_time >= start_date).all()

    # 统计各项数据
    trends = {
        "number_frequency": get_number_frequency(db),
        "zodiac_stats": get_zodiac_statistics(db),
        "color_stats": get_color_statistics(db),
        "odd_even_stats": get_odd_even_statistics(db),
        "big_small_stats": get_big_small_statistics(db),
        "hot_numbers": get_hot_numbers(db),
        "cold_numbers": get_cold_numbers(db)
    }

    return trends
