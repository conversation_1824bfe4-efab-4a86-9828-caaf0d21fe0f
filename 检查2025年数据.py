#!/usr/bin/env python3
"""
检查2025年144期的具体数据
"""
import json

def analyze_2025_data():
    """分析2025年的数据"""
    print("=== 2025年144期数据分析 ===")
    
    try:
        with open('2025_stats.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        basic_stats = data['data']['basicStats']
        number_freq = data['data']['numberFrequency']
        
        print(f"📊 统计概况:")
        print(f"   总期数: {basic_stats['totalCount']}期")
        print(f"   有效期数: {basic_stats['validCount']}期")
        print(f"   统计范围: {data['data']['period_range']}")
        
        print(f"\n🔢 所有号码在2025年的出现次数:")
        all_counts = []
        for num in range(1, 50):
            count = number_freq.get(str(num), 0)
            all_counts.append((num, count))
            print(f"   号码{num:02d}: {count}次")
        
        # 按出现次数排序
        all_counts.sort(key=lambda x: x[1])
        
        print(f"\n🧊 最冷号码排序:")
        for i, (num, count) in enumerate(all_counts[:10]):
            print(f"   {i+1}. 号码{num:02d}: {count}次")
        
        # 检查16、17、19
        check_numbers = [16, 17, 19]
        print(f"\n🔍 检查号码 {check_numbers} 在2025年的情况:")
        for num in check_numbers:
            count = number_freq.get(str(num), 0)
            print(f"   号码{num:02d}: {count}次")
        
        # 找出0次的号码
        zero_count = [num for num, count in all_counts if count == 0]
        if zero_count:
            print(f"\n❄️ 2025年出现0次的号码: {zero_count}")
        else:
            print(f"\n✅ 2025年没有出现0次的号码")
        
        # 最少出现次数
        min_count = min(count for _, count in all_counts)
        min_numbers = [num for num, count in all_counts if count == min_count]
        
        print(f"\n🧊 2025年最少出现次数: {min_count}次")
        print(f"🔢 2025年最冷号码: {min_numbers}")
        
        # API返回的冷门号码
        print(f"\n📋 API返回的2025年冷门号码:")
        cold_numbers = data['data']['basicStats']['coldNumbers']
        for i, num_data in enumerate(cold_numbers[:10]):
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def explain_difference():
    """解释数据差异"""
    print(f"\n=== 数据范围差异说明 ===")
    
    print(f"🎯 您看到的统计范围:")
    print(f"   • 年份: 2025年")
    print(f"   • 期数: 第1期到第144期")
    print(f"   • 总期数: 144期")
    
    print(f"\n🎯 我之前分析的范围:")
    print(f"   • 年份: 2020年-2025年")
    print(f"   • 期数: 2020130期到2025134期")
    print(f"   • 总期数: 1831期")
    
    print(f"\n✅ 结论:")
    print(f"   • 您的观察是正确的")
    print(f"   • 在2025年144期的范围内，16、17、19可能确实很少出现或没出现")
    print(f"   • 统计范围不同会导致结果不同")
    print(f"   • 前端应该根据用户选择的时间范围显示对应的统计结果")

def main():
    """主函数"""
    print("🔧 2025年数据分析开始...")
    
    # 分析2025年数据
    if analyze_2025_data():
        print("✅ 2025年数据分析完成")
    else:
        print("❌ 2025年数据分析失败")
    
    # 解释差异
    explain_difference()

if __name__ == "__main__":
    main()
