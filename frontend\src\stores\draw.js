import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import request from '@/utils/request'
import { ElMessage, ElLoading } from 'element-plus'
import { getDraws, getLatestDraw, getHistoryDraws, getStatistics, exportDraws } from '@/api/draw'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

export const useDrawStore = defineStore('draw', {
  state: () => ({
    loading: false,
    historyDraws: [],
    total: 0,
    currentPage: 1,
    pageSize: 20,
    exportLoading: false,
    latestDraw: null,
    statistics: null
  }),

  actions: {
    async fetchLatestDraw() {
      try {
        const response = await getLatestDraw()
        this.latestDraw = response.data
        return response
      } catch (error) {
        console.error('Failed to fetch latest draw:', error)
        throw error
      }
    },

    async fetchHistoryDraws(params) {
      try {
        this.loading = true
        console.log('Fetching history draws with params:', params)

        const response = await getHistoryDraws(params)
        console.log('History draws API response:', response)

        if (response && response.code === 200) {
          console.log('History draws data:', response.data)
          console.log('History draws total:', response.total)

          this.historyDraws = Array.isArray(response.data) ? response.data : []
          this.total = response.total || 0

          console.log('Updated store state - historyDraws:', this.historyDraws.length, 'items, total:', this.total)
        } else {
          console.error('Invalid response format:', response)
          throw new Error(response?.message || 'Invalid response format')
        }
        return response
      } catch (error) {
        console.error('Failed to fetch history draws:', error)
        this.historyDraws = []
        this.total = 0
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchStatistics(params) {
      try {
        this.loading = true
        console.log('Calling statistics API with params:', params);
        const response = await getStatistics(params)

        console.log('统计数据API原始响应:', JSON.stringify(response).substring(0, 500) + '...');

        if (response && response.code === 200) {
          console.log('统计数据API标准格式响应，数据:', JSON.stringify(response.data).substring(0, 500) + '...');
          this.statistics = response.data;
          return response;
        } else if (response) {
          // 处理直接返回数据的情况
          console.log('统计数据API非标准格式响应，直接使用响应作为数据');
          this.statistics = response;
          return {
            code: 200,
            data: response,
            message: 'success'
          };
        } else {
          console.error('统计数据API返回空响应');
          throw new Error('获取统计数据失败: API返回空响应');
        }
      } catch (error) {
        console.error('Failed to fetch statistics:', error)
        ElMessage.error('获取统计数据失败，请检查网络连接: ' + (error.message || '未知错误'));
        throw error
      } finally {
        this.loading = false
      }
    },

    async exportData(params) {
      try {
        const response = await exportDraws(params)
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `开奖记录_${new Date().toISOString().split('T')[0]}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('Failed to export data:', error)
        throw error
      }
    },

    setCurrentPage(page) {
      this.currentPage = page
    },

    setPageSize(size) {
      this.pageSize = size
      this.currentPage = 1 // 重置页码
    },

    resetPagination() {
      this.currentPage = 1
      this.pageSize = 20
    }
  }
})