import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
from datetime import datetime
import time
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class DataFetcher:
    def __init__(self):
        self.base_url = "https://api.macaumarksix.com/history/macaujc2/y"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 最大重试次数
            backoff_factor=1,  # 重试间隔
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
        )
        
        # 创建会话对象
        self.session = requests.Session()
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)
        
        # 禁用SSL验证
        self.session.verify = False

    def fetch_year_data(self, year):
        """获取指定年份的数据"""
        try:
            url = f"{self.base_url}/{year}"
            response = self.session.get(url, headers=self.headers)
            response.raise_for_status()
            
            # 打印原始响应以便调试
            logger.debug(f"API Response: {response.text[:500]}...")  # 只打印前500个字符
            
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    records = data['data']
                    if isinstance(records, list):
                        logger.info(f"成功获取 {year} 年数据，共 {len(records)} 条记录")
                        return records
                    
                logger.warning(f"{year} 年数据格式不正确: {type(data)}")
                return []
                    
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                return []
                
        except Exception as e:
            logger.error(f"获取 {year} 年数据失败: {str(e)}")
            return []

    def process_record(self, record):
        """处理单条记录"""
        try:
            logger.debug(f"处理记录: {record}")  # 打印原始记录以便调试
            
            # 解析开奖号码
            numbers = record.get('openCode', '').split(',')
            if not numbers:
                logger.warning(f"记录缺少开奖号码: {record}")
                return None
                
            main_numbers = [n.zfill(2) for n in numbers[:6]]  # 补零到两位数
            special_number = numbers[6].zfill(2) if len(numbers) > 6 else None

            # 解析生肖
            zodiacs = record.get('zodiac', '').split(',')
            main_zodiacs = zodiacs[:6] if zodiacs else []
            special_zodiac = zodiacs[6] if len(zodiacs) > 6 else None

            # 解析波色
            waves = record.get('wave', '').split(',')
            main_waves = waves[:6] if waves else []
            special_wave = waves[6] if len(waves) > 6 else None

            # 解析时间
            try:
                draw_time = datetime.strptime(record['openTime'], '%Y-%m-%d %H:%M:%S')
            except (KeyError, ValueError):
                logger.warning(f"无效的开奖时间格式: {record.get('openTime')}")
                draw_time = datetime.now()

            return {
                'period': record.get('expect', ''),
                'draw_time': draw_time,
                'numbers': ','.join(main_numbers),
                'special_number': special_number,
                'zodiac': ','.join(main_zodiacs) if main_zodiacs else None,
                'special_zodiac': special_zodiac,
                'color': ','.join(main_waves) if main_waves else None,
                'special_color': special_wave
            }
        except Exception as e:
            logger.error(f"处理记录失败: {str(e)}, 记录: {record}")
            return None

    def fetch_historical_data(self, start_year=2020, end_year=2025):
        """获取历史数据"""
        all_data = []
        years = list(range(start_year, end_year + 1))
        
        # 先获取最近的数据
        years.reverse()
        
        for year in years:
            logger.info(f"开始获取 {year} 年数据...")
            year_data = self.fetch_year_data(year)
            
            for record in year_data:
                processed_record = self.process_record(record)
                if processed_record:
                    all_data.append(processed_record)
                    logger.debug(f"成功处理记录: {processed_record['period']}")
            
            # 添加延时避免请求过快
            time.sleep(2)  # 增加延时到2秒
        
        logger.info(f"数据获取完成，共获取 {len(all_data)} 条记录")
        return all_data 

