from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date

from ..database import get_db
from ..models import Draw
from ..schemas.draw import (
    DrawCreate,
    DrawUpdate,
    DrawResponse,
    NextDrawInfoResponse,  # Import the new model
    DrawHistoryResponse,
    DrawStatisticsResponse,
    NumberAttributesResponse,
    DrawAnalysisResponse
)
from ..services.draw import DrawService

router = APIRouter(prefix="/api/draw", tags=["draw"])


@router.get("/history")
async def get_draw_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    number: Optional[int] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取开奖历史记录"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(
        f"获取历史开奖记录: page={page}, page_size={page_size}, start_date={start_date}, end_date={end_date}, start_period={start_period}, end_period={end_period}")

    service = DrawService(db)
    result = await service.get_history(
        page=page,
        page_size=page_size,
        start_date=start_date,
        end_date=end_date,
        start_period=start_period,
        end_period=end_period,
        number=number,
        zodiac=zodiac,
        color=color,
        odd_even=odd_even,
        big_small=big_small,
        tail_big_small=tail_big_small,
        sum_odd_even=sum_odd_even,
        animal_type=animal_type,
        wuxing=wuxing
    )

    # 转换为前端期望的格式
    response = {
        "code": 200,
        "message": "success",
        "data": result["items"],
        "total": result["total"],
        "page": result["page"],
        "page_size": result["page_size"]
    }

    logger.info(
        f"返回历史开奖记录: 总数={result['total']}, 当前页={result['page']}, 每页条数={result['page_size']}, 数据条数={len(result['items'])}")

    return response


@router.get("/latest")
async def get_latest_draws(
    limit: int = Query(5, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """获取最新开奖结果"""
    service = DrawService(db)
    results = await service.get_latest(limit)

    # 转换为前端期望的格式
    return {
        "code": 200,
        "message": "success",
        "data": results
    }


@router.get("/next")
async def get_next_draw(db: Session = Depends(get_db)):
    """获取下一期信息"""
    service = DrawService(db)
    result = await service.get_next()

    # 转换为前端期望的格式
    return {
        "code": 200,
        "message": "success",
        "data": result
    }


@router.get("/statistics")
async def get_draw_statistics(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    year: Optional[int] = None,
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取开奖统计数据"""
    service = DrawService(db)
    try:
        result = await service.get_statistics(
            start_date=start_date,
            end_date=end_date,
            year=year,
            start_period=start_period,
            end_period=end_period
        )

        # 转换为前端期望的格式
        return {
            "code": 200,
            "message": "success",
            "data": result
        }
    except HTTPException as e:
        return {
            "code": e.status_code,
            "message": e.detail,
            "data": None
        }
    except Exception as e:
        return {
            "code": 500,
            "message": str(e),
            "data": None
        }


@router.get("/history/{expect}", response_model=DrawResponse)
async def get_draw_by_expect(
    expect: str,
    db: Session = Depends(get_db)
):
    """获取特定期号的开奖结果"""
    service = DrawService(db)
    draw = await service.get_by_expect(expect)
    if not draw:
        raise HTTPException(status_code=404, detail="Draw not found")
    return draw


@router.get("/number/{number}/attributes", response_model=NumberAttributesResponse)
async def get_number_attributes(
    number: int,
    db: Session = Depends(get_db)
):
    """获取号码属性"""
    service = DrawService(db)
    return await service.get_number_attributes(number)


@router.get("/suggestions", response_model=List[int])
async def get_number_suggestions(
    count: int = Query(5, ge=1, le=10),
    db: Session = Depends(get_db)
):
    """获取选号建议"""
    service = DrawService(db)
    return await service.get_suggestions(count)


@router.get("/analysis", response_model=DrawAnalysisResponse)
async def get_analysis(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    period_count: Optional[int] = Query(None, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """获取智能分析结果"""
    service = DrawService(db)
    return await service.get_analysis(
        start_date=start_date,
        end_date=end_date,
        period_count=period_count
    )


@router.put("/update", response_model=DrawResponse)
async def update_draw(
    draw: DrawUpdate,
    db: Session = Depends(get_db)
):
    """更新开奖数据"""
    service = DrawService(db)
    updated_draw = await service.update(draw)
    if not updated_draw:
        raise HTTPException(status_code=404, detail="Draw not found")
    return updated_draw


@router.post("/import")
async def import_draws(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入开奖数据"""
    service = DrawService(db)
    try:
        result = await service.import_data(file)
        return {"message": "Import successful", "imported_count": result}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/export")
async def export_draws(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    number: Optional[int] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """导出开奖数据"""
    service = DrawService(db)
    return await service.export_data(
        start_date=start_date,
        end_date=end_date,
        start_period=start_period,
        end_period=end_period,
        number=number,
        zodiac=zodiac,
        color=color,
        odd_even=odd_even,
        big_small=big_small,
        tail_big_small=tail_big_small,
        sum_odd_even=sum_odd_even,
        animal_type=animal_type,
        wuxing=wuxing
    )
