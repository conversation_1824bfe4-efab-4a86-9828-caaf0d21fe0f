from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.init_db import get_db
from app.schemas.draw import DrawCreate, DrawResponse

router = APIRouter()


@router.get("/", response_model=List[DrawResponse])
def get_draws(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取开奖记录列表"""
    return []


@router.post("/", response_model=DrawResponse)
def create_draw(
    draw: DrawCreate,
    db: Session = Depends(get_db)
):
    """创建新的开奖记录"""
    return {}
