<template>
  <div class="chart-container">
    <div ref="chartRef" style="height: 400px"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  const option = {
    title: {
      text: '号码分布趋势'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({length: 49}, (_, i) => i + 1),
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '出现次数',
      type: 'bar',
      data: calculateDistribution(props.data)
    }]
  }
  
  chart.setOption(option)
}

const calculateDistribution = (data) => {
  const distribution = new Array(49).fill(0)
  data.forEach(num => {
    if (num >= 1 && num <= 49) {
      distribution[num - 1]++
    }
  })
  return distribution
}

watch(() => props.data, () => {
  initChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', () => {
    chart?.resize()
  })
})
</script> 