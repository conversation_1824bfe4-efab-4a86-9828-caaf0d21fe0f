from functools import wraps
from flask import request, jsonify, current_app
import jwt
from datetime import datetime, timedelta
from models.database import DatabaseManager
from utils.logger import setup_logger

logger = setup_logger('auth')
db = DatabaseManager()


def generate_token(user_id, role):
    """生成JWT令牌"""
    try:
        payload = {
            'user_id': user_id,
            'role': role,
            'exp': datetime.utcnow() + timedelta(days=1)
        }
        return jwt.encode(payload, current_app.config['SECRET_KEY'], algorithm='HS256')
    except Exception as e:
        logger.error(f"Token生成失败: {str(e)}")
        raise


def verify_token(token):
    """验证JWT令牌"""
    try:
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )
        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("Token已过期")
        return None

    except jwt.InvalidTokenError:
        logger.warning("无效的Token")
        return None


def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '需要登录'}), 401

        try:
            payload = jwt.decode(
                token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            request.user = payload
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Token已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': '无效的Token'}), 401

        return f(*args, **kwargs)
    return decorated


def role_required(roles):
    """角色验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            if not request.user:
                logger.warning("未找到用户信息")
                return jsonify({
                    'success': False,
                    'message': '需要登录'
                }), 401

            if request.user['role'] not in roles:
                logger.warning(f"用户权限不足: {request.user['role']}")
                return jsonify({
                    'success': False,
                    'message': '权限不足'
                }), 403

            return f(*args, **kwargs)
        return decorated
    return decorator


