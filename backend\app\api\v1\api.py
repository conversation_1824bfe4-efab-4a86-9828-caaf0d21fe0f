from fastapi import APIRouter
from app.api.v1.endpoints import draws, predictions, statistics, users

api_router = APIRouter()

api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(draws.router, prefix="/draws", tags=["draws"])
api_router.include_router(
    predictions.router, prefix="/predictions", tags=["predictions"])
api_router.include_router(
    statistics.router, prefix="/statistics", tags=["statistics"])
