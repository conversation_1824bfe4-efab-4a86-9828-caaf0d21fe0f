<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script setup>
import { provide } from 'vue'
import { ElMessage } from 'element-plus'

// 提供全局的消息提示方法
provide('showMessage', (message, type = 'info') => {
  ElMessage({
    message,
    type,
    duration: 3000
  })
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #2c3e50;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-content {
  padding: 60px 20px 20px;
  flex: 1;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
}

a {
  text-decoration: none;
  color: inherit;
}
</style>