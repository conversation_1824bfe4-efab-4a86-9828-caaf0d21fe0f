from flask import jsonify
import logging

logger = logging.getLogger(__name__)


def init_error_handlers(app):
    """初始化全局错误处理器"""

    @app.errorhandler(404)
    def not_found_error(error):
        logger.warning(f"404 错误: {error}")
        return jsonify({
            'code': 404,
            'message': '请求的资源不存在',
            'error': str(error)
        }), 404

    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"500 错误: {error}")
        return jsonify({
            'code': 500,
            'message': '服务器内部错误',
            'error': str(error)
        }), 500

    @app.errorhandler(400)
    def bad_request_error(error):
        logger.warning(f"400 错误: {error}")
        return jsonify({
            'code': 400,
            'message': '错误的请求',
            'error': str(error)
        }), 400

    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        logger.error(f"未预期的错误: {error}")
        return jsonify({
            'code': 500,
            'message': '服务器发生未预期的错误',
            'error': str(error)
        }), 500
