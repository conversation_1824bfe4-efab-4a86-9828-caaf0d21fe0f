import sqlite3

conn = sqlite3.connect('backend/marksix.db')
cursor = conn.cursor()

# 检查按不同方式排序的结果
print('=== 按draw_time排序（降序）===')
cursor.execute('SELECT expect, draw_time FROM draws WHERE expect LIKE "2025%" ORDER BY draw_time DESC LIMIT 10')
for row in cursor.fetchall():
    print(f'期号: {row[0]}, 时间: {row[1]}')

print('\n=== 按expect排序（降序）===')
cursor.execute('SELECT expect, draw_time FROM draws WHERE expect LIKE "2025%" ORDER BY expect DESC LIMIT 10')
for row in cursor.fetchall():
    print(f'期号: {row[0]}, 时间: {row[1]}')

print('\n=== 按id排序（降序）===')
cursor.execute('SELECT expect, draw_time, id FROM draws WHERE expect LIKE "2025%" ORDER BY id DESC LIMIT 10')
for row in cursor.fetchall():
    print(f'期号: {row[0]}, 时间: {row[1]}, ID: {row[2]}')

conn.close()
