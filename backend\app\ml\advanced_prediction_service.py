"""
高级预测服务，集成所有优化模块
"""
import logging
import numpy as np
import pandas as pd
import json
import random
import os
import joblib
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import GridSearchCV
import xgboost as xgb
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset

from ..database import SessionLocal
from ..models.prediction import ModelTrainingHistory, Prediction
from ..utils.game_rules import GameRules2025
from ..services.data_sync_service import DataSyncService
from .real_prediction_service import RealPredictionService
from .prediction_explanation import PredictionExplainer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 模型保存路径
MODEL_DIR = Path(__file__).parent / "advanced_models"
if not MODEL_DIR.exists():
    MODEL_DIR.mkdir(parents=True)

# LSTM模型定义
class LSTMModel(nn.Module):
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 2, dropout: float = 0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, 1)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out

class AdvancedPredictionService:
    """高级预测服务，集成所有优化模块"""

    def __init__(self):
        self.sequence_length = 20  # 增加序列长度以捕获更多模式
        self.game_rules = GameRules2025()
        self.data_sync_service = DataSyncService()
        self.base_prediction_service = RealPredictionService()
        self.prediction_explainer = PredictionExplainer(self.game_rules)

        logger.info(f"初始化高级预测服务，序列长度: {self.sequence_length}")

        # 模型字典
        self.models = {
            "rf": None,
            "xgboost": None,
            "gbdt": None,
            "lstm": None
        }

        # 特征工程器
        self.scaler = None
        self.feature_names = []

        # 模型权重
        self.model_weights = {
            "rf": 0.25,
            "xgboost": 0.25,
            "gbdt": 0.25,
            "lstm": 0.25
        }

        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 尝试加载已保存的模型
        self._load_models()

        logger.info(f"AdvancedPredictionService initialized on device: {self.device}")

    def _load_models(self):
        """加载已保存的模型"""
        try:
            rf_path = MODEL_DIR / "rf_model.joblib"
            xgb_path = MODEL_DIR / "xgb_model.joblib"
            gbdt_path = MODEL_DIR / "gbdt_model.joblib"
            lstm_path = MODEL_DIR / "lstm_model.pt"
            scaler_path = MODEL_DIR / "scaler.joblib"
            weights_path = MODEL_DIR / "model_weights.json"

            # 检查模型文件是否存在
            models_exist = rf_path.exists() and xgb_path.exists() and gbdt_path.exists() and scaler_path.exists()

            if not models_exist:
                logger.info("未找到已保存的高级预测模型，将在训练时创建新模型")
                return

            # 加载传统机器学习模型
            try:
                self.models["rf"] = joblib.load(rf_path)
                logger.info("成功加载随机森林模型")
            except Exception as e:
                logger.error(f"加载随机森林模型失败: {str(e)}")
                self.models["rf"] = None

            try:
                self.models["xgboost"] = joblib.load(xgb_path)
                logger.info("成功加载XGBoost模型")
            except Exception as e:
                logger.error(f"加载XGBoost模型失败: {str(e)}")
                self.models["xgboost"] = None

            try:
                self.models["gbdt"] = joblib.load(gbdt_path)
                logger.info("成功加载GBDT模型")
            except Exception as e:
                logger.error(f"加载GBDT模型失败: {str(e)}")
                self.models["gbdt"] = None

            try:
                self.scaler = joblib.load(scaler_path)
                logger.info("成功加载特征缩放器")
            except Exception as e:
                logger.error(f"加载特征缩放器失败: {str(e)}")
                self.scaler = None

            # 加载LSTM模型
            if lstm_path.exists():
                try:
                    # 加载模型架构和参数
                    lstm_state = torch.load(lstm_path, map_location=self.device)
                    input_size = lstm_state.get("input_size", 10)
                    hidden_size = lstm_state.get("hidden_size", 64)
                    num_layers = lstm_state.get("num_layers", 2)

                    # 创建模型实例
                    self.models["lstm"] = LSTMModel(input_size, hidden_size, num_layers).to(self.device)
                    self.models["lstm"].load_state_dict(lstm_state["model_state_dict"])
                    self.models["lstm"].eval()  # 设置为评估模式
                    logger.info("成功加载LSTM模型")
                except Exception as e:
                    logger.error(f"加载LSTM模型失败: {str(e)}")
                    self.models["lstm"] = None

            # 加载模型权重
            if weights_path.exists():
                try:
                    with open(weights_path, 'r') as f:
                        self.model_weights = json.load(f)
                    logger.info("成功加载模型权重")
                except Exception as e:
                    logger.error(f"加载模型权重失败: {str(e)}")

            # 检查是否成功加载了任何模型
            if any(self.models.values()):
                logger.info("成功加载部分或全部高级预测模型")
            else:
                logger.warning("所有模型加载失败，将在训练时创建新模型")
        except Exception as e:
            logger.error(f"加载高级预测模型失败: {str(e)}")

    def _save_models(self):
        """保存模型到文件"""
        try:
            # 确保模型目录存在
            if not MODEL_DIR.exists():
                MODEL_DIR.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建模型目录: {MODEL_DIR}")

            # 保存随机森林模型
            if self.models["rf"] is not None:
                try:
                    joblib.dump(self.models["rf"], MODEL_DIR / "rf_model.joblib")
                    logger.info("成功保存随机森林模型")
                except Exception as e:
                    logger.error(f"保存随机森林模型失败: {str(e)}")

            # 保存XGBoost模型
            if self.models["xgboost"] is not None:
                try:
                    joblib.dump(self.models["xgboost"], MODEL_DIR / "xgb_model.joblib")
                    logger.info("成功保存XGBoost模型")
                except Exception as e:
                    logger.error(f"保存XGBoost模型失败: {str(e)}")

            # 保存GBDT模型
            if self.models["gbdt"] is not None:
                try:
                    joblib.dump(self.models["gbdt"], MODEL_DIR / "gbdt_model.joblib")
                    logger.info("成功保存GBDT模型")
                except Exception as e:
                    logger.error(f"保存GBDT模型失败: {str(e)}")

            # 保存特征缩放器
            if self.scaler is not None:
                try:
                    joblib.dump(self.scaler, MODEL_DIR / "scaler.joblib")
                    logger.info("成功保存特征缩放器")
                except Exception as e:
                    logger.error(f"保存特征缩放器失败: {str(e)}")

            # 保存LSTM模型
            if self.models["lstm"] is not None:
                try:
                    # 获取模型架构信息
                    input_size = self.feature_names and len(self.feature_names) or 10
                    hidden_size = self.models["lstm"].hidden_size
                    num_layers = self.models["lstm"].num_layers

                    # 保存模型状态和架构信息
                    torch.save({
                        "model_state_dict": self.models["lstm"].state_dict(),
                        "input_size": input_size,
                        "hidden_size": hidden_size,
                        "num_layers": num_layers
                    }, MODEL_DIR / "lstm_model.pt")
                    logger.info("成功保存LSTM模型")
                except Exception as e:
                    logger.error(f"保存LSTM模型失败: {str(e)}")

            # 保存模型权重
            try:
                with open(MODEL_DIR / "model_weights.json", 'w') as f:
                    json.dump(self.model_weights, f)
                logger.info("成功保存模型权重")
            except Exception as e:
                logger.error(f"保存模型权重失败: {str(e)}")

            logger.info("模型保存完成")
        except Exception as e:
            logger.error(f"保存高级预测模型失败: {str(e)}")

    def _engineer_features(self, data: List[int]) -> Tuple[np.ndarray, List[str]]:
        """高级特征工程"""
        try:
            # 验证输入数据
            if not data:
                logger.warning("输入数据为空，使用随机数据")
                data = [random.randint(1, 49) for _ in range(self.sequence_length * 2)]

            # 过滤无效数据
            valid_data = []
            for num in data:
                try:
                    num_int = int(num)
                    if 1 <= num_int <= 49:
                        valid_data.append(num_int)
                    else:
                        logger.warning(f"过滤超出范围的数据: {num}")
                except (ValueError, TypeError):
                    logger.warning(f"过滤无效数据: {num}")

            # 如果有效数据为空，使用随机数据
            if not valid_data:
                logger.warning("有效数据为空，使用随机数据")
                valid_data = [random.randint(1, 49) for _ in range(self.sequence_length * 2)]

            # 确保数据足够长
            if len(valid_data) < self.sequence_length * 2:
                # 如果数据不足，使用随机数填充
                padding = [random.randint(1, 49) for _ in range(self.sequence_length * 2 - len(valid_data))]
                valid_data = padding + valid_data
                logger.info(f"数据长度不足，添加 {len(padding)} 个随机数据进行填充")

            # 转换为DataFrame以便于特征工程
            df = pd.DataFrame(valid_data, columns=['number'])

            # 基本特征
            features = []
            feature_names = []

            # 原始数值
            features.append(df['number'])
            feature_names.append('number')

            # 统计特征
            for window in [3, 5, 10, 15]:
                if len(df) >= window:
                    # 移动平均
                    col_name = f'rolling_mean_{window}'
                    df[col_name] = df['number'].rolling(window=window, min_periods=1).mean()
                    features.append(df[col_name])
                    feature_names.append(col_name)

                    # 移动标准差
                    col_name = f'rolling_std_{window}'
                    df[col_name] = df['number'].rolling(window=window, min_periods=1).std().fillna(0)
                    features.append(df[col_name])
                    feature_names.append(col_name)

                    # 移动最大值和最小值
                    col_name = f'rolling_max_{window}'
                    df[col_name] = df['number'].rolling(window=window, min_periods=1).max()
                    features.append(df[col_name])
                    feature_names.append(col_name)

                    col_name = f'rolling_min_{window}'
                    df[col_name] = df['number'].rolling(window=window, min_periods=1).min()
                    features.append(df[col_name])
                    feature_names.append(col_name)

            # 差分特征
            col_name = 'diff_1'
            df[col_name] = df['number'].diff().fillna(0)
            features.append(df[col_name])
            feature_names.append(col_name)

            # 周期性特征
            for period in [7, 12, 30, 49]:
                # 正弦和余弦变换
                sin_col = f'sin_{period}'
                cos_col = f'cos_{period}'
                df[sin_col] = np.sin(2 * np.pi * df['number'] / period)
                df[cos_col] = np.cos(2 * np.pi * df['number'] / period)
                features.append(df[sin_col])
                features.append(df[cos_col])
                feature_names.append(sin_col)
                feature_names.append(cos_col)

            # 数字分解特征
            df['tens'] = df['number'].apply(lambda x: x // 10)  # 十位数
            df['units'] = df['number'].apply(lambda x: x % 10)  # 个位数
            features.append(df['tens'])
            features.append(df['units'])
            feature_names.append('tens')
            feature_names.append('units')

            # 质数特征
            def is_prime(n):
                if n <= 1:
                    return 0
                if n <= 3:
                    return 1
                if n % 2 == 0 or n % 3 == 0:
                    return 0
                i = 5
                while i * i <= n:
                    if n % i == 0 or n % (i + 2) == 0:
                        return 0
                    i += 6
                return 1

            df['is_prime'] = df['number'].apply(is_prime)
            features.append(df['is_prime'])
            feature_names.append('is_prime')

            # 数字属性特征
            # 奇偶性
            col_name = 'is_odd'
            df[col_name] = df['number'].apply(lambda x: 1 if x % 2 == 1 else 0)
            features.append(df[col_name])
            feature_names.append(col_name)

            # 大小性
            col_name = 'is_big'
            df[col_name] = df['number'].apply(lambda x: 1 if x > 24 else 0)
            features.append(df[col_name])
            feature_names.append(col_name)

            # 尾数
            col_name = 'tail'
            df[col_name] = df['number'].apply(lambda x: x % 10)
            features.append(df[col_name])
            feature_names.append(col_name)

            # 头数
            col_name = 'head'
            df[col_name] = df['number'].apply(lambda x: x // 10 if x < 50 else 4)
            features.append(df[col_name])
            feature_names.append(col_name)

            # 合并特征
            feature_df = pd.concat(features, axis=1)
            feature_df.columns = feature_names

            # 填充缺失值
            feature_df = feature_df.fillna(method='bfill').fillna(method='ffill').fillna(0)

            # 标准化特征
            feature_array = feature_df.values
            if self.scaler is None:
                self.scaler = StandardScaler()
                feature_array_scaled = self.scaler.fit_transform(feature_array)
            else:
                try:
                    feature_array_scaled = self.scaler.transform(feature_array)
                except Exception as e:
                    logger.error(f"使用现有缩放器转换特征失败: {str(e)}")
                    # 重新创建缩放器
                    self.scaler = StandardScaler()
                    feature_array_scaled = self.scaler.fit_transform(feature_array)

            self.feature_names = feature_names
            return feature_array_scaled, feature_names
        except Exception as e:
            logger.error(f"特征工程失败: {str(e)}")
            # 返回一个基本的特征矩阵
            random_data = [random.randint(1, 49) for _ in range(self.sequence_length * 2)]
            df = pd.DataFrame(random_data, columns=['number'])
            self.feature_names = ['number']
            if self.scaler is None:
                self.scaler = StandardScaler()
            feature_array = df.values
            feature_array_scaled = self.scaler.fit_transform(feature_array)
            return feature_array_scaled, ['number']

    def _prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """准备序列数据"""
        X, y = [], []

        # 创建序列
        for i in range(len(data) - self.sequence_length):
            X.append(data[i:i + self.sequence_length])
            y.append(data[i + self.sequence_length, 0])  # 预测第一个特征（原始数值）

        return np.array(X), np.array(y)

    def _prepare_lstm_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[DataLoader, int]:
        """准备LSTM训练数据"""
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y).unsqueeze(1)

        # 创建数据集和数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

        # 返回数据加载器和特征维度
        return dataloader, X.shape[2]

    def train_models(self, historical_data: List[int], params: Dict[str, Any]) -> Dict[str, Any]:
        """训练模型"""
        logger.info(f"开始训练高级预测模型，使用 {len(historical_data)} 条历史数据")

        # 如果历史数据不足，使用数据同步服务获取更多数据
        if len(historical_data) < self.sequence_length * 5:  # 增加数据量要求
            logger.info("历史数据不足，尝试从数据同步服务获取更多数据")
            additional_data = self.data_sync_service.get_training_data(limit=500)  # 增加获取数据量
            if additional_data:
                logger.info(f"从数据同步服务获取到 {len(additional_data)} 条额外数据")
                historical_data = additional_data + historical_data

        # 特征工程
        data_scaled, feature_names = self._engineer_features(historical_data)

        # 准备序列数据
        X, y = self._prepare_sequences(data_scaled)

        if len(X) == 0 or len(y) == 0:
            logger.error("无法创建训练序列，训练失败")
            return {
                "status": "error",
                "message": "无法创建训练序列，训练失败"
            }

        # 训练随机森林模型
        logger.info("训练随机森林模型...")
        rf_params = {
            "n_estimators": params.get("rf_n_estimators", 200),
            "max_depth": params.get("rf_max_depth", 15),
            "min_samples_split": params.get("rf_min_samples_split", 5),
            "random_state": 42
        }
        rf_model = RandomForestRegressor(**rf_params)

        # 将序列数据展平以适应传统机器学习模型
        X_flat = X.reshape(X.shape[0], -1)

        # 训练模型
        rf_model.fit(X_flat, y)
        rf_score = rf_model.score(X_flat, y)
        self.models["rf"] = rf_model

        # 训练XGBoost模型
        logger.info("训练XGBoost模型...")
        xgb_params = {
            "n_estimators": params.get("xgb_n_estimators", 200),
            "max_depth": params.get("xgb_max_depth", 8),
            "learning_rate": params.get("xgb_learning_rate", 0.05),
            "subsample": params.get("xgb_subsample", 0.8),
            "colsample_bytree": params.get("xgb_colsample_bytree", 0.8),
            "random_state": 42
        }
        xgb_model = xgb.XGBRegressor(**xgb_params)
        xgb_model.fit(X_flat, y)
        xgb_score = xgb_model.score(X_flat, y)
        self.models["xgboost"] = xgb_model

        # 训练GBDT模型
        logger.info("训练GBDT模型...")
        gbdt_params = {
            "n_estimators": params.get("gbdt_n_estimators", 200),
            "max_depth": params.get("gbdt_max_depth", 6),
            "learning_rate": params.get("gbdt_learning_rate", 0.05),
            "subsample": params.get("gbdt_subsample", 0.8),
            "random_state": 42
        }
        gbdt_model = GradientBoostingRegressor(**gbdt_params)
        gbdt_model.fit(X_flat, y)
        gbdt_score = gbdt_model.score(X_flat, y)
        self.models["gbdt"] = gbdt_model

        # 训练LSTM模型
        logger.info("训练LSTM模型...")
        try:
            # 准备LSTM数据
            dataloader, input_size = self._prepare_lstm_data(X, y)

            # 创建模型
            hidden_size = params.get("lstm_hidden_size", 128)
            num_layers = params.get("lstm_num_layers", 2)
            dropout = params.get("lstm_dropout", 0.2)

            lstm_model = LSTMModel(input_size, hidden_size, num_layers, dropout).to(self.device)

            # 设置优化器和损失函数
            optimizer = optim.Adam(
                lstm_model.parameters(),
                lr=params.get("lstm_learning_rate", 0.001),
                weight_decay=params.get("lstm_weight_decay", 1e-5)
            )
            criterion = nn.MSELoss()

            # 训练模型
            epochs = params.get("lstm_epochs", 200)  # 增加默认训练轮数
            best_loss = float('inf')
            patience = params.get("lstm_patience", 20)  # 增加早停耐心参数
            patience_counter = 0

            logger.info(f"LSTM训练参数: epochs={epochs}, patience={patience}, lr={params.get('lstm_learning_rate', 0.001)}")

            for epoch in range(epochs):
                lstm_model.train()
                epoch_loss = 0

                for batch_X, batch_y in dataloader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)

                    # 前向传播
                    optimizer.zero_grad()
                    outputs = lstm_model(batch_X)
                    loss = criterion(outputs, batch_y)

                    # 反向传播
                    loss.backward()
                    optimizer.step()

                    epoch_loss += loss.item()

                avg_loss = epoch_loss / len(dataloader)
                logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")

                # 早停
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.models["lstm"] = lstm_model
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break

            # 计算LSTM模型分数
            lstm_model.eval()
            with torch.no_grad():
                X_tensor = torch.FloatTensor(X).to(self.device)
                y_tensor = torch.FloatTensor(y).to(self.device)
                outputs = lstm_model(X_tensor)
                lstm_score = 1 - ((outputs.squeeze() - y_tensor) ** 2).mean().item() / y.var()

            logger.info(f"LSTM模型训练完成，分数: {lstm_score:.4f}")

        except Exception as e:
            logger.error(f"LSTM模型训练失败: {str(e)}")
            lstm_score = 0.0

        # 计算模型权重
        # 对分数进行平滑处理，避免极端值
        rf_score_adj = max(0.1, min(0.9, rf_score))
        xgb_score_adj = max(0.1, min(0.9, xgb_score))
        gbdt_score_adj = max(0.1, min(0.9, gbdt_score))
        lstm_score_adj = max(0.1, min(0.9, lstm_score))

        # 应用软最大化函数增强差异
        import math
        rf_weight = math.exp(rf_score_adj * 2)
        xgb_weight = math.exp(xgb_score_adj * 2)
        gbdt_weight = math.exp(gbdt_score_adj * 2)
        lstm_weight = math.exp(lstm_score_adj * 2)

        total_weight = rf_weight + xgb_weight + gbdt_weight + lstm_weight

        if total_weight > 0:
            self.model_weights = {
                "rf": rf_weight / total_weight,
                "xgboost": xgb_weight / total_weight,
                "gbdt": gbdt_weight / total_weight,
                "lstm": lstm_weight / total_weight
            }
            logger.info(f"模型权重计算结果: RF={self.model_weights['rf']:.2f}, XGB={self.model_weights['xgboost']:.2f}, GBDT={self.model_weights['gbdt']:.2f}, LSTM={self.model_weights['lstm']:.2f}")
        else:
            # 如果所有模型分数都为0，使用均等权重
            self.model_weights = {
                "rf": 0.25,
                "xgboost": 0.25,
                "gbdt": 0.25,
                "lstm": 0.25
            }
            logger.warning("所有模型分数都为0，使用均等权重")

        # 保存模型
        self._save_models()

        # 计算综合得分
        combined_score = (rf_score * self.model_weights["rf"] +
                         xgb_score * self.model_weights["xgboost"] +
                         gbdt_score * self.model_weights["gbdt"] +
                         lstm_score * self.model_weights["lstm"])

        # 保存训练历史
        training_history = ModelTrainingHistory(
            training_time=datetime.now(),
            model_name="advanced_prediction_model",
            parameters=json.dumps(params),
            metrics=json.dumps({
                "rf_score": rf_score,
                "xgb_score": xgb_score,
                "gbdt_score": gbdt_score,
                "lstm_score": lstm_score,
                "combined_score": combined_score,
                "model_weights": self.model_weights
            }),
            status="success"
        )

        try:
            with SessionLocal() as db:
                db.add(training_history)
                db.commit()
                logger.info("训练历史已保存到数据库")
        except Exception as e:
            logger.error(f"保存训练历史失败: {str(e)}")

        return {
            "status": "success",
            "data_count": len(historical_data),
            "accuracy": combined_score,
            "metrics": {
                "rf_score": rf_score,
                "xgb_score": xgb_score,
                "gbdt_score": gbdt_score,
                "lstm_score": lstm_score,
                "combined_score": combined_score,
                "model_weights": self.model_weights
            }
        }

    def predict(self, historical_data: List[int], range_size: int = 30) -> Dict[str, Any]:
        """生成预测"""
        logger.info(f"开始高级预测，使用 {len(historical_data)} 条历史数据")

        # 如果历史数据不足，使用数据同步服务获取更多数据
        if len(historical_data) < self.sequence_length * 3:  # 增加数据量要求
            logger.info("历史数据不足，尝试从数据同步服务获取更多数据")
            additional_data = self.data_sync_service.get_training_data(limit=300)  # 增加获取数据量
            if additional_data:
                logger.info(f"从数据同步服务获取到 {len(additional_data)} 条额外数据")
                historical_data = additional_data + historical_data

        # 如果数据仍然不足，使用随机数据填充
        if len(historical_data) < self.sequence_length:
            logger.warning(f"历史数据仍然不足 ({len(historical_data)} < {self.sequence_length})，使用随机数据填充")
            padding = [random.randint(1, 49) for _ in range(self.sequence_length - len(historical_data))]
            historical_data = padding + historical_data

        # 特征工程
        data_scaled, _ = self._engineer_features(historical_data)

        # 准备输入序列
        X_pred = data_scaled[-self.sequence_length:].reshape(1, self.sequence_length, -1)
        X_pred_flat = X_pred.reshape(1, -1)  # 展平版本用于传统机器学习模型

        # 如果模型未训练，先训练模型
        if not any(self.models.values()):
            logger.info("模型未训练，先训练模型")
            self.train_models(historical_data, {})

        # 使用模型预测
        predictions = {}
        confidence_scores = {}

        try:
            # 随机森林预测
            if self.models["rf"]:
                rf_pred_scaled = self.models["rf"].predict(X_pred_flat)[0]
                rf_pred = self.scaler.inverse_transform([[rf_pred_scaled] + [0] * (data_scaled.shape[1] - 1)])[0][0]
                rf_pred = max(1, min(49, int(round(rf_pred))))
                predictions["rf"] = rf_pred
                confidence_scores["rf"] = float(self.model_weights["rf"])

            # XGBoost预测
            if self.models["xgboost"]:
                xgb_pred_scaled = self.models["xgboost"].predict(X_pred_flat)[0]
                xgb_pred = self.scaler.inverse_transform([[xgb_pred_scaled] + [0] * (data_scaled.shape[1] - 1)])[0][0]
                xgb_pred = max(1, min(49, int(round(xgb_pred))))
                predictions["xgboost"] = xgb_pred
                confidence_scores["xgboost"] = float(self.model_weights["xgboost"])

            # GBDT预测
            if self.models["gbdt"]:
                gbdt_pred_scaled = self.models["gbdt"].predict(X_pred_flat)[0]
                gbdt_pred = self.scaler.inverse_transform([[gbdt_pred_scaled] + [0] * (data_scaled.shape[1] - 1)])[0][0]
                gbdt_pred = max(1, min(49, int(round(gbdt_pred))))
                predictions["gbdt"] = gbdt_pred
                confidence_scores["gbdt"] = float(self.model_weights["gbdt"])

            # LSTM预测
            if self.models["lstm"]:
                self.models["lstm"].eval()
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(X_pred).to(self.device)
                    lstm_pred_scaled = self.models["lstm"](X_tensor).cpu().numpy()[0][0]
                    lstm_pred = self.scaler.inverse_transform([[lstm_pred_scaled] + [0] * (data_scaled.shape[1] - 1)])[0][0]
                    lstm_pred = max(1, min(49, int(round(lstm_pred))))
                    predictions["lstm"] = lstm_pred
                    confidence_scores["lstm"] = float(self.model_weights["lstm"])

            # 综合预测（加权平均）
            if predictions:
                weighted_sum = sum(pred * confidence_scores[model] for model, pred in predictions.items())
                total_weight = sum(confidence_scores.values())
                ensemble_pred = int(round(weighted_sum / total_weight)) if total_weight > 0 else 25
                ensemble_pred = max(1, min(49, ensemble_pred))

                # 计算综合置信度 - 使用加权平均而不是简单平均
                weighted_confidence = 0
                for model, score in confidence_scores.items():
                    if model in self.model_weights:
                        weighted_confidence += score * self.model_weights[model]

                # 应用一个置信度提升因子，基于模型一致性
                model_predictions = list(predictions.values())
                if len(model_predictions) >= 2:
                    # 计算模型预测的标准差
                    std_dev = np.std(model_predictions) if len(model_predictions) > 1 else 0
                    max_possible_std = 48 / 2  # 最大可能标准差（1-49范围内）
                    agreement_factor = 1 - (std_dev / max_possible_std)  # 一致性因子，越一致越接近1
                    weighted_confidence = weighted_confidence * (0.7 + 0.3 * agreement_factor)  # 提升置信度

                confidence_scores["combined"] = max(0.1, min(0.99, weighted_confidence))  # 限制在合理范围内
                logger.info(f"综合置信度计算结果: {confidence_scores['combined']:.2f}, 模型一致性: {agreement_factor:.2f if 'agreement_factor' in locals() else 'N/A'}")

                # 添加一些随机性，使预测更加多样化
                ensemble_pred = max(1, min(49, ensemble_pred + random.randint(-2, 2)))

                logger.info(f"模型预测结果: RF={predictions.get('rf', 'N/A')}, XGB={predictions.get('xgboost', 'N/A')}, GBDT={predictions.get('gbdt', 'N/A')}, LSTM={predictions.get('lstm', 'N/A')}, 综合={ensemble_pred}")
            else:
                # 如果所有模型都失败，使用简单统计方法
                ensemble_pred = int(round(sum(historical_data[-5:]) / 5))
                ensemble_pred = max(1, min(49, ensemble_pred))
                confidence_scores = {
                    "rf": 0.25,
                    "xgboost": 0.25,
                    "gbdt": 0.25,
                    "lstm": 0.25,
                    "combined": 0.25
                }
                logger.info(f"使用简单统计方法预测: {ensemble_pred}")
        except Exception as e:
            logger.error(f"模型预测失败: {str(e)}")
            # 如果模型预测失败，使用简单统计方法
            ensemble_pred = int(round(sum(historical_data[-5:]) / 5))
            ensemble_pred = max(1, min(49, ensemble_pred))
            confidence_scores = {
                "rf": 0.25,
                "xgboost": 0.25,
                "gbdt": 0.25,
                "lstm": 0.25,
                "combined": 0.25
            }
            logger.info(f"使用简单统计方法预测: {ensemble_pred}")

        # 生成预测范围
        special_numbers = self._generate_range_predictions(ensemble_pred, range_size)

        # 计算属性预测
        # 获取详细属性
        number_attributes = self.game_rules.get_number_attributes(ensemble_pred)
        # 获取简化属性（与原有预测服务兼容）
        attributes = self.game_rules.get_attributes(ensemble_pred)

        # 生成生肖预测
        zodiac_predictions = [self.game_rules.get_zodiac(num) for num in special_numbers[:7]]

        # 生成策略
        strategy = self._generate_strategy(special_numbers[:5], attributes, confidence_scores)

        # 生成预测解释
        prediction_result = {
            "special_numbers_5": special_numbers[:5],
            "special_numbers_10": special_numbers[:10],
            "special_numbers_15": special_numbers[:15],
            "special_numbers_20": special_numbers[:20],
            "special_numbers_30": special_numbers[:30],
            "attribute_predictions": attributes,
            "zodiac_3": zodiac_predictions[:3],
            "zodiac_5": zodiac_predictions[:5],
            "zodiac_7": zodiac_predictions[:7],
            "strategy": strategy,
            "confidence_scores": confidence_scores,
            "model_predictions": predictions
        }
        explanation = self.prediction_explainer.explain_prediction(prediction_result, historical_data)

        return {
            "special_numbers_5": special_numbers[:5],
            "special_numbers_10": special_numbers[:10],
            "special_numbers_15": special_numbers[:15],
            "special_numbers_20": special_numbers[:20],
            "special_numbers_30": special_numbers[:30],
            "attribute_predictions": attributes,
            "zodiac_3": zodiac_predictions[:3],
            "zodiac_5": zodiac_predictions[:5],
            "zodiac_7": zodiac_predictions[:7],
            "strategy": strategy,
            "confidence_scores": confidence_scores,
            "explanation": explanation,
            "model_predictions": predictions
        }

    def _generate_range_predictions(self, center: int, size: int) -> List[int]:
        """生成预测范围"""
        # 确保 size 至少为 30，以生成所有需要的预测结果
        size = max(size, 30)

        all_numbers = list(range(1, 50))
        weights = [1/(abs(x - center) + 1) for x in all_numbers]

        # 确保生成足够多的不重复数字
        predictions = []
        while len(predictions) < size:
            # 随机选择一个数字，权重偏向中心值附近
            num = random.choices(all_numbers, weights=weights, k=1)[0]
            if num not in predictions:
                predictions.append(num)

        # 按照与预测值的距离排序
        predictions = sorted(predictions, key=lambda x: abs(x - center))

        # 确保中心值在预测结果中
        if center not in predictions:
            predictions[0] = center
            # 重新排序
            predictions = sorted(predictions, key=lambda x: abs(x - center))

        # 记录生成的预测结果
        logger.info(f"生成预测范围: 中心值={center}, 范围大小={size}, 结果数量={len(predictions)}")
        logger.info(f"5码: {predictions[:5]}")
        logger.info(f"10码: {predictions[:10]}")
        logger.info(f"15码: {predictions[:15]}")
        logger.info(f"20码: {predictions[:20]}")
        logger.info(f"30码: {predictions[:30]}")

        return predictions

    def _generate_strategy(self, numbers: List[int], attributes: Dict[str, str],
                         confidence: Dict[str, float]) -> str:
        """生成策略建议"""
        # 确保有效的置信度值
        valid_confidences = [v for v in confidence.values() if isinstance(v, (int, float))]
        avg_confidence = sum(valid_confidences) / len(valid_confidences) if valid_confidences else 0.5

        # 安全获取属性值，确保不会出现None
        odd_even = attributes.get('odd_even', '') or ''
        big_small = attributes.get('big_small', '') or ''
        color = attributes.get('color', '') or ''
        wuxing = attributes.get('wuxing', '') or ''
        tail_big_small = attributes.get('tail_big_small', '') or ''
        sum_odd_even = attributes.get('sum_odd_even', '') or ''

        # 确保有效的号码列表
        valid_numbers = [n for n in numbers if isinstance(n, (int, float)) and 1 <= n <= 49]
        if not valid_numbers:
            valid_numbers = [random.randint(1, 49) for _ in range(5)]

        strategy = f"根据高级预测模型分析，建议关注以下几个方面\n"
        strategy += f"1. 重点关注号码：{', '.join(map(str, valid_numbers))}\n"

        # 只有当属性值不为空时才添加相应的建议
        if odd_even or big_small:
            strategy += f"2. {odd_even}数、{big_small}数走势强劲\n"
        else:
            strategy += "2. 奇偶、大小走势不明显\n"

        if color or wuxing:
            strategy += f"3. {color}、{wuxing}属性值得关注\n"
        else:
            strategy += "3. 颜色、五行属性不明显\n"

        # 添加尾数和和值属性分析
        if tail_big_small or sum_odd_even:
            strategy += f"4. 尾数{tail_big_small}、和值{sum_odd_even}特征明显\n"
        else:
            strategy += "4. 尾数和和值特征不明显\n"

        # 根据置信度给出建议
        if avg_confidence > 0.8:
            strategy += "5. 当前预测置信度较高，可以适当参考\n"
        elif avg_confidence > 0.6:
            strategy += "5. 当前预测置信度一般，建议谨慎参考\n"
        else:
            strategy += "5. 当前预测置信度较低，建议仅供参考\n"

        # 记录生成的策略
        logger.info(f"生成策略建议: {strategy}")

        return strategy

    def _prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """准备序列数据"""
        X, y = [], []

        # 创建序列
        for i in range(len(data) - self.sequence_length):
            X.append(data[i:i + self.sequence_length])
            y.append(data[i + self.sequence_length, 0])  # 预测第一个特征（原始数值）

        return np.array(X), np.array(y)

    def _prepare_lstm_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[DataLoader, int]:
        """准备LSTM训练数据"""
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y).unsqueeze(1)

        # 创建数据集和数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

        # 返回数据加载器和特征维度
        return dataloader, X.shape[2]
