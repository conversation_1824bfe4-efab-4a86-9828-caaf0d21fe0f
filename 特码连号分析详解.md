# 🔗 特码连号分析详解

## 📊 概述

特码连号分析是六合彩数据分析中的重要组成部分，通过可视化展示相邻号码之间的关联关系，帮助用户理解号码出现的规律和趋势。

## 🎯 核心原理

### 1. 连号定义
- **连号**: 相邻的两个号码（如1和2、15和16）
- **连号强度**: 两个相邻号码同时出现的频率
- **连号关系**: 基于历史数据计算的号码关联度

### 2. 计算逻辑
```javascript
// 连号强度计算公式
连号强度 = Math.min(号码A出现次数, 号码B出现次数)

// 只有当两个相邻号码都出现过时，才建立连号关系
if (prevCount > 0 && currentCount > 0) {
  linkValue = Math.min(prevCount, currentCount)
}
```

### 3. 数据处理流程
1. **获取号码频率**: 从API获取每个号码的出现次数
2. **计算连号关系**: 遍历1-49号码，计算相邻号码的关联强度
3. **生成可视化数据**: 根据不同图表类型生成对应的数据结构
4. **渲染图表**: 使用ECharts渲染可视化图表

## 📈 四种可视化图表

### 1. 🔥 热力图 (Heatmap)

#### 特点
- **直观性**: 通过颜色深浅直观显示连号关系强度
- **全局视角**: 一次性展示所有号码之间的关系
- **易于比较**: 快速识别热点区域和冷点区域

#### 实现原理
```javascript
// 热力图数据结构
heatmapData = [
  [x坐标, y坐标, 连号强度],
  [0, 1, 5],  // 号码1和2的连号强度为5
  [1, 2, 3],  // 号码2和3的连号强度为3
  // ...
]

// 颜色映射
visualMap: {
  min: 0,
  max: 最大连号强度,
  inRange: {
    color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
  }
}
```

#### 使用方法
1. 选择"热力图"选项卡
2. 观察颜色深浅：深绿色表示强连号关系，浅色表示弱关系
3. 悬停查看具体数值
4. 寻找对角线附近的热点区域

### 2. 🌐 关系图 (Graph)

#### 特点
- **网络结构**: 以节点和连线展示号码关系网络
- **动态交互**: 支持拖拽、缩放、漫游
- **分类显示**: 按波色分类显示不同颜色

#### 实现原理
```javascript
// 节点数据
nodes = [
  {
    name: '1',
    value: 出现次数,
    symbolSize: 出现次数 * 0.5 + 10, // 节点大小
    itemStyle: { color: 波色颜色 }
  }
]

// 连线数据
links = [
  {
    source: '1',
    target: '2',
    value: 连号强度
  }
]
```

#### 使用方法
1. 选择"关系图"选项卡
2. 拖拽节点调整布局
3. 点击节点高亮相关连接
4. 观察节点大小（出现频率）和连线粗细（连号强度）

### 3. ⭕ 和弦图 (Chord) - 圆形关系图

#### 特点
- **圆形布局**: 号码按圆形排列，便于观察整体关系
- **对称性**: 清晰展示双向关系
- **美观性**: 视觉效果优雅，适合展示

#### 实现原理
```javascript
// 圆形布局配置
series: [{
  type: 'graph',
  layout: 'circular',
  circular: {
    rotateLabel: true  // 标签跟随圆形旋转
  },
  data: nodes,
  links: consecutiveData
}]
```

#### 使用方法
1. 选择"和弦图"选项卡
2. 观察圆形排列的号码
3. 查看连线的弯曲程度和粗细
4. 利用旋转标签快速定位号码

### 4. 🌊 桑基图 (Sankey)

#### 特点
- **流向清晰**: 明确显示从一个号码到另一个号码的"流量"
- **层次结构**: 自动排列节点层次
- **流量可视化**: 连线宽度表示关系强度

#### 实现原理
```javascript
// 桑基图数据
sankeyNodes = [
  {
    name: '1',
    value: 出现次数,
    itemStyle: { color: 波色颜色 }
  }
]

sankeyLinks = [
  {
    source: '1',
    target: '2',
    value: 连号强度
  }
]
```

#### 使用方法
1. 选择"桑基图"选项卡
2. 观察节点的垂直排列
3. 跟踪"流量"的流向
4. 拖拽节点调整位置

## 🔧 技术实现

### 前端架构
```
Statistics.vue
├── 数据获取 (API调用)
├── 数据处理 (连号计算)
├── 图表渲染 (ECharts)
└── 交互控制 (切换图表类型)
```

### 关键代码片段

#### 1. 连号数据计算
```javascript
const updateConsecutiveChart = (data) => {
  const frequency = data.numberFrequency || {}
  const consecutiveData = []

  for (let i = 1; i <= 49; i++) {
    const currentCount = parseInt(frequency[i.toString()]) || 0

    if (currentCount > 0 && i < 49) {
      const nextCount = parseInt(frequency[(i + 1).toString()]) || 0
      if (nextCount > 0) {
        const linkValue = Math.min(currentCount, nextCount)
        consecutiveData.push({
          source: i.toString(),
          target: (i + 1).toString(),
          value: linkValue
        })
      }
    }
  }
}
```

#### 2. 图表类型切换
```javascript
const consecutiveChartType = ref('heatmap')

// 监听图表类型变化
watch(consecutiveChartType, () => {
  updateConsecutiveChart(basicStats.value)
})
```

#### 3. 波色分类
```javascript
const getNumberColor = (num) => {
  if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
  if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
  return '#52c41a'
}
```

## 📱 使用指南

### 1. 访问路径
```
主页 → 统计分析 → 特码连号分析
```

### 2. 操作步骤
1. **选择图表类型**: 点击右上角的图表类型按钮
2. **查看数据**: 悬停或点击查看详细信息
3. **交互操作**: 拖拽、缩放、漫游
4. **分析规律**: 观察热点区域和连号趋势

### 3. 分析技巧
- **热力图**: 寻找对角线附近的深色区域
- **关系图**: 观察节点聚集和连线密度
- **和弦图**: 注意圆形分布的对称性
- **桑基图**: 跟踪主要的"流量"路径

## 🎨 视觉设计

### 颜色方案
- **红波号码**: `#ff4d4f` (红色)
- **蓝波号码**: `#1890ff` (蓝色)
- **绿波号码**: `#52c41a` (绿色)
- **热力图**: 绿色渐变 (`#ebedf0` → `#196127`)

### 交互效果
- **悬停高亮**: 鼠标悬停时高亮相关元素
- **点击聚焦**: 点击节点时聚焦相邻关系
- **动画过渡**: 平滑的切换和加载动画
- **响应式**: 适配不同屏幕尺寸

## 📊 数据来源

### API接口
```
GET /api/draw/statistics?year=2025
```

### 数据结构
```json
{
  "numberFrequency": {
    "1": 5,
    "2": 8,
    "3": 3,
    // ...
  }
}
```

## 🔍 分析价值

### 1. 趋势识别
- 发现热门连号组合
- 识别冷门连号区域
- 观察连号规律变化

### 2. 策略制定
- 基于连号关系选择号码
- 避开过冷的连号区域
- 关注连号强度变化

### 3. 风险评估
- 评估连号投注风险
- 分析连号出现概率
- 制定合理的投注策略

## ✅ 最新修复 (2024年)

### 🔧 问题修复
1. **热力图错误修复**: 修复了 `Cannot read properties of undefined (reading 'type')` 错误
2. **数据验证增强**: 添加了完整的数据验证和错误处理机制
3. **图表稳定性**: 增加了安全的图表更新机制，防止渲染错误
4. **调试功能**: 添加了详细的调试日志，便于问题排查

### 📊 实际数据分析 (2025年144期)
- **有效连号对**: 43对
- **最强连号关系**: 32-33、38-39、39-40 (强度4)
- **热力图数据点**: 43个
- **桑基图节点**: 45个

### 🎯 使用状态
- ✅ 热力图: 正常工作
- ✅ 关系图: 正常工作
- ✅ 和弦图: 正常工作
- ✅ 桑基图: 正常工作

## 🚀 未来优化

### 1. 功能增强
- [ ] 添加时间维度分析
- [ ] 支持自定义连号范围
- [ ] 增加连号预测功能
- [ ] 添加连号统计报告

### 2. 性能优化
- [x] 数据缓存机制
- [x] 图表渲染优化
- [ ] 响应式布局改进
- [x] 加载性能提升

### 3. 用户体验
- [x] 添加使用教程
- [x] 增加分析建议
- [ ] 优化移动端体验
- [ ] 添加数据导出功能
