from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.v1.api import api_router
from app.db.session import engine
from app.db.base import Base
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="""
    这是一个基于机器学习的数字竞猜游戏系统，提供以下功能：
    
    ## 开奖记录
    * 获取历史开奖记录
    * 获取最新开奖结果
    
    ## 统计分析
    * 号码频率统计
    * 生肖统计
    * 波色统计
    * 单双统计
    * 大小统计
    * 热门号码分析
    * 冷门号码分析
    
    ## 预测功能
    * 模型训练
    * 下一期预测
    * 预测准确率统计
    
    ## 数据管理
    * 数据导入导出
    * 数据备份恢复
    
    所有API接口都需要进行身份验证，请在请求头中添加 `Authorization: Bearer {token}`。
    """,
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    terms_of_service="http://example.com/terms/",
    contact={
        "name": "技术支持",
        "url": "http://example.com/contact/",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    }
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    return {"message": "欢迎使用Marksix API"}

if __name__ == "__main__":
    """
    启动FastAPI应用
    使用端口8000，与前端配置一致
    """
    try:
        # 初始化测试数据
        from init_test_data import init_test_data
        init_test_data()
        logger.info("Test data initialized successfully")

        # 初始化预测数据
        from init_prediction_data import init_prediction_data
        init_prediction_data()
        logger.info("Prediction data initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing test data: {str(e)}")
        logger.info("Continuing with application startup...")

    # 启动应用
    logger.info("Starting FastAPI application on port 8000")
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
