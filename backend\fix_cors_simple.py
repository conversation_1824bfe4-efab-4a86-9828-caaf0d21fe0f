"""
简单的CORS修复脚本
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_cors():
    """修复CORS问题"""
    try:
        logger.info("开始修复CORS问题...")
        
        # 修改main.py文件
        main_py_path = os.path.join(os.path.dirname(__file__), 'app', 'main.py')
        
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找CORS中间件配置
        cors_middleware_start = content.find("app.add_middleware(\n    CORSMiddleware,")
        if cors_middleware_start == -1:
            logger.error("无法找到CORS中间件配置")
            return False
        
        # 查找CORS中间件配置结束位置
        cors_middleware_end = content.find(")", cors_middleware_start)
        if cors_middleware_end == -1:
            logger.error("无法找到CORS中间件配置结束位置")
            return False
        
        # 替换CORS中间件配置
        new_cors_config = """app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)"""
        
        new_content = content[:cors_middleware_start] + new_cors_config + content[cors_middleware_end+1:]
        
        # 写入修改后的内容
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info("CORS中间件配置已更新")
        
        # 添加自定义CORS中间件
        custom_cors_middleware = """
# 添加自定义CORS中间件
@app.middleware("http")
async def add_cors_headers(request, call_next):
    response = await call_next(request)
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "*"
    response.headers["Access-Control-Allow-Headers"] = "*"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    return response
"""
        
        # 查找合适的位置插入自定义CORS中间件
        insert_pos = new_content.find("# 添加缓存中间件")
        if insert_pos == -1:
            logger.warning("无法找到合适的位置插入自定义CORS中间件，尝试其他位置")
            insert_pos = new_content.find("app.add_middleware(CORSMiddleware")
            if insert_pos == -1:
                logger.error("无法找到合适的位置插入自定义CORS中间件")
                return False
        
        # 插入自定义CORS中间件
        final_content = new_content[:insert_pos] + custom_cors_middleware + new_content[insert_pos:]
        
        # 写入修改后的内容
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        logger.info("自定义CORS中间件已添加")
        logger.info("CORS问题修复完成")
        return True
    except Exception as e:
        logger.error(f"修复CORS问题失败: {str(e)}")
        return False

if __name__ == "__main__":
    fix_cors()
    logger.info("请重启服务以应用CORS修复")
