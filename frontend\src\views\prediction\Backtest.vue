<template>
  <div class="backtest-page">
    <el-card class="backtest-card">
      <template #header>
        <div class="card-header">
          <h2>回测分析</h2>
          <div class="header-actions">
            <el-button type="primary" @click="startBacktest" :loading="isRunning">
              开始回测
            </el-button>
          </div>
        </div>
      </template>

      <!-- 回测配置 -->
      <div class="backtest-config">
        <el-form :model="form" label-width="120px" class="config-form">
          <el-form-item label="回测时间范围">
            <el-date-picker v-model="form.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>

          <el-form-item label="回测模型">
            <el-checkbox-group v-model="form.models">
              <el-checkbox label="lstm">LSTM模型</el-checkbox>
              <el-checkbox label="rf">随机森林模型</el-checkbox>
              <el-checkbox label="xgboost">XGBoost模型</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="预测范围">
            <el-radio-group v-model="form.predictionRange">
              <el-radio value="5">5码</el-radio>
              <el-radio value="10">10码</el-radio>
              <el-radio value="15">15码</el-radio>
              <el-radio value="20">20码</el-radio>
              <el-radio value="30">30码</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 回测结果 -->
      <div v-if="backtestResults" class="backtest-results">
        <!-- 总体表现 -->
        <div class="overall-performance">
          <h3>总体表现</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card shadow="hover" class="metric-card">
                <template #header>
                  <div class="metric-header">总预测次数</div>
                </template>
                <div class="metric-value">{{ backtestResults.totalPredictions }}</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="metric-card">
                <template #header>
                  <div class="metric-header">命中次数</div>
                </template>
                <div class="metric-value">{{ backtestResults.hits }}</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="metric-card">
                <template #header>
                  <div class="metric-header">平均准确率</div>
                </template>
                <div class="metric-value">{{ (backtestResults.averageAccuracy * 100).toFixed(2) }}%</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="metric-card">
                <template #header>
                  <div class="metric-header">收益率</div>
                </template>
                <div class="metric-value" :class="backtestResults.roi >= 0 ? 'positive' : 'negative'">
                  {{ backtestResults.roi >= 0 ? '+' : '' }}{{ (backtestResults.roi * 100).toFixed(2) }}%
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 模型表现对比 -->
        <div class="model-performance">
          <h3>模型表现对比</h3>
          <el-table :data="backtestResults.modelPerformance" style="width: 100%">
            <el-table-column prop="model" label="模型" width="150" />
            <el-table-column prop="predictions" label="预测次数" width="120" />
            <el-table-column prop="hits" label="命中次数" width="120" />
            <el-table-column label="命中率" width="120">
              <template #default="scope">
                {{ ((scope.row.hits / scope.row.predictions) * 100).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column prop="mae" label="平均绝对误差" width="150">
              <template #default="scope">
                {{ scope.row.mae.toFixed(4) }}
              </template>
            </el-table-column>
            <el-table-column prop="rmse" label="均方根误差" width="150">
              <template #default="scope">
                {{ scope.row.rmse.toFixed(4) }}
              </template>
            </el-table-column>
            <el-table-column label="收益率">
              <template #default="scope">
                <span :class="scope.row.roi >= 0 ? 'positive' : 'negative'">
                  {{ scope.row.roi >= 0 ? '+' : '' }}{{ (scope.row.roi * 100).toFixed(2) }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 预测准确率趋势 -->
        <div class="accuracy-trend">
          <h3>预测准确率趋势</h3>
          <div class="chart-container">
            <!-- 这里可以添加准确率趋势图表 -->
          </div>
        </div>

        <!-- 收益曲线 -->
        <div class="profit-curve">
          <h3>收益曲线</h3>
          <div class="chart-container">
            <!-- 这里可以添加收益曲线图表 -->
          </div>
        </div>

        <!-- 详细回测记录 -->
        <div class="backtest-records">
          <h3>详细回测记录</h3>
          <el-table :data="backtestResults.records" style="width: 100%" height="400">
            <el-table-column prop="expect" label="期号" width="120" />
            <el-table-column prop="prediction_time" label="预测时间" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.prediction_time) }}
              </template>
            </el-table-column>
            <el-table-column label="预测号码" width="250">
              <template #default="scope">
                <div class="number-list">
                  <span v-for="num in scope.row.predicted_numbers" :key="num" class="number">
                    {{ String(num).padStart(2, '0') }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="actual_number" label="实际开奖" width="100" />
            <el-table-column label="是否命中" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.hit ? 'success' : 'danger'">
                  {{ scope.row.hit ? '命中' : '未中' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="收益" width="120">
              <template #default="scope">
                <span :class="scope.row.profit >= 0 ? 'positive' : 'negative'">
                  {{ scope.row.profit >= 0 ? '+' : '' }}{{ scope.row.profit }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="model" label="预测模型" width="120" />
            <el-table-column label="置信度">
              <template #default="scope">
                {{ (scope.row.confidence * 100).toFixed(2) }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { runBacktest } from '@/api/prediction'
import dayjs from 'dayjs'

// 状态变量
const isRunning = ref(false)
const form = ref({
  dateRange: null,
  models: ['lstm', 'rf', 'xgboost'],
  predictionRange: '5'
})
const backtestResults = ref(null)

// 开始回测
const startBacktest = async () => {
  if (!form.value.dateRange) {
    ElMessage.warning('请选择回测时间范围')
    return
  }
  if (form.value.models.length === 0) {
    ElMessage.warning('请至少选择一个回测模型')
    return
  }

  try {
    isRunning.value = true
    const response = await runBacktest({
      start_date: form.value.dateRange[0],
      end_date: form.value.dateRange[1],
      models: form.value.models,
      prediction_range: Number(form.value.predictionRange)
    })

    backtestResults.value = response.data
    ElMessage.success('回测完成')
  } catch (error) {
    ElMessage.error('回测失败: ' + error.message)
  } finally {
    isRunning.value = false
  }
}

// 工具函数
const formatDateTime = (datetime) => {
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style lang="scss" scoped>
.backtest-page {
  padding: 20px;
}

.backtest-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.backtest-config {
  margin-bottom: 30px;

  .config-form {
    max-width: 800px;
  }
}

.backtest-results {
  >div {
    margin-top: 30px;

    h3 {
      margin-bottom: 20px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }
}

.metric-card {
  .metric-header {
    text-align: center;
    color: var(--el-text-color-regular);
  }

  .metric-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--el-color-primary);
    text-align: center;
    margin-top: 10px;

    &.positive {
      color: var(--el-color-success);
    }

    &.negative {
      color: var(--el-color-danger);
    }
  }
}

.chart-container {
  height: 300px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  margin-bottom: 20px;
}

.number-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .number {
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background-color: var(--el-color-primary-light-9);
    border-radius: 50%;
    font-size: 13px;
  }
}

.positive {
  color: var(--el-color-success);
}

.negative {
  color: var(--el-color-danger);
}
</style>