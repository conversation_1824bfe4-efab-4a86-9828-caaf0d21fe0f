#!/usr/bin/env python3
"""
将数据从lottery.db迁移到marksix.db
"""
import sqlite3
import os
import shutil
from datetime import datetime

def backup_database(db_path):
    """备份数据库"""
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(db_path, backup_path)
        print(f"已备份数据库到: {backup_path}")
        return backup_path
    return None

def migrate_data():
    """迁移数据"""
    source_db = 'backend/data/lottery.db'
    target_db = 'backend/marksix.db'
    
    if not os.path.exists(source_db):
        print(f"源数据库不存在: {source_db}")
        return False
    
    # 备份目标数据库
    backup_database(target_db)
    
    # 连接源数据库和目标数据库
    source_conn = sqlite3.connect(source_db)
    target_conn = sqlite3.connect(target_db)
    
    try:
        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()
        
        # 检查源数据库中的draws表结构
        source_cursor.execute("PRAGMA table_info(draws)")
        source_columns = source_cursor.fetchall()
        print("源数据库draws表结构:")
        for col in source_columns:
            print(f"  {col[1]} {col[2]}")
        
        # 检查目标数据库中的draws表结构
        target_cursor.execute("PRAGMA table_info(draws)")
        target_columns = target_cursor.fetchall()
        print("\n目标数据库draws表结构:")
        for col in target_columns:
            print(f"  {col[1]} {col[2]}")
        
        # 获取源数据库中的所有draws数据
        source_cursor.execute("SELECT * FROM draws ORDER BY draw_time")
        all_draws = source_cursor.fetchall()
        
        print(f"\n源数据库中共有 {len(all_draws)} 条记录")
        
        # 清空目标数据库的draws表
        target_cursor.execute("DELETE FROM draws")
        print("已清空目标数据库draws表")
        
        # 获取列名映射
        source_col_names = [col[1] for col in source_columns]
        target_col_names = [col[1] for col in target_columns]
        
        # 找到共同的列
        common_columns = [col for col in source_col_names if col in target_col_names]
        print(f"共同列: {common_columns}")
        
        # 构建插入SQL
        placeholders = ', '.join(['?' for _ in common_columns])
        columns_str = ', '.join(common_columns)
        insert_sql = f"INSERT INTO draws ({columns_str}) VALUES ({placeholders})"
        
        # 迁移数据
        migrated_count = 0
        for row in all_draws:
            try:
                # 创建行数据字典
                row_dict = dict(zip(source_col_names, row))
                
                # 提取共同列的值
                values = [row_dict[col] for col in common_columns]
                
                # 插入到目标数据库
                target_cursor.execute(insert_sql, values)
                migrated_count += 1
                
                if migrated_count % 100 == 0:
                    print(f"已迁移 {migrated_count} 条记录...")
                    
            except Exception as e:
                print(f"迁移记录失败: {e}")
                print(f"记录: {row}")
                continue
        
        # 提交事务
        target_conn.commit()
        print(f"成功迁移 {migrated_count} 条记录")
        
        # 验证迁移结果
        target_cursor.execute("SELECT COUNT(*) FROM draws")
        target_count = target_cursor.fetchone()[0]
        print(f"目标数据库中现有 {target_count} 条记录")
        
        # 检查2025年数据
        target_cursor.execute("SELECT COUNT(*) FROM draws WHERE expect LIKE '2025%'")
        target_2025_count = target_cursor.fetchone()[0]
        print(f"2025年记录数: {target_2025_count}")
        
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        target_conn.rollback()
        return False
        
    finally:
        source_conn.close()
        target_conn.close()

def main():
    """主函数"""
    print("开始数据迁移...")
    
    if migrate_data():
        print("数据迁移完成！")
    else:
        print("数据迁移失败！")

if __name__ == "__main__":
    main()
