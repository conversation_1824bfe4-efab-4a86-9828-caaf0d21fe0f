import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database import get_db
from ..utils.data_manager import get_data_manager
from ..models.draw import Draw

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(
    prefix="/data",
    tags=["data-management"],
    responses={404: {"description": "Not found"}},
)


# 请求和响应模型
class SyncResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class BackupResponse(BaseModel):
    success: bool
    message: str
    backup_path: Optional[str] = None


class IntegrityCheckResponse(BaseModel):
    success: bool
    message: str
    total_records: int
    null_fields: Dict[str, int]
    duplicate_expects: List[str]
    future_dates: int


class FixDataResponse(BaseModel):
    success: bool
    message: str
    fixed_count: Dict[str, int]


class DataStatsResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: Dict[str, Any]


class BackupListResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: List[Dict[str, Any]]


class SyncHistoryResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: List[Dict[str, Any]]


# 后台任务
def sync_historical_data_task(year: int, db: Session):
    """后台任务：同步指定年份的历史数据"""
    try:
        data_manager = get_data_manager(db)
        success_count, fail_count = data_manager.sync_historical_data(year)
        logger.info(
            f"后台任务完成：同步 {year} 年的历史数据，成功 {success_count} 条，失败 {fail_count} 条")
    except Exception as e:
        logger.error(f"后台任务失败：同步 {year} 年的历史数据，错误：{str(e)}")
    finally:
        db.close()


# 路由处理函数
@router.get("/stats", response_model=DataStatsResponse)
async def get_data_stats(db: Session = Depends(get_db)):
    """获取数据统计信息"""
    try:
        data_manager = get_data_manager(db)

        # 获取数据库中的记录总数
        try:
            # 使用原生SQL查询，避免使用is_future_date字段
            from sqlalchemy import text
            result = db.execute(text("SELECT COUNT(*) FROM draws")).scalar()
            total_records = result or 0
        except Exception as e:
            logger.error(f"获取记录总数失败: {str(e)}")
            total_records = 0

        # 获取最新一期记录
        try:
            # 使用原生SQL查询，避免使用is_future_date字段
            from sqlalchemy import text
            result = db.execute(
                text("SELECT expect FROM draws ORDER BY expect DESC LIMIT 1")).fetchone()
            latest_expect = result[0] if result else None
        except Exception as e:
            logger.error(f"获取最新一期记录失败: {str(e)}")
            latest_expect = None

        # 获取数据问题统计
        try:
            # 使用原生SQL查询，避免使用is_future_date字段
            from sqlalchemy import text
            null_fields_count = 0
            null_fields_result = db.execute(
                text("SELECT COUNT(*) FROM draws WHERE numbers IS NULL OR zodiac IS NULL OR color IS NULL OR wuxing IS NULL")).scalar()
            null_fields_count = null_fields_result or 0

            duplicate_expects_result = db.execute(
                text("SELECT COUNT(*) FROM (SELECT expect, COUNT(*) as count FROM draws GROUP BY expect HAVING count > 1)")).scalar()
            duplicate_expects_count = duplicate_expects_result or 0

            future_dates_result = db.execute(
                text("SELECT COUNT(*) FROM draws WHERE draw_time > datetime('now')")).scalar()
            future_dates_count = future_dates_result or 0

            total_issues = null_fields_count + duplicate_expects_count + future_dates_count
        except Exception as e:
            logger.error(f"获取数据问题统计失败: {str(e)}")
            null_fields_count = 0
            duplicate_expects_count = 0
            future_dates_count = 0
            total_issues = 0

        # 获取最后同步时间
        try:
            # 使用原生SQL查询，获取最新记录的更新时间
            from sqlalchemy import text
            result = db.execute(
                text("SELECT updated_at FROM draws ORDER BY expect DESC LIMIT 1")).fetchone()
            last_sync_time = result[0] if result else None
        except Exception as e:
            logger.error(f"获取最后同步时间失败: {str(e)}")
            last_sync_time = None

        return {
            "code": 200,
            "message": "success",
            "data": {
                "totalRecords": total_records,
                "latestExpect": latest_expect,
                "lastSyncTime": last_sync_time,
                "nullFields": null_fields_count,
                "duplicateExpects": duplicate_expects_count,
                "futureDates": future_dates_count,
                "totalIssues": total_issues
            }
        }
    except Exception as e:
        logger.error(f"获取数据统计信息失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据统计信息失败：{str(e)}")


@router.get("/backup-list", response_model=BackupListResponse)
async def get_backup_list():
    """获取备份列表"""
    try:
        # 获取备份目录
        import os
        from ..utils.data_manager import DataBackup

        backup_dir = os.path.join(os.path.dirname(
            os.path.dirname(os.path.dirname(__file__))), "data", "backups")
        if not os.path.exists(backup_dir):
            return {
                "code": 200,
                "message": "success",
                "data": []
            }

        # 获取备份文件列表
        backup_files = DataBackup.list_backups(backup_dir)

        # 构建响应数据
        backup_list = []
        for filename in backup_files:
            file_path = os.path.join(backup_dir, filename)
            file_stat = os.stat(file_path)

            # 从文件名中提取时间戳
            import re
            time_match = re.search(r'(\d{8}_\d{6})', filename)
            time_str = time_match.group(1) if time_match else ""

            # 格式化时间
            if time_str:
                try:
                    from datetime import datetime
                    time_obj = datetime.strptime(time_str, "%Y%m%d_%H%M%S")
                    formatted_time = time_obj.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    formatted_time = time_str
            else:
                formatted_time = "未知时间"

            # 格式化文件大小
            size_bytes = file_stat.st_size
            if size_bytes < 1024:
                size_str = f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                size_str = f"{size_bytes / 1024:.2f} KB"
            else:
                size_str = f"{size_bytes / (1024 * 1024):.2f} MB"

            backup_list.append({
                "filename": filename,
                "path": file_path,
                "time": formatted_time,
                "size": size_str
            })

        return {
            "code": 200,
            "message": "success",
            "data": backup_list
        }
    except Exception as e:
        logger.error(f"获取备份列表失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"获取备份列表失败：{str(e)}")


@router.get("/sync-history", response_model=SyncHistoryResponse)
async def get_sync_history(db: Session = Depends(get_db), limit: int = 20):
    """获取同步历史"""
    try:
        # 从数据库中获取同步历史记录
        from ..models.sync_history import SyncHistory

        # 获取最近的同步历史记录
        history_records = db.query(SyncHistory).order_by(
            SyncHistory.created_at.desc()).limit(limit).all()

        # 构建响应数据
        history_list = []
        for record in history_records:
            history_list.append({
                "time": record.created_at.strftime("%Y-%m-%d %H:%M:%S") if record.created_at else "",
                "type": record.sync_type,
                "target": record.target,
                "result": record.result,
                "message": record.message
            })

        return {
            "code": 200,
            "message": "success",
            "data": history_list
        }
    except Exception as e:
        logger.error(f"获取同步历史失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"获取同步历史失败：{str(e)}")


@router.get("/sync/latest", response_model=SyncResponse)
async def sync_latest_data(
    force_update: bool = Query(False, description="是否强制更新"),
    db: Session = Depends(get_db)
):
    """同步最新开奖数据"""
    try:
        data_manager = get_data_manager(db)
        latest_draw = data_manager.sync_latest_draw(force_update=force_update)

        if latest_draw:
            return {
                "success": True,
                "message": f"成功同步最新开奖数据：期号 {latest_draw.expect}",
                "data": {
                    "expect": latest_draw.expect,
                    "draw_time": latest_draw.draw_time.isoformat(),
                    "open_code": latest_draw.open_code,
                    "special_number": latest_draw.special_number
                }
            }
        else:
            return {
                "success": False,
                "message": "未能同步最新开奖数据，可能是API无法访问或返回的数据无效",
                "data": None
            }
    except Exception as e:
        logger.error(f"同步最新开奖数据失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"同步最新开奖数据失败：{str(e)}")


@router.get("/sync/year/{year}", response_model=SyncResponse)
async def sync_year_data(
    year: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """同步指定年份的历史数据（后台任务）"""
    try:
        # 验证年份
        current_year = datetime.now().year
        if year < 2000 or year > current_year:
            return {
                "success": False,
                "message": f"无效的年份：{year}，请提供2000-{current_year}之间的年份",
                "data": None
            }

        # 添加后台任务
        background_tasks.add_task(sync_historical_data_task, year, db)

        return {
            "success": True,
            "message": f"已启动后台任务，同步 {year} 年的历史数据",
            "data": {"year": year}
        }
    except Exception as e:
        logger.error(f"启动同步 {year} 年历史数据的后台任务失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"启动同步任务失败：{str(e)}")


@router.get("/backup", response_model=BackupResponse)
async def backup_database(db: Session = Depends(get_db)):
    """备份数据库"""
    try:
        data_manager = get_data_manager(db)

        # 获取数据库文件路径
        db_dir = os.path.join(os.path.dirname(
            os.path.dirname(os.path.dirname(__file__))), "data")
        db_path = os.path.join(db_dir, "lottery.db")

        # 备份数据库
        backup_path = data_manager.backup_data(db_path)

        if backup_path:
            return {
                "success": True,
                "message": f"成功备份数据库",
                "backup_path": backup_path
            }
        else:
            return {
                "success": False,
                "message": "备份数据库失败",
                "backup_path": None
            }
    except Exception as e:
        logger.error(f"备份数据库失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"备份数据库失败：{str(e)}")


@router.get("/integrity-check", response_model=IntegrityCheckResponse)
async def check_data_integrity(db: Session = Depends(get_db)):
    """检查数据完整性"""
    try:
        data_manager = get_data_manager(db)
        result = data_manager.verify_data_integrity()

        return {
            "success": True,
            "message": "数据完整性检查完成",
            "total_records": result.get("total_records", 0),
            "null_fields": result.get("null_fields", {}),
            "duplicate_expects": result.get("duplicate_expects", []),
            "future_dates": result.get("future_dates", 0)
        }
    except Exception as e:
        logger.error(f"检查数据完整性失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"检查数据完整性失败：{str(e)}")


@router.post("/fix-issues", response_model=FixDataResponse)
async def fix_data_issues(db: Session = Depends(get_db)):
    """修复数据问题"""
    try:
        data_manager = get_data_manager(db)
        result = data_manager.fix_data_issues()

        if "error" in result:
            return {
                "success": False,
                "message": f"修复数据问题失败：{result['error']}",
                "fixed_count": {k: v for k, v in result.items() if k != "error"}
            }

        total_fixed = sum(result.values())
        return {
            "success": True,
            "message": f"成功修复 {total_fixed} 条数据问题",
            "fixed_count": result
        }
    except Exception as e:
        logger.error(f"修复数据问题失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"修复数据问题失败：{str(e)}")


class RestoreRequest(BaseModel):
    backup_path: str


@router.post("/restore", response_model=SyncResponse)
async def restore_database(request: RestoreRequest, db: Session = Depends(get_db)):
    """从备份恢复数据库"""
    try:
        data_manager = get_data_manager(db)

        # 获取数据库文件路径
        db_dir = os.path.join(os.path.dirname(
            os.path.dirname(os.path.dirname(__file__))), "data")
        db_path = os.path.join(db_dir, "lottery.db")

        # 恢复数据库
        success = data_manager.restore_data(request.backup_path, db_path)

        if success:
            return {
                "success": True,
                "message": "成功从备份恢复数据库",
                "data": {"backup_path": request.backup_path}
            }
        else:
            return {
                "success": False,
                "message": "从备份恢复数据库失败",
                "data": None
            }
    except Exception as e:
        logger.error(f"从备份恢复数据库失败：{str(e)}")
        raise HTTPException(status_code=500, detail=f"从备份恢复数据库失败：{str(e)}")
