import * as echarts from 'echarts'

export function useChart(domId) {
  let chart = null

  const initChart = () => {
    if (chart) {
      chart.dispose()
    }
    const dom = document.getElementById(domId)
    if (dom) {
      chart = echarts.init(dom)
    }
    return chart
  }

  const setOption = (option) => {
    if (chart) {
      chart.setOption(option)
    }
  }

  const resize = () => {
    if (chart) {
      chart.resize()
    }
  }

  const dispose = () => {
    if (chart) {
      chart.dispose()
      chart = null
    }
  }

  return {
    chart,
    initChart,
    setOption,
    resize,
    dispose
  }
}

// 通用图表配置
export const defaultOption = {
  tooltip: {
    trigger: 'axis'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  }
}

// 颜色主题
export const colors = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc'
] 