from datetime import datetime
import logging
import requests
from typing import List, Dict, Optional, Tuple
import json
import os
import time
import pandas as pd
import shutil
from sqlalchemy.exc import SQLAlchemyError
from .config import Base, engine, SessionLocal
from ..models.draw import Draw

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', f'init_{datetime.now().strftime("%Y%m%d")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

from ..utils.game_rules import GameRules2025


class DataValidator:
    """数据验证类"""
    
    @staticmethod
    def validate_expect(expect: str) -> bool:
        """验证期号格式"""
        if not expect or not isinstance(expect, str):
            return False
        return expect.isdigit()
    
    @staticmethod
    def validate_open_time(open_time: str) -> Optional[datetime]:
        """验证开奖时间格式并转换为datetime对象"""
        if not open_time or not isinstance(open_time, str):
            return None
        try:
            return datetime.strptime(open_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None
    
    @staticmethod
    def validate_open_code(open_code: str) -> Optional[List[int]]:
        """验证开奖号码格式并转换为整数列表"""
        if not open_code or not isinstance(open_code, str):
            return None
        try:
            numbers = [int(num.strip()) for num in open_code.split(',')]
            if not numbers:
                return None
            # 验证号码范围
            for num in numbers:
                if not (1 <= num <= 49):
                    return None
            return numbers
        except ValueError:
            return None


class DataProcessor:
    """数据处理类"""
    
    @staticmethod
    def get_zodiac(number: int) -> str:
        """根据号码获取生肖"""
        return GameRules2025.get_zodiac(number)
    
    @staticmethod
    def get_color(number: int) -> str:
        """根据号码获取波色"""
        return GameRules2025.get_color(number).replace('波', '')
    
    @staticmethod
    def process_draw_data(draw_data: Dict) -> Optional[Dict]:
        """处理单条开奖数据"""
        try:
            # 验证必填字段
            required_fields = ['openCode', 'expect', 'openTime']
            for field in required_fields:
                if not draw_data.get(field):
                    logger.error(f"Missing or empty required field: {field} in draw_data")
                    return None
            
            open_code = draw_data['openCode'].strip()
            expect = draw_data['expect'].strip()
            open_time = draw_data['openTime'].strip()
            
            # 使用验证器验证数据
            if not DataValidator.validate_expect(expect):
                logger.error(f"Invalid expect format: {expect}")
                return None
            
            draw_time = DataValidator.validate_open_time(open_time)
            if not draw_time:
                logger.error(f"Invalid openTime format: {open_time}")
                return None
            
            numbers = DataValidator.validate_open_code(open_code)
            if not numbers:
                logger.error(f"Invalid openCode format: {open_code}")
                return None
            
            special_number = numbers[-1]  # 最后一个号码是特码
            
            # 计算生肖和波色
            zodiac = DataProcessor.get_zodiac(special_number)
            color = DataProcessor.get_color(special_number)
            if not zodiac or not color:
                logger.error(f"Failed to get zodiac or color for number {special_number}")
                return None
            
            head = special_number // 10
            tail = special_number % 10
            
            processed_data = {
                'expect': expect,
                'open_code': open_code,
                'draw_time': draw_time,
                'zodiac': zodiac,
                'color': color,
                'special_number': special_number,
                'number': special_number,
                'odd_even': "单" if special_number % 2 else "双",
                'big_small': "大" if special_number > 24 else "小",
                'tail_big_small': "尾大" if tail >= 5 else "尾小",
                'sum_odd_even': "合单" if (head + tail) % 2 else "合双",
                'animal_type': "家禽" if zodiac in ["牛", "马", "羊", "鸡", "狗", "猪"] else "野兽",
                'wuxing': GameRules2025.get_wuxing(special_number)
            }
            
            return processed_data
        
        except Exception as e:
            logger.error(f"Error processing draw data: {str(e)}")
            logger.error(f"Raw draw data: {json.dumps(draw_data, ensure_ascii=False)}")
            return None


class DataFetcher:
    """数据获取类"""
    
    @staticmethod
    def fetch_historical_data(year: int, max_retries: int = 3, timeout: int = 10) -> List[Dict]:
        """从API获取历史数据，支持重试机制"""
        url = f"https://api.macaumarksix.com/history/macaujc2/y/{year}"
        for attempt in range(max_retries):
            try:
                logger.info(f"Fetching data for year {year}, attempt {attempt + 1}/{max_retries}")
                response = requests.get(url, timeout=timeout)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('result') and isinstance(data.get('data'), list):
                        logger.info(f"Successfully fetched {year} data, {len(data['data'])} records")
                        return data['data']
                    else:
                        logger.error(f"Invalid data format for year {year}: {data}")
                else:
                    logger.error(f"Failed to fetch data for year {year}: HTTP {response.status_code}")
                
                if attempt < max_retries - 1:
                    wait_time = 3 * (attempt + 1)  # 指数退避策略
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
            
            except requests.exceptions.Timeout:
                logger.error(f"Request timeout for year {year}")
            except requests.exceptions.RequestException as e:
                logger.error(f"Network error for year {year}: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error fetching data for year {year}: {str(e)}")
            
            if attempt < max_retries - 1:
                continue
        
        logger.warning(f"Failed to fetch data for year {year} after {max_retries} attempts")
        return []
    
    @staticmethod
    def fetch_data_from_excel(file_path: str) -> List[Dict]:
        """从Excel文件获取历史数据"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"Excel file not found: {file_path}")
                return []
            
            df = pd.read_excel(file_path)
            if df.empty:
                logger.error(f"Excel file is empty: {file_path}")
                return []
            
            # 验证必要的列是否存在
            required_columns = ['expect', 'openCode', 'openTime']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"Missing required column '{col}' in Excel file")
                    return []
            
            # 转换为字典列表
            data = df.to_dict('records')
            logger.info(f"Successfully loaded {len(data)} records from Excel file")
            return data
        
        except Exception as e:
            logger.error(f"Error loading data from Excel file: {str(e)}")
            return []


class DatabaseInitializer:
    """数据库初始化类"""
    
    @staticmethod
    def backup_database(db_path: str) -> Optional[str]:
        """备份现有数据库"""
        try:
            if not os.path.exists(db_path):
                logger.info(f"No database to backup at {db_path}")
                return None
            
            backup_dir = os.path.dirname(db_path)
            backup_filename = f"lottery_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            shutil.copy2(db_path, backup_path)
            logger.info(f"Created database backup at {backup_path}")
            return backup_path
        
        except Exception as e:
            logger.error(f"Error backing up database: {str(e)}")
            return None
    
    @staticmethod
    def create_tables():
        """创建数据库表"""
        try:
            logger.info("Dropping all tables...")
            Base.metadata.drop_all(bind=engine)
            logger.info("Creating new tables...")
            Base.metadata.create_all(bind=engine)
            return True
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
            return False
    
    @staticmethod
    def import_data(data_list: List[Dict]) -> Tuple[int, int]:
        """导入数据到数据库"""
        db = SessionLocal()
        total_records = 0
        failed_records = 0
        existing_expects = set()
        
        try:
            for draw_data in data_list:
                try:
                    processed_data = DataProcessor.process_draw_data(draw_data)
                    if processed_data and processed_data['expect'] not in existing_expects:
                        draw = Draw(**processed_data)
                        db.add(draw)
                        existing_expects.add(processed_data['expect'])
                        total_records += 1
                        
                        # 每100条记录提交一次，减少内存占用
                        if total_records % 100 == 0:
                            db.commit()
                            logger.info(f"Committed {total_records} records")
                    
                except Exception as e:
                    logger.error(f"Error processing draw {draw_data.get('expect')}: {str(e)}")
                    failed_records += 1
                    continue
            
            # 提交剩余记录
            db.commit()
            logger.info(f"Successfully imported {total_records} records, {failed_records} failed")
            
        except SQLAlchemyError as e:
            logger.error(f"Database error during import: {str(e)}")
            db.rollback()
            raise
        
        finally:
            db.close()
        
        return total_records, failed_records


def init_database():
    """初始化数据库，获取真实历史数据"""
    try:
        # 确保数据目录存在
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"Created data directory at {data_dir}")
        
        # 确保日志目录存在
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            logger.info(f"Created logs directory at {log_dir}")
        
        # 备份现有数据库
        db_path = os.path.join(data_dir, "lottery.db")
        DatabaseInitializer.backup_database(db_path)
        
        # 创建数据库表
        if not DatabaseInitializer.create_tables():
            logger.error("Failed to create database tables, aborting initialization")
            return
        
        logger.info("Initializing database with historical data...")
        
        # 获取2020-2025年的数据
        total_imported = 0
        total_failed = 0
        years = range(2020, 2026)  # 获取2020-2025年的数据
        
        for year in years:
            try:
                logger.info(f"Processing data for year {year}...")
                historical_data = DataFetcher.fetch_historical_data(year)
                
                if not historical_data:
                    logger.warning(f"No data found for year {year}, skipping")
                    continue
                
                imported, failed = DatabaseInitializer.import_data(historical_data)
                total_imported += imported
                total_failed += failed
                
                logger.info(f"Year {year}: imported {imported} records, {failed} failed")
                
            except Exception as e:
                logger.error(f"Error processing year {year}: {str(e)}")
                continue
        
        # 尝试从Excel文件导入数据（如果API获取失败）
        if total_imported == 0:
            logger.warning("No data imported from API, trying to import from Excel file")
            excel_path = os.path.join(os.path.dirname(__file__), "..\temp\lottery_history.xlsx")
            
            if os.path.exists(excel_path):
                excel_data = DataFetcher.fetch_data_from_excel(excel_path)
                if excel_data:
                    imported, failed = DatabaseInitializer.import_data(excel_data)
                    total_imported += imported
                    total_failed += failed
                    logger.info(f"Excel import: {imported} records imported, {failed} failed")
        
        logger.info(f"Database initialization completed: {total_imported} records imported, {total_failed} failed")
        
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise


if __name__ == "__main__":
    # 直接运行此脚本时执行初始化
    init_database()