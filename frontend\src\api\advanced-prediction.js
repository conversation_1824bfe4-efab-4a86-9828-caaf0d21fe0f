import request from '@/utils/request'

// 获取高级预测
export function getAdvancedPrediction(data) {
  return request({
    url: '/api/advanced-prediction/predict',
    method: 'post',
    data
  })
}

// 训练高级预测模型
export function trainAdvancedModel(data) {
  return request({
    url: '/api/advanced-prediction/train',
    method: 'post',
    data
  })
}

// 获取模型状态
export function getModelStatus() {
  return request({
    url: '/api/advanced-prediction/status',
    method: 'get'
  })
}

/**
 * 训练高级预测模型（长超时版本）
 * @param {Object} params - 训练参数
 * @returns {Promise} - 返回训练结果的Promise对象
 */
export function trainAdvancedModelLongTimeout(params) {
    return request({
        url: '/api/advanced-prediction/train',
        method: 'post',
        data: params,
        timeout: 120000  // 将超时时间增加到120秒
    });
}

/**
 * 训练高级预测模型（异步版本）
 * @param {Object} params - 训练参数
 * @param {Function} progressCallback - 进度回调函数
 * @returns {Promise} - 返回训练结果的Promise对象
 */
export function trainAdvancedModelAsync(params, progressCallback) {
  return request({
    url: '/api/advanced-prediction/train',
    method: 'post',
    data: params,
    timeout: 120000,  // 将超时时间从15000ms增加到120000ms (120秒)
    onUploadProgress: progressCallback ? (progressEvent) => {
      progressCallback(progressEvent.loaded / progressEvent.total * 100);
    } : undefined
  });
}