<template>
  <div class="assistant-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>数字选择助手</span>
        </div>
      </template>
      <div>
        <el-form :model="filter" label-width="100px">
          <el-form-item label="选择范围">
            <el-slider
              v-model="filter.range"
              :min="1"
              :max="50"
              :step="1"
              tooltip-placement="bottom"
              show-input
            />
          </el-form-item>
          <el-form-item label="选择个数">
            <el-input-number v-model="filter.count" :min="1" :max="10" />
          </el-form-item>
          <el-form-item label="自动生成">
            <el-switch v-model="filter.autoGenerate" active-text="自动" inactive-text="手动" />
          </el-form-item>
        </el-form>
        <el-button type="primary" @click="generateNumbers" style="margin-top: 10px;">
          推荐号码
        </el-button>
        <div v-if="recommendedNumbers.length" class="result">
          <h4>推荐号码</h4>
          <div class="number-list">
            <el-tag
              v-for="(num, idx) in recommendedNumbers"
              :key="idx"
              class="number-tag"
            >
              {{ num }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const filter = ref({
  range: 50,
  count: 6,
  autoGenerate: true
})

const recommendedNumbers = ref([])

const generateNumbers = () => {
  recommendedNumbers.value = []
  const numbers = new Set()
  while (numbers.size < filter.value.count) {
    const num = Math.floor(Math.random() * filter.value.range) + 1
    numbers.add(num)
  }
  // Convert set to array and sort for display
  recommendedNumbers.value = Array.from(numbers).sort((a, b) => a - b)
}
</script>

<style scoped>
.assistant-container {
  padding: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.number-list {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.number-tag {
  font-size: 16px;
  padding: 5px 10px;
}
</style> 