#!/usr/bin/env python3
"""
直接测试统计函数
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.utils.enhanced_statistics import EnhancedStatistics
from app.utils.statistics import calculate_statistics
from app.models.draw import Draw
from app.database import SessionLocal

def test_enhanced_statistics():
    """直接测试增强版统计函数"""
    print("=== 直接测试增强版统计函数 ===")
    
    try:
        # 获取数据库连接
        db = SessionLocal()
        
        # 获取2025年的数据
        draws = db.query(Draw).filter(Draw.expect.like("2025%")).all()
        print(f"获取到 {len(draws)} 条2025年数据")
        
        if not draws:
            print("❌ 没有找到2025年的数据")
            return False
        
        # 测试增强版统计
        enhanced_stats = EnhancedStatistics()
        result = enhanced_stats.calculate_statistics(draws)
        
        if not result:
            print("❌ 增强版统计返回空结果")
            return False
        
        print("✅ 增强版统计成功执行")
        
        # 检查结果结构
        basic_stats = result.get('basicStats', {})
        cold_numbers = basic_stats.get('coldNumbers', [])
        number_freq = result.get('numberFrequency', {})
        
        print(f"📊 冷门号码数量: {len(cold_numbers)}")
        print(f"📊 前5个冷门号码:")
        for i, num_data in enumerate(cold_numbers[:5]):
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次")
        
        # 检查0次的号码
        zero_count_numbers = [num for num in cold_numbers if num['count'] == 0]
        print(f"\n❄️ 冷门号码中0次的数量: {len(zero_count_numbers)}")
        
        if zero_count_numbers:
            print("✅ 增强版统计正确返回了0次的号码:")
            for num_data in zero_count_numbers:
                print(f"   号码{num_data['number']:02d}: {num_data['count']}次")
        else:
            print("❌ 增强版统计没有返回0次的号码")
        
        # 检查numberFrequency
        zero_freq_numbers = [num for num, count in number_freq.items() if count == 0]
        print(f"\n📋 numberFrequency中0次的号码: {zero_freq_numbers}")
        
        db.close()
        return len(zero_count_numbers) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_original_statistics():
    """测试原始统计函数"""
    print("\n=== 测试原始统计函数 ===")
    
    try:
        # 获取数据库连接
        db = SessionLocal()
        
        # 获取2025年的数据
        draws = db.query(Draw).filter(Draw.expect.like("2025%")).all()
        print(f"获取到 {len(draws)} 条2025年数据")
        
        if not draws:
            print("❌ 没有找到2025年的数据")
            return False
        
        # 测试原始统计
        result = calculate_statistics(draws)
        
        if not result:
            print("❌ 原始统计返回空结果")
            return False
        
        print("✅ 原始统计成功执行")
        
        # 检查结果结构
        basic_stats = result.get('basicStats', {})
        cold_numbers = basic_stats.get('coldNumbers', [])
        number_freq = result.get('numberFrequency', {})
        
        print(f"📊 冷门号码数量: {len(cold_numbers)}")
        print(f"📊 前5个冷门号码:")
        for i, num_data in enumerate(cold_numbers[:5]):
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次")
        
        # 检查0次的号码
        zero_count_numbers = [num for num in cold_numbers if num['count'] == 0]
        print(f"\n❄️ 冷门号码中0次的数量: {len(zero_count_numbers)}")
        
        if zero_count_numbers:
            print("✅ 原始统计正确返回了0次的号码:")
            for num_data in zero_count_numbers:
                print(f"   号码{num_data['number']:02d}: {num_data['count']}次")
        else:
            print("❌ 原始统计没有返回0次的号码")
        
        # 检查numberFrequency
        zero_freq_numbers = [num for num, count in number_freq.items() if count == 0]
        print(f"\n📋 numberFrequency中0次的号码: {zero_freq_numbers}")
        
        db.close()
        return len(zero_count_numbers) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 直接测试统计函数...")
    
    # 测试增强版统计
    enhanced_success = test_enhanced_statistics()
    
    # 测试原始统计
    original_success = test_original_statistics()
    
    print(f"\n📋 测试结果:")
    print(f"   增强版统计: {'✅ 成功' if enhanced_success else '❌ 失败'}")
    print(f"   原始统计: {'✅ 成功' if original_success else '❌ 失败'}")
    
    if enhanced_success:
        print("\n🎉 增强版统计函数工作正常，问题可能在API路由层面！")
    elif original_success:
        print("\n🔧 原始统计函数工作正常，增强版统计需要进一步修复！")
    else:
        print("\n😞 两个统计函数都有问题，需要深入调试！")

if __name__ == "__main__":
    main()
