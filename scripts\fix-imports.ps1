# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== 开始修复导入路径 ===" -ForegroundColor Green

# 修复后端导入
Write-Host "`n修复后端导入路径..." -ForegroundColor Cyan
Get-ChildItem -Path "backend" -Recurse -Filter "*.py" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $content = $content -replace "from src\.", "from "
    Set-Content $_.FullName $content
}

# 检查前端依赖
Write-Host "`n检查前端依赖..." -ForegroundColor Cyan
Set-Location frontend
npm install @vitejs/plugin-vue path --save-dev

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green 