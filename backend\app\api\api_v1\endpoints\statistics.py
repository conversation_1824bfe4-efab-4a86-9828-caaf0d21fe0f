from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict
from app.db.session import get_db
from app.services.statistics_service import (
    get_number_frequency,
    get_zodiac_statistics,
    get_color_statistics,
    get_odd_even_statistics
)

router = APIRouter()


@router.get("/number-frequency", response_model=Dict[str, int])
def get_numbers_frequency(db: Session = Depends(get_db)):
    """获取号码出现频率统计"""
    return get_number_frequency(db)


@router.get("/zodiac", response_model=Dict[str, int])
def get_zodiac_stats(db: Session = Depends(get_db)):
    """获取生肖统计"""
    return get_zodiac_statistics(db)


@router.get("/color", response_model=Dict[str, int])
def get_color_stats(db: Session = Depends(get_db)):
    """获取波色统计"""
    return get_color_statistics(db)


@router.get("/odd-even", response_model=Dict[str, int])
def get_odd_even_stats(db: Session = Depends(get_db)):
    """获取单双统计"""
    return get_odd_even_statistics(db)
