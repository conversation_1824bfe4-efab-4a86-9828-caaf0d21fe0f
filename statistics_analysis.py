#!/usr/bin/env python3
"""
统计分析验证脚本
"""
import requests
import json

def analyze_statistics():
    """分析统计数据的准确性"""
    print("=== 统计数据分析 ===")
    
    try:
        # 获取统计数据
        response = requests.get("http://localhost:8000/api/draw/statistics")
        data = response.json()['data']
        
        basic_stats = data['basicStats']
        number_freq = data['numberFrequency']
        
        print(f"📊 基础统计:")
        print(f"   总开奖次数: {basic_stats['totalCount']}")
        print(f"   有效开奖次数: {basic_stats['validCount']}")
        print(f"   最热特码: {basic_stats['hotNumber']} (出现{basic_stats['hotNumberCount']}次)")
        print(f"   最冷特码: {basic_stats['coldNumber']} (出现{basic_stats['coldNumberCount']}次)")
        print(f"   平均间隔: {basic_stats['averageInterval']:.1f}期")
        
        # 验证号码频率总和
        total_freq = sum(int(count) for count in number_freq.values())
        print(f"\n🔍 数据验证:")
        print(f"   号码频率总和: {total_freq}")
        print(f"   与总开奖次数比较: {'✅ 一致' if total_freq == basic_stats['totalCount'] else '❌ 不一致'}")
        
        # 分析热门号码
        hot_numbers = basic_stats['hotNumbers'][:5]
        print(f"\n🔥 热门号码TOP5:")
        for i, num_info in enumerate(hot_numbers, 1):
            print(f"   {i}. 号码{num_info['number']:02d}: {num_info['count']}次 ({num_info['percentage']:.2f}%)")
        
        # 分析冷门号码
        cold_numbers = basic_stats['coldNumbers'][:5]
        print(f"\n🧊 冷门号码TOP5:")
        for i, num_info in enumerate(cold_numbers, 1):
            print(f"   {i}. 号码{num_info['number']:02d}: {num_info['count']}次 ({num_info['percentage']:.2f}%)")
        
        # 分析波色分布
        color_freq = data['colorFrequency']
        total_colors = sum(color_freq.values())
        print(f"\n🌈 波色分布:")
        for color, count in color_freq.items():
            percentage = (count / total_colors) * 100 if total_colors > 0 else 0
            print(f"   {color}: {count}次 ({percentage:.1f}%)")
        
        # 分析生肖分布
        zodiac_freq = data['zodiacFrequency']
        total_zodiac = sum(zodiac_freq.values())
        print(f"\n🐲 生肖分布:")
        for zodiac, count in zodiac_freq.items():
            percentage = (count / total_zodiac) * 100 if total_zodiac > 0 else 0
            print(f"   {zodiac}: {count}次 ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_frontend_display_issues():
    """检查前端显示问题"""
    print("\n=== 前端显示问题检查 ===")
    
    issues = []
    suggestions = []
    
    # 问题1: 数据加载中显示
    issues.append("1. 最热特码和最冷特码显示'数据加载中...'")
    suggestions.append("   - 检查API数据结构是否匹配前端期望格式")
    suggestions.append("   - 确保hotNumbers和coldNumbers数组有正确的frequency字段")
    
    # 问题2: 平均间隔显示精度
    issues.append("2. 平均间隔显示32.9期，精度可能过高")
    suggestions.append("   - 建议四舍五入到整数或保留1位小数")
    
    # 问题3: 图表显示优化
    issues.append("3. 特码出现次数图表可能需要优化")
    suggestions.append("   - 添加数据标签显示具体数值")
    suggestions.append("   - 优化颜色搭配和视觉效果")
    
    print("🔍 发现的问题:")
    for issue in issues:
        print(f"   {issue}")
    
    print("\n💡 优化建议:")
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("开始统计分析验证...")
    
    if analyze_statistics():
        check_frontend_display_issues()
        print("\n✅ 分析完成！")
    else:
        print("\n❌ 分析失败！")

if __name__ == "__main__":
    main()
