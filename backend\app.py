from flask import Flask, send_from_directory
from flask_cors import CORS
from api.routes import api_bp as api
from utils.error_handler import init_error_handlers
from utils.logger import setup_logger
from config.config import BASE_CONFIG, API_CONFIG
from models.database import db, DatabaseManager
from utils.performance_monitor import init_performance_monitor

logger = setup_logger('app')


def create_app():
    """创建Flask应用"""
    try:
        app = Flask(__name__, static_folder='../frontend/dist')

        # 加载配置
        app.config.update(BASE_CONFIG)

        # 初始化CORS
        CORS(app)

        # 初始化数据库
        db.init_app(app)
        with app.app_context():
            db.create_all()

        # 注册API蓝图
        app.register_blueprint(api, url_prefix='/api')

        # 初始化错误处理器
        init_error_handlers(app)

        # 初始化性能监控
        init_performance_monitor(app)

        # 添加根路由，用于提供前端静态文件
        @app.route('/')
        def serve_frontend():
            return send_from_directory(app.static_folder, 'index.html')

        @app.route('/<path:path>')
        def serve_static(path):
            return send_from_directory(app.static_folder, path)

        logger.info("应用程序初始化完成")
        return app

    except Exception as e:
        logger.error(f"应用程序初始化失败: {str(e)}")
        raise


def main():
    """主函数"""
    app = create_app()
    app.run(
        host=BASE_CONFIG['host'],
        port=BASE_CONFIG['port'],
        debug=BASE_CONFIG['debug']
    )


if __name__ == '__main__':
    main()
