from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
import logging
from sqlalchemy import and_, func

from ..database import get_db
from ..models import Draw
from ..schemas.draw import (
    DrawResponse,
    NextDrawInfoResponse,
    DrawHistoryResponse
)
from ..utils.statistics import calculate_statistics
from ..utils.enhanced_statistics import EnhancedStatistics
from ..services.draw_service import get_latest_draw, get_next_draw_info, update_latest_draw
from ..utils.data_manager import get_data_manager

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["api_draw"])


@router.get("/draw/history", response_model=DrawHistoryResponse)
async def get_draw_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    limit: int = Query(5, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """获取开奖历史记录"""
    try:
        logger.info(
            f"获取开奖历史记录: page={page}, page_size={page_size}, limit={limit}")

        # 查询总记录数
        total = db.query(Draw).count()

        # 查询分页数据（按期号排序，期号越大越新）
        draws = db.query(Draw).order_by(Draw.expect.desc()).offset(
            (page - 1) * page_size).limit(page_size).all()

        if not draws:
            logger.warning("未找到开奖记录")
            return {"total": 0, "page": page, "page_size": page_size, "items": []}

        logger.info(f"找到 {len(draws)} 条开奖记录")
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "items": draws
        }
    except Exception as e:
        logger.error(f"获取开奖历史记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取开奖历史记录失败: {str(e)}")


@router.get("/draw/next", response_model=NextDrawInfoResponse)
async def get_next_draw(
    force_update: bool = Query(False, description="是否强制从API获取最新数据"),
    db: Session = Depends(get_db)
):
    """获取下一期信息"""
    try:
        logger.info(f"获取下一期信息, force_update={force_update}")

        # 如果需要强制更新，从API获取最新数据
        if force_update:
            logger.info("强制从API获取最新开奖结果")
            # 尝试从API获取最新数据
            try:
                data_manager = get_data_manager(db)
                latest_draw = data_manager.sync_latest_draw(force_update=True)
                if latest_draw:
                    logger.info(f"成功从API获取最新开奖结果: {latest_draw.expect}")
                    # 数据管理器已经处理了数据的验证、清洗和存储
            except Exception as api_error:
                logger.error(f"从API获取最新开奖结果失败: {str(api_error)}")
                # 如果API获取失败，尝试使用服务获取
                latest_draw = get_latest_draw(db, force_update=True)
                if latest_draw:
                    logger.info(f"成功从服务获取最新开奖结果: {latest_draw.expect}")

        # 使用服务获取下一期信息
        next_draw_info = get_next_draw_info(db)

        if not next_draw_info:
            logger.warning("未能获取下一期信息")
            raise HTTPException(status_code=404, detail="未能获取下一期信息")

        logger.info(
            f"下一期信息: expect={next_draw_info['expect']}, draw_time={next_draw_info['draw_time']}")
        return next_draw_info
    except HTTPException as e:
        # 重新抛出HTTP异常
        raise e
    except Exception as e:
        logger.error(f"获取下一期信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取下一期信息失败: {str(e)}")


@router.get("/draw/latest", response_model=List[DrawResponse])
async def get_latest_draws(
    limit: int = Query(5, ge=1, le=50),
    force_update: bool = Query(False, description="是否强制从API获取最新数据"),
    db: Session = Depends(get_db)
):
    """获取最新开奖结果"""
    try:
        logger.info(f"获取最新开奖结果: limit={limit}, force_update={force_update}")

        # 如果需要强制更新，从API获取最新数据
        if force_update:
            logger.info("强制从API获取最新开奖结果")
            # 尝试从API获取最新数据
            try:
                data_manager = get_data_manager(db)
                latest_draw = data_manager.sync_latest_draw(force_update=True)
                if latest_draw:
                    logger.info(f"成功从API获取最新开奖结果: {latest_draw.expect}")
                    # 数据管理器已经处理了数据的验证、清洗和存储
            except Exception as api_error:
                logger.error(f"从API获取最新开奖结果失败: {str(api_error)}")
                # 如果API获取失败，尝试使用服务获取
                latest_draw = get_latest_draw(db, force_update=True)
                if latest_draw:
                    logger.info(f"成功从服务获取最新开奖结果: {latest_draw.expect}")

        # 从数据库获取最新记录（按期号排序，期号越大越新）
        draws = db.query(Draw).order_by(
            Draw.expect.desc()).limit(limit).all()

        if not draws:
            logger.warning("未找到开奖记录")
            return []

        logger.info(f"找到 {len(draws)} 条开奖记录")
        return draws
    except Exception as e:
        logger.error(f"获取最新开奖结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最新开奖结果失败: {str(e)}")


@router.get("/draw/year/{year}")
async def get_draws_by_year(
    year: int,
    db: Session = Depends(get_db)
):
    """获取指定年份的开奖记录"""
    try:
        logger.info(f"获取 {year} 年的开奖记录")

        # 首先尝试从数据库获取（按期号排序）
        draws = db.query(Draw).filter(Draw.expect.like(
            f"{year}%")).order_by(Draw.expect.desc()).all()

        # 如果数据库中没有数据，则从API获取
        if not draws:
            logger.info(f"数据库中没有 {year} 年的开奖记录，尝试从API获取")

            # 从API获取数据
            data_manager = get_data_manager(db)
            success_count, _ = data_manager.sync_historical_data(year)
            # 重新查询数据库获取同步后的数据（按期号排序）
            draws = db.query(Draw).filter(Draw.expect.like(
                f"{year}%")).order_by(Draw.expect.desc()).all()
            return {"code": 200, "message": f"成功同步 {success_count} 条数据", "data": draws}

        if not draws:
            logger.warning(f"未找到 {year} 年的开奖记录")
            return {"code": 200, "message": "success", "data": []}

        logger.info(f"找到 {len(draws)} 条 {year} 年的开奖记录")
        return {"code": 200, "message": "success", "data": draws}
    except Exception as e:
        logger.error(f"获取 {year} 年的开奖记录失败: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"获取 {year} 年的开奖记录失败: {str(e)}")


@router.get("/draw/expect/{expect}")
async def get_draw_by_expect(
    expect: str,
    db: Session = Depends(get_db)
):
    """获取指定期号的开奖记录"""
    try:
        logger.info(f"获取期号 {expect} 的开奖记录")

        # 首先尝试从数据库获取
        draw = db.query(Draw).filter(Draw.expect == expect).first()

        # 如果数据库中没有数据，则从API获取
        if not draw:
            logger.info(f"数据库中没有期号 {expect} 的开奖记录，尝试从API获取")

            # 使用数据管理器获取指定期号的数据
            data_manager = get_data_manager(db)
            # 尝试从API获取并保存到数据库
            try:
                # 使用DataSynchronizer获取数据
                from ..utils.data_manager import DataSynchronizer
                api_draw = DataSynchronizer.fetch_by_expect(expect)

                if api_draw:
                    logger.info(f"从API获取到期号 {expect} 的开奖记录")

                    # 创建新记录
                    new_draw = Draw(**api_draw)
                    db.add(new_draw)
                    db.commit()
                    db.refresh(new_draw)
                    draw = new_draw
                else:
                    logger.warning(f"无法从API获取期号 {expect} 的开奖记录")
            except Exception as e:
                logger.error(f"获取或保存期号 {expect} 的开奖记录失败: {str(e)}")

        if not draw:
            logger.warning(f"未找到期号 {expect} 的开奖记录")
            raise HTTPException(
                status_code=404, detail=f"未找到期号 {expect} 的开奖记录")

        logger.info(f"找到期号 {expect} 的开奖记录")
        return {"code": 200, "message": "success", "data": draw}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取期号 {expect} 的开奖记录失败: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"获取期号 {expect} 的开奖记录失败: {str(e)}")


@router.get("/draw/statistics")
async def get_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    year: Optional[int] = None,
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取开奖统计数据"""
    try:
        logger.info(
            f"获取统计数据: year={year}, start_period={start_period}, end_period={end_period}")

        query = db.query(Draw)
        conditions = []

        # 年份筛选
        if year:
            conditions.append(Draw.expect.like(f"{year}%"))

        # 期数范围筛选
        if start_period:
            conditions.append(Draw.expect >= start_period)
        if end_period:
            conditions.append(Draw.expect <= end_period)

        # 日期范围筛选
        if start_date:
            conditions.append(Draw.draw_time >= f"{start_date} 00:00:00")
        if end_date:
            conditions.append(Draw.draw_time <= f"{end_date} 23:59:59")

        if conditions:
            query = query.filter(and_(*conditions))

        draws = query.order_by(Draw.draw_time).all()

        if not draws:
            logger.warning("未找到符合条件的开奖记录")
            # 返回空统计数据
            empty_stats = {
                "basicStats": {
                    "totalCount": 0,
                    "hotNumbers": [],
                    "coldNumbers": [],
                    "hotNumber": None,
                    "hotNumberCount": 0,
                    "coldNumber": None,
                    "coldNumberCount": 0,
                    "averageInterval": 0
                },
                "numberFrequency": {str(i): 0 for i in range(1, 50)},
                "colorFrequency": {'红波': 0, '蓝波': 0, '绿波': 0},
                "tailFrequency": {str(i): 0 for i in range(10)},
                "headFrequency": {str(i): 0 for i in range(5)},
                "zodiacFrequency": {zodiac: 0 for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]},
                "wuxingFrequency": {wuxing: 0 for wuxing in ["金", "木", "水", "火", "土"]},
                "attributes": {
                    "单": 0, "双": 0, "大": 0, "小": 0,
                    "家禽": 0, "野兽": 0,
                    "尾单": 0, "尾双": 0,
                    "尾大": 0, "尾小": 0,
                    "合单": 0, "合双": 0,
                    "合大": 0, "合小": 0
                },
                "missing": {
                    "current": {str(i): 0 for i in range(1, 50)},
                    "max": {str(i): 0 for i in range(1, 50)},
                    "lastAppearance": {str(i): None for i in range(1, 50)}
                },
                "total_draws": 0,
                "period_range": "",
                "date_range": "",
                "hot_numbers": [],
                "cold_numbers": []
            }

            return {
                "code": 200,
                "message": "success",
                "data": empty_stats
            }

        logger.info(f"找到 {len(draws)} 条开奖记录，开始计算统计数据")

        # 计算统计数据
        # 使用增强版统计分析类
        enhanced_stats = EnhancedStatistics()
        stats = enhanced_stats.calculate_statistics(draws)

        logger.info(f"增强版统计结果: stats存在={bool(stats)}")
        if stats:
            logger.info(f"missing字段存在={bool(stats.get('missing'))}")
            if stats.get("missing"):
                logger.info(
                    f"missing.current字段存在={bool(stats['missing'].get('current'))}")

        # 如果增强版统计失败，则使用原始版统计
        if not stats or not stats.get("missing") or not stats["missing"].get("current"):
            logger.warning("增强版统计失败，使用原始版统计")
            stats = calculate_statistics(draws)
            logger.info("已切换到原始版统计")

        logger.info(f"统计数据计算完成，返回结果")

        return {
            "code": 200,
            "message": "success",
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")
