import pytest
from datetime import datetime
from app.models.draw import Draw
from app.models.prediction import Prediction


def test_get_draws(client, db_session):
    # 创建测试数据
    draw = Draw(
        expect="2025001",
        numbers=[1, 2, 3, 4, 5, 6],
        draw_time=datetime.now(),
        special_number=7,
        zodiac="鼠",
        color="红",
        odd_even="单",
        big_small="大",
        tail_big_small="尾大",
        sum_odd_even="和单",
        wuxing="金"
    )
    db_session.add(draw)
    db_session.commit()

    # 测试获取开奖记录
    response = client.get("/api/v1/draws/")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["expect"] == "2025001"
    assert data[0]["numbers"] == [1, 2, 3, 4, 5, 6]


def test_get_statistics(client, db_session):
    # 创建测试数据
    draw = Draw(
        expect="2025001",
        numbers=[1, 2, 3, 4, 5, 6],
        draw_time=datetime.now(),
        special_number=7,
        zodiac="鼠",
        color="红",
        odd_even="单",
        big_small="大",
        tail_big_small="尾大",
        sum_odd_even="和单",
        wuxing="金"
    )
    db_session.add(draw)
    db_session.commit()

    # 测试获取统计信息
    response = client.get("/api/v1/statistics/")
    assert response.status_code == 200
    data = response.json()
    assert "number_frequency" in data
    assert "zodiac_stats" in data
    assert "color_stats" in data
    assert "hot_numbers" in data
    assert "cold_numbers" in data


def test_get_predictions(client, db_session):
    # 创建测试数据
    prediction = Prediction(
        expect="2025001",
        predicted_numbers=[1, 2, 3, 4, 5],
        confidence=0.8,
        model_version="1.0.0",
        prediction_time=datetime.now(),
        is_correct=1
    )
    db_session.add(prediction)
    db_session.commit()

    # 测试获取预测记录
    response = client.get("/api/v1/predictions/")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["expect"] == "2025001"
    assert data[0]["predicted_numbers"] == [1, 2, 3, 4, 5]
    assert data[0]["confidence"] == 0.8


def test_get_prediction_accuracy(client, db_session):
    # 创建测试数据
    for i in range(10):
        prediction = Prediction(
            expect=f"2025{i+1:03d}",
            predicted_numbers=[1, 2, 3, 4, 5],
            confidence=0.8,
            model_version="1.0.0",
            prediction_time=datetime.now(),
            is_correct=1 if i < 5 else 0
        )
        db_session.add(prediction)
    db_session.commit()

    # 测试获取预测准确率
    response = client.get("/api/v1/predictions/accuracy")
    assert response.status_code == 200
    data = response.json()
    assert data["accuracy"] == 0.5
    assert data["total_predictions"] == 10
    assert data["correct_predictions"] == 5


def test_train_model(client, db_session):
    # 创建测试数据
    for i in range(15):
        draw = Draw(
            expect=f"2025{i+1:03d}",
            numbers=[1, 2, 3, 4, 5, 6],
            draw_time=datetime.now(),
            special_number=7,
            zodiac="鼠",
            color="红",
            odd_even="单",
            big_small="大",
            tail_big_small="尾大",
            sum_odd_even="和单",
            wuxing="金"
        )
        db_session.add(draw)
    db_session.commit()

    # 测试训练模型
    response = client.post("/api/v1/models/train")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "model_version" in data
