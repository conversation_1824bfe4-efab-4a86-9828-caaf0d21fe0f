from functools import wraps
import json
from typing import Any, Callable, Optional
from redis import Redis
from app.core.config import settings

# 创建Redis连接
redis_client = Redis.from_url(settings.REDIS_URL)


def cache_key(prefix: str, *args, **kwargs) -> str:
    """生成缓存键"""
    key_parts = [prefix]
    if args:
        key_parts.extend([str(arg) for arg in args])
    if kwargs:
        key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])
    return ":".join(key_parts)


def cache(expire: int = 300, prefix: str = "cache"):
    """
    缓存装饰器
    :param expire: 缓存过期时间（秒）
    :param prefix: 缓存键前缀
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # 生成缓存键
            key = cache_key(prefix, *args, **kwargs)

            # 尝试从缓存获取数据
            cached_data = redis_client.get(key)
            if cached_data:
                return json.loads(cached_data)

            # 执行原函数
            result = await func(*args, **kwargs)

            # 将结果存入缓存
            redis_client.setex(
                key,
                expire,
                json.dumps(result)
            )

            return result
        return wrapper
    return decorator


def invalidate_cache(prefix: str, *args, **kwargs) -> None:
    """
    使缓存失效
    :param prefix: 缓存键前缀
    """
    key = cache_key(prefix, *args, **kwargs)
    redis_client.delete(key)


def clear_cache_pattern(pattern: str) -> None:
    """
    清除匹配模式的缓存
    :param pattern: 缓存键模式
    """
    keys = redis_client.keys(pattern)
    if keys:
        redis_client.delete(*keys)


class CacheManager:
    """缓存管理器"""

    @staticmethod
    def get(key: str) -> Optional[Any]:
        """获取缓存"""
        data = redis_client.get(key)
        return json.loads(data) if data else None

    @staticmethod
    def set(key: str, value: Any, expire: int = 300) -> None:
        """设置缓存"""
        redis_client.setex(
            key,
            expire,
            json.dumps(value)
        )

    @staticmethod
    def delete(key: str) -> None:
        """删除缓存"""
        redis_client.delete(key)

    @staticmethod
    def clear_pattern(pattern: str) -> None:
        """清除匹配模式的缓存"""
        clear_cache_pattern(pattern)

    @staticmethod
    def clear_all() -> None:
        """清除所有缓存"""
        redis_client.flushdb()
