from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.init_db import get_db
from app.schemas.prediction import PredictionCreate, PredictionResponse

router = APIRouter()


@router.get("/", response_model=List[PredictionResponse])
def get_predictions(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取预测记录列表"""
    return []


@router.post("/", response_model=PredictionResponse)
def create_prediction(
    prediction: PredictionCreate,
    db: Session = Depends(get_db)
):
    """创建新的预测记录"""
    return {}
