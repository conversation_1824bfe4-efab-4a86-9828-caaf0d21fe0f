/* 全局样式 */
html {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移动端点击高亮 */
* {
  -webkit-tap-highlight-color: transparent;
}

/* 布局相关 */
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

/* 响应式布局 */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  box-sizing: border-box;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .responsive-container {
    padding: 0 10px;
  }

  .el-card {
    margin-bottom: 10px;
  }

  .el-table {
    width: 100%;
    overflow-x: auto;
    
    &::before {
      display: none;
    }
    
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }

  .el-form-item {
    margin-bottom: 15px;
  }

  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  .el-message {
    min-width: auto !important;
    width: 90% !important;
  }
}

/* 通用间距 */
.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-4 {
  margin-right: 1rem;
}

/* 移动端间距 */
@media screen and (max-width: 768px) {
  .mt-4 {
    margin-top: 0.5rem;
  }

  .mb-4 {
    margin-bottom: 0.5rem;
  }

  .ml-4 {
    margin-left: 0.5rem;
  }

  .mr-4 {
    margin-right: 0.5rem;
  }
}

/* Element Plus 组件样式覆盖 */
.el-card {
  --el-card-padding: 16px;
  
  .el-card__header {
    padding: var(--el-card-padding);
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  .el-card__body {
    padding: var(--el-card-padding);
  }
}

/* 移动端卡片样式 */
@media screen and (max-width: 768px) {
  .el-card {
    --el-card-padding: 12px;
  }
}

.el-table {
  --el-table-header-bg-color: var(--el-bg-color);
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
  
  th {
    font-weight: 600;
  }
}

.el-pagination {
  margin-top: 1rem;
  justify-content: flex-end;
}

/* 移动端分页样式 */
@media screen and (max-width: 768px) {
  .el-pagination {
    justify-content: center;
    
    .el-pagination__sizes {
      display: none !important;
    }
  }
} 