"""
数据同步服务，用于从外部API获取开奖数据并保存到数据库
"""
import logging
import requests
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models.prediction import Prediction, DrawResult

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DataSyncService:
    """数据同步服务"""

    def __init__(self):
        self.api_url = "https://macaumarksix.com/api/macaujc2.com"
        self.backup_api_urls = [
            "https://macaumarksix.com/api/macaujc.com",
            "https://macaumarksix.com/api/macaujc1.com"
        ]
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    def fetch_latest_data(self) -> Optional[Dict[str, Any]]:
        """从API获取最新开奖数据"""
        try:
            logger.info(f"从 {self.api_url} 获取最新开奖数据...")
            response = requests.get(
                self.api_url, headers=self.headers, timeout=10)
            response.raise_for_status()

            data = response.json()
            if not data or not isinstance(data, list) or len(data) == 0:
                logger.warning("API返回数据为空或格式不正确")
                return None

            # 返回第一条数据（最新一期）
            latest_data = data[0]
            logger.info(f"获取到最新开奖数据: 期号 {latest_data.get('expect')}")
            return latest_data
        except Exception as e:
            logger.error(f"获取最新开奖数据失败: {str(e)}")

            # 尝试备用API
            for backup_url in self.backup_api_urls:
                try:
                    logger.info(f"尝试从备用API {backup_url} 获取数据...")
                    response = requests.get(
                        backup_url, headers=self.headers, timeout=10)
                    response.raise_for_status()

                    data = response.json()
                    if data and isinstance(data, list) and len(data) > 0:
                        latest_data = data[0]
                        logger.info(
                            f"从备用API获取到最新开奖数据: 期号 {latest_data.get('expect')}")
                        return latest_data
                except Exception as backup_e:
                    logger.error(f"从备用API获取数据失败: {str(backup_e)}")

            return None

    def process_and_save_data(self, data: Dict[str, Any]) -> bool:
        """处理并保存开奖数据到数据库"""
        try:
            # 数据清洗和转换
            cleaned_data = self._clean_data(data)
            if not cleaned_data:
                logger.warning("数据清洗失败，无法保存")
                return False

            # 保存到数据库
            with SessionLocal() as db:
                # 检查是否已存在该期号的记录
                expect = cleaned_data["expect"]
                existing_result = db.query(DrawResult).filter(
                    DrawResult.expect == expect).first()

                if existing_result:
                    logger.info(f"期号 {expect} 的开奖结果已存在，更新记录")
                    # 更新现有记录
                    for key, value in cleaned_data.items():
                        setattr(existing_result, key, value)
                else:
                    logger.info(f"创建期号 {expect} 的新开奖结果记录")
                    # 创建新记录
                    new_result = DrawResult(**cleaned_data)
                    db.add(new_result)

                # 提交事务
                db.commit()
                logger.info(f"成功保存期号 {expect} 的开奖结果")

                # 更新预测记录的实际结果
                self._update_prediction_results(db, expect, cleaned_data)

                return True
        except Exception as e:
            logger.error(f"保存开奖数据失败: {str(e)}")
            return False

    def _clean_data(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """清洗和转换API返回的数据"""
        try:
            # 检查必要字段
            required_fields = ["expect", "openCode", "openTime"]
            for field in required_fields:
                if field not in data or not data[field]:
                    logger.warning(f"数据缺少必要字段: {field}")
                    return None

            # 解析开奖号码
            open_code = data["openCode"]
            numbers = [int(num.strip())
                       for num in open_code.split(",") if num.strip().isdigit()]

            if len(numbers) < 6:
                logger.warning(f"开奖号码格式不正确: {open_code}")
                return None

            # 解析生肖（可选字段）
            zodiac_list = []
            if "zodiac" in data and data["zodiac"]:
                zodiac_list = [z.strip()
                               for z in data["zodiac"].split(",") if z.strip()]

            # 解析波色（可选字段）
            wave_list = []
            if "wave" in data and data["wave"]:
                wave_list = [w.strip()
                             for w in data["wave"].split(",") if w.strip()]

            # 解析开奖时间
            try:
                open_time = datetime.strptime(
                    data["openTime"], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logger.warning(f"开奖时间格式不正确: {data['openTime']}")
                open_time = datetime.now()  # 使用当前时间作为备用

            # 修正期号格式（如果需要）
            expect = data["expect"]
            if len(expect) > 7:
                # 如果期号格式错误（如20250101001），则修正为正确格式（年份+期号）
                expect = expect[:4] + expect[-3:]
                logger.info(f"修正期号格式为: {expect}")

            # 确保有足够的号码（至少7个）
            if len(numbers) < 7:
                logger.warning(f"开奖号码数量不足7个: {len(numbers)}")
                return None

            # 构建清洗后的数据
            cleaned_data = {
                "expect": expect,
                "draw_time": open_time,
                "numbers": numbers,
                "special_number": numbers[-1],  # 特码是最后一个号码
                "zodiac_list": zodiac_list,
                "color_list": wave_list,
                "source": "macaumarksix.com",
                "raw_data": json.dumps(data)
            }

            logger.info(f"成功清洗数据: 期号 {expect}, 号码 {numbers}, 特码 {numbers[-1]}")
            return cleaned_data
        except Exception as e:
            logger.error(f"数据清洗失败: {str(e)}")
            return None

    def _update_prediction_results(self, db: Session, expect: str, draw_data: Dict[str, Any]) -> None:
        """更新预测记录的实际结果和准确度"""
        try:
            # 查找该期号的预测记录
            prediction = db.query(Prediction).filter(
                Prediction.expect == expect).first()
            if not prediction:
                logger.info(f"未找到期号 {expect} 的预测记录，无需更新")
                return

            # 获取特码
            special_number = draw_data.get("special_number")
            if not special_number:
                logger.warning(f"期号 {expect} 的特码为空，无法更新预测准确度")
                return

            # 更新实际结果
            prediction.actual_result = special_number

            # 计算预测准确度
            from ..ml.mock_prediction_service import MockPredictionService
            prediction_service = MockPredictionService()

            # 构建预测结果字典
            prediction_dict = {
                "special_numbers_5": prediction.special_numbers_5,
                "special_numbers_10": prediction.special_numbers_10,
                "special_numbers_15": prediction.special_numbers_15,
                "special_numbers_20": prediction.special_numbers_20,
                "special_numbers_30": prediction.special_numbers_30,
                "attribute_predictions": prediction.attribute_predictions
            }

            # 计算准确度
            accuracy = prediction_service.evaluate_prediction(
                prediction_dict, special_number)
            prediction.accuracy = accuracy

            # 提交更新
            db.commit()
            logger.info(f"成功更新期号 {expect} 的预测准确度: {accuracy}")
        except Exception as e:
            logger.error(f"更新预测准确度失败: {str(e)}")

    def sync_latest_data(self) -> bool:
        """同步最新开奖数据"""
        latest_data = self.fetch_latest_data()
        if not latest_data:
            logger.warning("无法获取最新开奖数据，同步失败")
            return False

        return self.process_and_save_data(latest_data)

    def get_historical_data(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取历史开奖数据"""
        try:
            with SessionLocal() as db:
                results = db.query(DrawResult).order_by(
                    DrawResult.draw_time.desc()).limit(limit).all()

                historical_data = []
                for result in results:
                    historical_data.append({
                        "expect": result.expect,
                        "draw_time": result.draw_time.strftime("%Y-%m-%d %H:%M:%S") if result.draw_time else None,
                        "numbers": result.numbers,
                        "special_number": result.special_number,
                        "zodiac_list": result.zodiac_list,
                        "color_list": result.color_list
                    })

                return historical_data
        except Exception as e:
            logger.error(f"获取历史开奖数据失败: {str(e)}")
            return []

    def get_training_data(self, limit: int = 100) -> List[int]:
        """获取用于训练的历史特码数据"""
        try:
            with SessionLocal() as db:
                results = db.query(DrawResult).order_by(
                    DrawResult.draw_time.desc()).limit(limit).all()

                # 提取特码数据
                special_numbers = []
                for result in results:
                    if result.special_number is not None:
                        special_numbers.append(result.special_number)

                return special_numbers
        except Exception as e:
            logger.error(f"获取训练数据失败: {str(e)}")
            return []
