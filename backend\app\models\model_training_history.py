from sqlalchemy import Column, Integer, String, DateTime, JSON, Float
from sqlalchemy.sql import func
from app.db.base_class import Base


class ModelTrainingHistory(Base):
    """模型训练历史记录"""

    __tablename__ = "model_training_history"

    id = Column(Integer, primary_key=True, index=True)
    training_time = Column(DateTime, nullable=False, default=func.now())
    model_name = Column(String(50), nullable=False)
    parameters = Column(JSON, nullable=True)  # 模型参数
    metrics = Column(JSON, nullable=True)  # 训练指标
    status = Column(String(20), nullable=False)  # 训练状态：success/failed
    data_range = Column(String(100), nullable=True)  # 数据范围
    data_count = Column(Integer, nullable=True)  # 训练数据量
    period_range = Column(String(100), nullable=True)  # 时间范围
    custom_range = Column(String(100), nullable=True)  # 自定义范围
    recent_count = Column(Integer, nullable=True)  # 最近数据量
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False,
                        default=func.now(), onupdate=func.now())
