#!/usr/bin/env python3
"""
测试数据同步功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from app.utils.data_manager import DataValidator, DataProcessor, DataSynchronizer

def test_api_data():
    """测试API数据获取和处理"""
    print("=== 测试API数据获取 ===")
    
    # 获取API数据
    try:
        response = requests.get('https://macaumarksix.com/api/macaujc2.com', timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if not data or not isinstance(data, list):
            print("❌ API返回数据格式错误")
            return False
            
        latest_data = data[0]
        print(f"✅ 成功获取API数据: {latest_data.get('expect')}")
        print(f"原始数据: {json.dumps(latest_data, indent=2, ensure_ascii=False)}")
        
        # 测试数据验证
        print("\n=== 测试数据验证 ===")
        is_valid = DataValidator.validate_draw_data(latest_data)
        print(f"数据验证结果: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        if not is_valid:
            # 详细检查每个字段
            print("详细验证结果:")
            print(f"- expect: {DataValidator.validate_expect(latest_data.get('expect'))}")
            print(f"- openTime: {DataValidator.validate_open_time(latest_data.get('openTime'))}")
            print(f"- openCode: {DataValidator.validate_open_code(latest_data.get('openCode'))}")
        
        # 测试数据处理
        print("\n=== 测试数据处理 ===")
        processed_data = DataProcessor.process_draw_data(latest_data)
        if processed_data:
            print("✅ 数据处理成功")
            print(f"处理后数据: {json.dumps(processed_data, indent=2, ensure_ascii=False, default=str)}")
        else:
            print("❌ 数据处理失败")
            
        return processed_data is not None
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_data_synchronizer():
    """测试数据同步器"""
    print("\n=== 测试数据同步器 ===")
    
    try:
        latest_data = DataSynchronizer.fetch_latest_draw()
        if latest_data:
            print("✅ 数据同步器获取数据成功")
            print(f"同步器返回数据: {json.dumps(latest_data, indent=2, ensure_ascii=False, default=str)}")
        else:
            print("❌ 数据同步器获取数据失败")
            
        return latest_data is not None
        
    except Exception as e:
        print(f"❌ 数据同步器测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试数据同步功能...\n")
    
    # 测试API数据
    api_success = test_api_data()
    
    # 测试数据同步器
    sync_success = test_data_synchronizer()
    
    print(f"\n=== 测试结果 ===")
    print(f"API数据测试: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"数据同步器测试: {'✅ 成功' if sync_success else '❌ 失败'}")
    
    if api_success and sync_success:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 存在问题需要修复")
