<template>
  <div class="backtest-page">
    <!-- 回测参数设置 -->
    <el-card class="parameter-card">
      <template #header>
        <div class="card-header">
          <h2>回测参数</h2>
          <div class="header-actions">
            <el-button type="primary" @click="startBacktest" :loading="loading">
              开始回测
            </el-button>
            <el-button type="success" @click="exportResults" :disabled="!backtestResult">
              导出结果
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="backtestParams" label-width="120px" class="parameter-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="handleDateRangeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预测范围">
              <el-input-number
                v-model="backtestParams.prediction_range"
                :min="5"
                :max="30"
                :step="5"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="选择模型">
          <el-checkbox-group v-model="backtestParams.models">
            <el-checkbox label="lstm">LSTM</el-checkbox>
            <el-checkbox label="rf">随机森林</el-checkbox>
            <el-checkbox label="xgboost">XGBoost</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 新增：策略设置 -->
        <el-divider>策略设置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="回测策略">
              <el-select v-model="backtestParams.strategy" placeholder="请选择策略">
                <el-option
                  v-for="item in strategyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投资金额">
              <el-input-number
                v-model="backtestParams.investment_amount"
                :min="10"
                :max="1000"
                :step="10"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="止损比例">
              <el-input-number
                v-model="backtestParams.stop_loss"
                :min="10"
                :max="90"
                :step="5"
              >
                <template #suffix>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="止盈比例">
              <el-input-number
                v-model="backtestParams.take_profit"
                :min="110"
                :max="500"
                :step="10"
              >
                <template #suffix>%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="置信度阈值">
              <el-input-number
                v-model="backtestParams.confidence_threshold"
                :min="0.3"
                :max="0.9"
                :step="0.05"
                :precision="2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 回测结果 -->
    <template v-if="backtestResult">
      <!-- 回测概览 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="6">
          <el-card class="stat-card">
            <template #header>
              <div class="stat-header">
                <span>总预测次数</span>
              </div>
            </template>
            <div class="stat-content">
              <span class="stat-value">{{ backtestResult.totalPredictions }}</span>
              <span class="stat-label">次</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <template #header>
              <div class="stat-header">
                <span>命中次数</span>
              </div>
            </template>
            <div class="stat-content">
              <span class="stat-value">{{ backtestResult.hits }}</span>
              <span class="stat-label">次</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <template #header>
              <div class="stat-header">
                <span>平均准确率</span>
              </div>
            </template>
            <div class="stat-content">
              <span class="stat-value">{{ (backtestResult.averageAccuracy * 100).toFixed(2) }}</span>
              <span class="stat-label">%</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <template #header>
              <div class="stat-header">
                <span>投资回报率</span>
              </div>
            </template>
            <div class="stat-content">
              <span class="stat-value" :class="{ 'positive': backtestResult.roi > 0, 'negative': backtestResult.roi < 0 }">
                {{ (backtestResult.roi * 100).toFixed(2) }}
              </span>
              <span class="stat-label">%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 新增：趋势分析图表 -->
      <el-card class="mt-4">
        <template #header>
          <div class="card-header">
            <h3>趋势分析</h3>
            <el-select v-model="backtestParams.analysis_period" @change="updateTrendChart">
              <el-option label="7天" :value="7" />
              <el-option label="14天" :value="14" />
              <el-option label="30天" :value="30" />
            </el-select>
          </div>
        </template>
        <div ref="trendChartRef" style="height: 400px;"></div>
      </el-card>

      <!-- 新增：策略分析 -->
      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <h3>策略绩效分析</h3>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="总投资金额">
                {{ backtestResult.strategyAnalysis?.totalInvestment?.toFixed(2) }} 元
              </el-descriptions-item>
              <el-descriptions-item label="总收益金额">
                {{ backtestResult.strategyAnalysis?.totalProfit?.toFixed(2) }} 元
              </el-descriptions-item>
              <el-descriptions-item label="最大回撤">
                {{ (backtestResult.strategyAnalysis?.maxDrawdown * 100)?.toFixed(2) }}%
              </el-descriptions-item>
              <el-descriptions-item label="夏普比率">
                {{ backtestResult.strategyAnalysis?.sharpeRatio?.toFixed(2) }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <h3>风险分析</h3>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="胜率">
                {{ (backtestResult.strategyAnalysis?.winRate * 100)?.toFixed(2) }}%
              </el-descriptions-item>
              <el-descriptions-item label="盈亏比">
                {{ backtestResult.strategyAnalysis?.profitLossRatio?.toFixed(2) }}
              </el-descriptions-item>
              <el-descriptions-item label="连续盈利最大次数">
                {{ backtestResult.strategyAnalysis?.maxConsecutiveWins }}
              </el-descriptions-item>
              <el-descriptions-item label="连续亏损最大次数">
                {{ backtestResult.strategyAnalysis?.maxConsecutiveLosses }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 模型性能对比 -->
      <el-card class="mt-4">
        <template #header>
          <div class="card-header">
            <h3>模型性能对比</h3>
          </div>
        </template>
        <div ref="modelPerformanceChartRef" style="height: 400px;"></div>
      </el-card>

      <!-- 回测记录 -->
      <el-card class="mt-4">
        <template #header>
          <div class="card-header">
            <h3>回测记录</h3>
          </div>
        </template>
        <el-table :data="backtestResult.records" style="width: 100%" border stripe>
          <el-table-column prop="expect" label="期号" width="120" />
          <el-table-column label="预测号码" min-width="200">
            <template #default="{ row }">
              <div class="number-balls">
                <span
                  v-for="num in row.predicted_numbers"
                  :key="num"
                  class="number-ball"
                  :class="{ 'hit': row.actual_result === num }"
                >
                  {{ num }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开奖号码" width="100">
            <template #default="{ row }">
              <span class="number-ball">{{ row.actual_result }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否命中" width="100">
            <template #default="{ row }">
              <el-tag :type="row.hit ? 'success' : 'danger'">
                {{ row.hit ? '命中' : '未中' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="准确度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="Math.round(row.accuracy * 100)"
                :status="getAccuracyStatus(row.accuracy)"
              />
            </template>
          </el-table-column>
          <el-table-column label="模型置信度" width="200">
            <template #default="{ row }">
              <div class="confidence-scores">
                <div v-for="(score, model) in row.confidence_scores" :key="model" class="confidence-item">
                  <span class="model-name">{{ model.toUpperCase() }}:</span>
                  <span class="confidence-value">{{ (score * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>

    <div v-else-if="!loading" class="empty-state">
      <el-empty description="请设置回测参数并点击'开始回测'按钮">
        <template #image>
          <el-icon style="font-size: 60px; color: #909399;">
            <DataAnalysis />
          </el-icon>
        </template>
        <template #description>
          <div>
            <p>请设置回测参数并点击'开始回测'按钮</p>
            <p class="empty-tip">提示：如果没有足够的真实数据，系统将使用模拟数据进行回测演示</p>
          </div>
        </template>
      </el-empty>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="loading-message">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>正在进行回测分析，请稍候...</span>
      </div>
      <el-skeleton :rows="10" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { runBacktest } from '@/api/prediction'
import * as echarts from 'echarts'
import { DataAnalysis, Loading } from '@element-plus/icons-vue'

// 状态变量
const loading = ref(false)
const dateRange = ref(null)
const backtestParams = ref({
  start_date: '',
  end_date: '',
  models: ['lstm', 'rf', 'xgboost'],
  prediction_range: 5,
  strategy: 'basic',
  investment_amount: 100,
  stop_loss: 50,
  take_profit: 200,
  confidence_threshold: 0.6,
  analysis_period: 7
})
const backtestResult = ref(null)

// 图表实例
let modelPerformanceChart = null
let trendChart = null

// 图表DOM引用
const modelPerformanceChartRef = ref(null)
const trendChartRef = ref(null)

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 新增：策略选项
const strategyOptions = [
  { label: '基础策略', value: 'basic' },
  { label: '保守策略', value: 'conservative' },
  { label: '激进策略', value: 'aggressive' },
  { label: '动态调仓策略', value: 'dynamic' }
]

// 处理日期范围变化
function handleDateRangeChange(val) {
  if (val) {
    backtestParams.value.start_date = val[0]
    backtestParams.value.end_date = val[1]
  } else {
    backtestParams.value.start_date = ''
    backtestParams.value.end_date = ''
  }
}

// 开始回测
async function startBacktest() {
  if (!validateParams()) return

  try {
    loading.value = true

    // 显示通知，提示用户回测已开始
    ElNotification({
      title: '回测分析',
      message: '正在进行回测分析，这可能需要一些时间。如果没有足够的真实数据，系统将使用模拟数据进行演示。',
      type: 'info',
      duration: 5000
    })

    const result = await runBacktest(backtestParams.value)
    backtestResult.value = result
    updateModelPerformanceChart()
    updateTrendChart()

    // 显示成功通知，并提供更多信息
    ElNotification({
      title: '回测完成',
      message: `分析了 ${result.totalPredictions} 条数据，命中率 ${((result.hits / result.totalPredictions) * 100).toFixed(2)}%，平均准确度 ${(result.averageAccuracy * 100).toFixed(2)}%`,
      type: 'success',
      duration: 5000
    })
  } catch (error) {
    const errorMessage = error.response?.data?.detail || error.message || '回测失败'
    ElMessage.error(errorMessage)
    console.error('回测错误:', error)

    // 显示错误通知，并提供更多信息
    ElNotification({
      title: '回测失败',
      message: `错误原因: ${errorMessage}\n请尝试选择更大的时间范围或者修改回测参数。`,
      type: 'error',
      duration: 8000
    })
  } finally {
    loading.value = false
  }
}

// 导出结果
function exportResults() {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

// 获取准确度状态
function getAccuracyStatus(accuracy) {
  if (accuracy >= 0.8) return 'success'
  if (accuracy >= 0.5) return 'warning'
  return 'exception'
}

// 初始化图表
function initCharts() {
  if (!modelPerformanceChartRef.value || !trendChartRef.value) return

  modelPerformanceChart = echarts.init(modelPerformanceChartRef.value)
  const option = {
    title: {
      text: '模型性能对比',
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const accuracyData = params.find(p => p.seriesName === '准确率')
        const predictionsData = params.find(p => p.seriesName === '预测次数')
        return `${params[0].name}<br/>
                准确率: ${accuracyData.value}%<br/>
                预测次数: ${predictionsData.value}`
      }
    },
    legend: {
      data: ['准确率', '预测次数'],
      bottom: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '准确率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '预测次数',
        min: 0
      }
    ],
    series: []
  }
  modelPerformanceChart.setOption(option)

  // 初始化趋势图表
  trendChart = echarts.init(trendChartRef.value)
  const trendOption = {
    title: {
      text: '预测准确率趋势',
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['准确率', '收益率'],
      bottom: '5%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      boundaryGap: false
    },
    yAxis: [
      {
        type: 'value',
        name: '准确率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '收益率',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: []
  }
  trendChart.setOption(trendOption)
}

// 更新模型性能图表
function updateModelPerformanceChart() {
  if (!backtestResult.value || !modelPerformanceChart) return

  const modelData = backtestResult.value.modelPerformance
  const models = modelData.map(item => item.model.toUpperCase())
  const accuracy = modelData.map(item => (item.accuracy * 100).toFixed(2))
  const predictions = modelData.map(item => item.total_predictions)

  modelPerformanceChart.setOption({
    xAxis: {
      data: models
    },
    series: [
      {
        name: '准确率',
        type: 'bar',
        data: accuracy,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      },
      {
        name: '预测次数',
        type: 'line',
        yAxisIndex: 1,
        data: predictions,
        label: {
          show: true,
          position: 'top'
        }
      }
    ]
  })
}

// 更新趋势图表
function updateTrendChart() {
  if (!backtestResult.value || !trendChart) return

  const trendData = backtestResult.value.trendAnalysis || []
  const dates = trendData.map(item => item.date)
  const accuracy = trendData.map(item => (item.accuracy * 100).toFixed(2))
  const returns = trendData.map(item => (item.returns * 100).toFixed(2))

  trendChart.setOption({
    xAxis: {
      data: dates
    },
    series: [
      {
        name: '准确率',
        type: 'line',
        data: accuracy,
        smooth: true,
        showSymbol: false,
        areaStyle: {
          opacity: 0.1
        }
      },
      {
        name: '收益率',
        type: 'line',
        yAxisIndex: 1,
        data: returns,
        smooth: true,
        showSymbol: false,
        areaStyle: {
          opacity: 0.1
        }
      }
    ]
  })
}

// 新增：参数验证
function validateParams() {
  if (!backtestParams.value.start_date || !backtestParams.value.end_date) {
    ElMessage.warning('请选择回测时间范围')
    return false
  }

  if (backtestParams.value.models.length === 0) {
    ElMessage.warning('请至少选择一个模型')
    return false
  }

  if (backtestParams.value.stop_loss >= backtestParams.value.take_profit) {
    ElMessage.warning('止损比例必须小于止盈比例')
    return false
  }

  // 检查时间范围是否合理
  const startDate = new Date(backtestParams.value.start_date)
  const endDate = new Date(backtestParams.value.end_date)
  const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    ElMessage.warning('结束日期不能早于开始日期')
    return false
  }

  if (diffDays > 365) {
    ElMessage.warning('回测时间范围不能超过一年')
    return false
  }

  // 如果时间范围小于7天，显示提示但仍然允许继续
  if (diffDays < 7) {
    ElNotification({
      title: '时间范围较小',
      message: '您选择的时间范围小于7天，可能没有足够的数据进行回测。系统将使用模拟数据补充。',
      type: 'warning',
      duration: 5000
    })
  }

  return true
}

// 处理窗口大小变化
function handleResize() {
  modelPerformanceChart?.resize()
  trendChart?.resize()
}

// 生命周期钩子
onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  modelPerformanceChart?.dispose()
  trendChart?.dispose()
})

// 监听分析周期变化
watch(() => backtestParams.value.analysis_period, updateTrendChart)
</script>

<style scoped>
.backtest-page {
  padding: 20px;
}

.parameter-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.parameter-form {
  max-width: 800px;
}

.stat-card {
  height: 160px;
}

.stat-header {
  text-align: center;
  font-size: 16px;
  color: #606266;
}

.stat-content {
  display: flex;
  justify-content: center;
  align-items: baseline;
  margin-top: 20px;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;

  &.positive {
    color: #67C23A;
  }

  &.negative {
    color: #F56C6C;
  }
}

.stat-label {
  margin-left: 5px;
  font-size: 14px;
  color: #909399;
}

.number-balls {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.number-ball {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  font-weight: bold;
  font-size: 14px;

  &.hit {
    background-color: #67C23A;
  }
}

.confidence-scores {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.confidence-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.model-name {
  color: #606266;
}

.confidence-value {
  color: #409EFF;
}

.empty-state {
  margin-top: 20px;
  padding: 40px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.empty-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
}

.loading-state {
  margin-top: 20px;
  padding: 40px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 16px;
  color: #409EFF;
}

.loading-icon {
  margin-right: 10px;
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.mt-4 {
  margin-top: 20px;
}

.el-descriptions {
  margin: 20px 0;
}
</style>