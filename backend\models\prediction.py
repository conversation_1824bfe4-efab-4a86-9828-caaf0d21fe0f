from datetime import datetime
from .database import db


class DrawHistory(db.Model):
    __tablename__ = 'draw_history'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    period = db.Column(db.String(10), unique=True, nullable=False)
    draw_time = db.Column(db.DateTime, nullable=False)
    numbers = db.Column(db.String(50), nullable=False)  # 平码
    special_number = db.Column(db.String(10))  # 特码
    zodiac = db.Column(db.String(50))  # 生肖
    special_zodiac = db.Column(db.String(10))  # 特码生肖
    color = db.Column(db.String(50))  # 波色
    special_color = db.Column(db.String(10))  # 特码波色
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        try:
            main_numbers = [int(n)
                            for n in self.numbers.split(',') if n.strip()]
            special = int(self.special_number) if self.special_number else None

            return {
                'period': self.period,
                'drawTime': self.draw_time.strftime('%Y-%m-%d %H:%M:%S'),
                'numbers': main_numbers,
                'special': special,
                'attributes': {
                    'zodiac': self.zodiac.split(',') if self.zodiac else [],
                    'specialZodiac': self.special_zodiac,
                    'color': self.color.split(',') if self.color else [],
                    'specialColor': self.special_color
                }
            }
        except Exception as e:
            print(f"Error converting record to dict: {e}")
            return None
