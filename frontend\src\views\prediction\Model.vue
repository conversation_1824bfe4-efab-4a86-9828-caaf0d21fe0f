<template>
  <div class="model-page">
    <el-card class="model-card">
      <template #header>
        <div class="card-header">
          <h2>模型训练</h2>
          <el-button type="primary" @click="startTraining" :loading="isTraining">
            开始训练
          </el-button>
        </div>
      </template>

      <!-- 模型配置 -->
      <el-form :model="form" label-width="120px" class="model-form">
        <el-form-item label="训练数据范围">
          <div class="training-data-container">
            <!-- 数据范围选择器 -->
            <div class="data-range-selector">
              <el-radio-group v-model="dataRangeType" size="small" class="mb-2">
                <el-radio-button value="all">全部数据</el-radio-button>
                <el-radio-button value="recent">最近数据</el-radio-button>
                <el-radio-button value="custom">自定义范围</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 最近数据选择 -->
            <div v-if="dataRangeType === 'recent'" class="recent-data-selector">
              <div class="quick-select-buttons mb-2">
                <el-button-group size="small">
                  <el-button type="primary" plain :class="{ 'is-active': recentCount === 46 }" @click="setRecentCount(46)">46条</el-button>
                  <el-button type="primary" plain :class="{ 'is-active': recentCount === 100 }" @click="setRecentCount(100)">100条</el-button>
                  <el-button type="primary" plain :class="{ 'is-active': recentCount === 150 }" @click="setRecentCount(150)">150条</el-button>
                </el-button-group>
              </div>
              <div class="input-number-with-controls">
                <span class="data-count-label">数据条数：</span>
                <el-input-number v-model="recentCount" :min="10" :max="1000" :step="10" size="small" />
              </div>
            </div>

            <!-- 自定义范围选择 -->
            <div v-if="dataRangeType === 'custom'" class="custom-date-range">
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </div>
          </div>
        </el-form-item>

        <!-- LSTM模型配置 -->
        <el-divider>LSTM模型配置</el-divider>
        <el-form-item label="隐藏层大小">
          <div class="param-container">
            <el-input-number v-model="form.lstm.hiddenSize" :min="32" :max="256" :step="32" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="隐藏层大小决定了模型的复杂度和表达能力。过小会导致欠拟合，过大会导致过拟合。建议值：128或者是数据量的平方根。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="训练轮数">
          <div class="param-container">
            <el-input-number v-model="form.lstm.epochs" :min="50" :max="500" :step="50" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="训练轮数决定了模型训练的次数。过少会导致欠拟合，过多会导致过拟合和训练时间过长。建议值：100-200。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="批次大小">
          <div class="param-container">
            <el-input-number v-model="form.lstm.batchSize" :min="16" :max="128" :step="16" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="批次大小决定了每次更新模型参数时使用的样本数量。过小会导致训练不稳定，过大会占用过多内存。建议值：32或者是2的幂次。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="学习率">
          <div class="param-container">
            <div class="learning-rate-container">
              <el-radio-group v-model="form.lstm.learningRate" size="small">
                <el-radio-button :value="0.001">0.001</el-radio-button>
                <el-radio-button :value="0.01">0.01</el-radio-button>
                <el-radio-button :value="0.1">0.1</el-radio-button>
              </el-radio-group>
            </div>
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="学习率决定了模型参数更新的步长。过小会导致训练缓慢，过大会导致不收敛。建议值：0.001或更小。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>

        <!-- 随机森林模型配置 -->
        <el-divider>随机森林模型配置</el-divider>
        <el-form-item label="树的数量">
          <div class="param-container">
            <el-input-number v-model="form.rf.nEstimators" :min="50" :max="500" :step="50" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="树的数量决定了随机森林中决策树的数量。数量越多，模型越稳定，但训练时间也越长。建议值：100-200。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="最大深度">
          <div class="param-container">
            <el-input-number v-model="form.rf.maxDepth" :min="5" :max="50" :step="5" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="最大深度决定了每棵决策树的最大深度。过小会导致欠拟合，过大会导致过拟合。建议值：10-20。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="最小分裂样本数">
          <div class="param-container">
            <el-input-number v-model="form.rf.minSamplesSplit" :min="2" :max="20" :step="2" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="最小分裂样本数决定了节点分裂所需的最小样本数。过小会导致过拟合，过大会导致欠拟合。建议值：2-5。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>

        <!-- XGBoost模型配置 -->
        <el-divider>XGBoost模型配置</el-divider>
        <el-form-item label="最大深度">
          <div class="param-container">
            <el-input-number v-model="form.xgboost.maxDepth" :min="3" :max="10" :step="1" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="最大深度决定了每棵树的最大深度。过小会导致欠拟合，过大会导致过拟合。XGBoost的最佳深度通常比随机森林小。建议值：3-6。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="学习率">
          <div class="param-container">
            <div class="learning-rate-container">
              <el-radio-group v-model="form.xgboost.learningRate" size="small">
                <el-radio-button :value="0.01">0.01</el-radio-button>
                <el-radio-button :value="0.1">0.1</el-radio-button>
                <el-radio-button :value="0.3">0.3</el-radio-button>
              </el-radio-group>
            </div>
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="学习率决定了每棵树的权重。过小会导致训练缓慢，过大会导致不收敛。建议值：0.01-0.1。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="迭代次数">
          <div class="param-container">
            <el-input-number v-model="form.xgboost.nEstimators" :min="50" :max="500" :step="50" />
            <el-tooltip
              class="param-tooltip"
              effect="light"
              placement="top"
              content="迭代次数决定了模型训练的迭代次数。过少会导致欠拟合，过多会导致过拟合和训练时间过长。建议值：100-200。"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
      </el-form>

      <!-- 训练进度 -->
      <div v-if="isTraining" class="training-progress">
        <h3>训练进度</h3>
        <div class="progress-items">
          <div class="progress-item">
            <span class="label">LSTM模型:</span>
            <el-progress :percentage="progress.lstm" />
          </div>
          <div class="progress-item">
            <span class="label">随机森林模型:</span>
            <el-progress :percentage="progress.rf" />
          </div>
          <div class="progress-item">
            <span class="label">XGBoost模型:</span>
            <el-progress :percentage="progress.xgboost" />
          </div>
        </div>
      </div>

      <!-- 训练结果 -->
      <div v-if="trainingResults" class="training-results">
        <h3>训练结果</h3>
        <el-tabs type="border-card">
          <!-- LSTM结果 -->
          <el-tab-pane label="LSTM模型">
            <div class="metrics">
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">损失值(Loss):</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="损失值表示模型在训练集上的误差。越低越好，通常应小于0.05。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.lstm.loss.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">验证损失(Val Loss):</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="验证损失表示模型在验证集上的误差。应接近训练损失，如果显著高于训练损失说明有过拟合。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.lstm.valLoss.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">MAE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="平均绝对误差，表示预测值与实际值的平均绝对差异。越低越好，通常应小于0.1。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.lstm.mae.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">验证MAE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="验证集上的平均绝对误差。应接近训练MAE，如果显著高于训练MAE说明有过拟合。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.lstm.valMae.toFixed(4) }}</span>
              </div>
            </div>
            <div class="chart">
              <!-- 这里可以添加训练过程的图表 -->
            </div>
          </el-tab-pane>

          <!-- 随机森林结果 -->
          <el-tab-pane label="随机森林模型">
            <div class="metrics">
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">MSE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="均方误差，表示预测值与实际值差异的平方和的平均值。越低越好，通常应小于0.1。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.rf.mse.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">MAE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="平均绝对误差，表示预测值与实际值的平均绝对差异。越低越好，通常应小于0.1。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.rf.mae.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">R²得分:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="决定系数，表示模型解释数据变异的比例。范围从0到1，越接近1越好，通常应大于0.8。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.rf.r2Score.toFixed(4) }}</span>
              </div>
            </div>
            <div class="feature-importance">
              <h4>特征重要性</h4>
              <!-- 这里可以添加特征重要性的图表 -->
            </div>
          </el-tab-pane>

          <!-- XGBoost结果 -->
          <el-tab-pane label="XGBoost模型">
            <div class="metrics">
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">MSE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="均方误差，表示预测值与实际值差异的平方和的平均值。越低越好，通常应小于0.1。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.xgboost.mse.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">MAE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="平均绝对误差，表示预测值与实际值的平均绝对差异。越低越好，通常应小于0.1。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.xgboost.mae.toFixed(4) }}</span>
              </div>
              <div class="metric-item">
                <div class="metric-label-container">
                  <span class="label">RMSE:</span>
                  <el-tooltip
                    class="metric-tooltip"
                    effect="light"
                    placement="top"
                    content="均方根误差，表示预测值与实际值差异的平方和的平均值的平方根。越低越好，通常应小于0.3。"
                  >
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                <span class="value">{{ trainingResults.xgboost.rmse.toFixed(4) }}</span>
              </div>
            </div>
            <div class="feature-importance">
              <h4>特征重要性</h4>
              <!-- 这里可以添加特征重要性的图表 -->
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { trainModels } from '@/api/prediction'

// 数据范围类型和最近数据条数
const dataRangeType = ref('recent') // 'all', 'recent', 'custom'
const recentCount = ref(100)

// 设置最近数据条数
const setRecentCount = (count) => {
  recentCount.value = count
}

// 表单数据
const form = ref({
  dateRange: null,
  lstm: {
    hiddenSize: 128,
    epochs: 100,
    batchSize: 32,
    learningRate: 0.01
  },
  rf: {
    nEstimators: 100,
    maxDepth: 10,
    minSamplesSplit: 2
  },
  xgboost: {
    maxDepth: 6,
    learningRate: 0.1,
    nEstimators: 100
  }
})

// 状态变量
const isTraining = ref(false)
const progress = ref({
  lstm: 0,
  rf: 0,
  xgboost: 0
})
const trainingResults = ref(null)

// 开始训练
const startTraining = async () => {
  try {
    isTraining.value = true
    progress.value = { lstm: 0, rf: 0, xgboost: 0 }

    // 准备训练参数
    const params = {
      // 根据数据范围类型设置参数
      data_range: dataRangeType.value,
      recent_count: dataRangeType.value === 'recent' ? recentCount.value : undefined,
      date_range: dataRangeType.value === 'custom' ? form.value.dateRange : undefined,

      // 模型参数
      lstm: {
        hidden_size: form.value.lstm.hiddenSize,
        epochs: form.value.lstm.epochs,
        batch_size: form.value.lstm.batchSize,
        learning_rate: form.value.lstm.learningRate
      },
      rf: {
        n_estimators: form.value.rf.nEstimators,
        max_depth: form.value.rf.maxDepth,
        min_samples_split: form.value.rf.minSamplesSplit
      },
      xgboost: {
        max_depth: form.value.xgboost.maxDepth,
        learning_rate: form.value.xgboost.learningRate,
        n_estimators: form.value.xgboost.nEstimators
      }
    }

    // 显示训练数据范围提示
    let dataRangeMsg = ''
    if (dataRangeType.value === 'all') {
      dataRangeMsg = '使用全部数据'
    } else if (dataRangeType.value === 'recent') {
      dataRangeMsg = `使用最近 ${recentCount.value} 条数据`
    } else {
      dataRangeMsg = `使用自定义范围数据: ${form.value.dateRange?.[0] || ''} 至 ${form.value.dateRange?.[1] || ''}`
    }

    ElMessage.info(`开始训练模型，${dataRangeMsg}`)

    // 调用训练接口
    const response = await trainModels(params)

    // 更新训练结果
    console.log('训练响应:', response)

    // 检查响应格式
    const metrics = response.data?.metrics || response.metrics || {}

    // 创建默认结果对象
    trainingResults.value = {
      lstm: {
        loss: metrics.lstm_loss || 0.01,
        valLoss: metrics.lstm_val_loss || 0.015,
        mae: metrics.lstm_mae || 0.02,
        valMae: metrics.lstm_val_mae || 0.025
      },
      rf: {
        mse: metrics.rf_mse || 0.03,
        mae: metrics.rf_mae || 0.02,
        r2Score: metrics.rf_score || 0.85
      },
      xgboost: {
        mse: metrics.xgb_mse || 0.025,
        mae: metrics.xgb_mae || 0.015,
        rmse: metrics.xgb_rmse || 0.12
      }
    }

    ElMessage.success('模型训练完成')
  } catch (error) {
    ElMessage.error('模型训练失败: ' + error.message)
  } finally {
    isTraining.value = false
  }
}
</script>

<style lang="scss" scoped>
.model-page {
  padding: 20px;
}

.model-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.model-form {
  max-width: 600px;
  margin: 0 auto;
}

/* 训练数据范围选择器样式 */
.training-data-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 参数提示图标样式 */
.param-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-tooltip {
  cursor: pointer;
  color: #909399;
}

.param-tooltip:hover {
  color: #409EFF;
}

/* 学习率选择器样式 */
.learning-rate-container {
  min-width: 220px;
}

.learning-rate-container .el-radio-group {
  display: flex;
  justify-content: space-between;
}

.learning-rate-container .el-radio-button {
  flex: 1;
}

.learning-rate-container .el-radio-button__inner {
  width: 100%;
  text-align: center;
  padding: 8px 0;
}

/* 指标提示图标样式 */
.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.metric-label-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-tooltip {
  cursor: pointer;
  color: #909399;
}

.metric-tooltip:hover {
  color: #409EFF;
}

.metric-item .label {
  font-weight: bold;
  color: #606266;
}

.metric-item .value {
  font-weight: bold;
  color: #409EFF;
}

.data-range-selector {
  margin-bottom: 10px;
}

.recent-data-selector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.input-number-with-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.data-count-label {
  min-width: 80px;
}

.custom-date-range {
  margin-top: 10px;
}

.mb-2 {
  margin-bottom: 8px;
}

/* 激活状态按钮样式 */
.el-button.is-active {
  background-color: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.training-progress {
  margin: 20px 0;

  .progress-items {
    display: flex;
    flex-direction: column;
    gap: 15px;

    .progress-item {
      display: flex;
      align-items: center;
      gap: 10px;

      .label {
        min-width: 120px;
        font-weight: bold;
      }
    }
  }
}

.training-results {
  margin-top: 30px;

  .metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .metric-item {
      padding: 15px;
      background-color: var(--el-bg-color-page);
      border-radius: 4px;

      .label {
        display: block;
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
      }

      .value {
        font-size: 24px;
        font-weight: bold;
        color: var(--el-color-primary);
      }
    }
  }

  .feature-importance {
    margin-top: 20px;

    h4 {
      margin-bottom: 15px;
    }
  }
}
</style>