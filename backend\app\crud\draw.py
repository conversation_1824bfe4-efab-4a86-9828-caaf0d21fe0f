from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Dict, List, Optional
from datetime import datetime
import pandas as pd
import os
from fastapi.responses import FileResponse

from app.models.draw import Draw
from app.rules import GameRules2025


def get_latest_draw(db: Session) -> Optional[Draw]:
    """Get the latest draw."""
    return db.query(Draw).order_by(Draw.expect.desc()).first()


def get_history_draws(db: Session, skip: int = 0, limit: int = 20, filters: Dict = None) -> List[Draw]:
    """Get history draws with pagination and filters."""
    query = db.query(Draw)
    query = apply_filters(query, filters)
    return query.order_by(Draw.expect.desc()).offset(skip).limit(limit).all()


def count_history_draws(db: Session, filters: Dict = None) -> int:
    """Count total number of draws matching the filters."""
    query = db.query(Draw)
    query = apply_filters(query, filters)
    return query.count()


def get_filtered_draws(db: Session, filters: Dict = None) -> List[Draw]:
    """Get all draws matching the filters."""
    query = db.query(Draw)
    query = apply_filters(query, filters)
    return query.order_by(Draw.expect.desc()).all()


def apply_filters(query, filters: Dict = None):
    """Apply filters to query."""
    if not filters:
        return query

    filter_conditions = []

    if filters.get("start_period"):
        filter_conditions.append(Draw.expect >= filters["start_period"])
    if filters.get("end_period"):
        filter_conditions.append(Draw.expect <= filters["end_period"])
    if filters.get("start_date"):
        filter_conditions.append(Draw.open_time >= filters["start_date"])
    if filters.get("end_date"):
        filter_conditions.append(Draw.open_time <= filters["end_date"])
    if filters.get("number"):
        filter_conditions.append(Draw.special_number == filters["number"])

    if filters.get("zodiac"):
        zodiac_numbers = GameRules2025.ZODIAC_NUMBERS.get(
            filters["zodiac"], [])
        filter_conditions.append(Draw.special_number.in_(zodiac_numbers))

    if filters.get("color"):
        color_map = {
            "红波": GameRules2025.RED_NUMBERS,
            "蓝波": GameRules2025.BLUE_NUMBERS,
            "绿波": GameRules2025.GREEN_NUMBERS
        }
        color_numbers = color_map.get(filters["color"], [])
        filter_conditions.append(Draw.special_number.in_(color_numbers))

    if filters.get("odd_even"):
        is_odd = filters["odd_even"] == "单"
        filter_conditions.append(Draw.special_number %
                                 2 == (1 if is_odd else 0))

    if filters.get("big_small"):
        is_big = filters["big_small"] == "大"
        filter_conditions.append(
            Draw.special_number >= 25 if is_big else Draw.special_number < 25)

    if filters.get("tail_big_small"):
        is_tail_big = filters["tail_big_small"] == "尾大"
        filter_conditions.append((Draw.special_number % 10) >= 5 if is_tail_big else (
            Draw.special_number % 10) < 5)

    if filters.get("sum_odd_even"):
        def sum_is_odd(number):
            return (number // 10 + number % 10) % 2 == 1
        is_sum_odd = filters["sum_odd_even"] == "合单"
        numbers_with_sum_odd = [n for n in range(
            1, 50) if sum_is_odd(n) == is_sum_odd]
        filter_conditions.append(Draw.special_number.in_(numbers_with_sum_odd))

    if filters.get("animal_type"):
        is_domestic = filters["animal_type"] == "家禽"
        domestic_zodiac_numbers = []
        for zodiac in (GameRules2025.DOMESTIC_ANIMALS if is_domestic else GameRules2025.WILD_ANIMALS):
            domestic_zodiac_numbers.extend(
                GameRules2025.ZODIAC_NUMBERS[zodiac])
        filter_conditions.append(
            Draw.special_number.in_(domestic_zodiac_numbers))

    if filters.get("wuxing"):
        wuxing_numbers = GameRules2025.WUXING_NUMBERS.get(
            filters["wuxing"], [])
        filter_conditions.append(Draw.special_number.in_(wuxing_numbers))

    if filter_conditions:
        query = query.filter(and_(*filter_conditions))

    return query


def export_draws(db: Session, filters: Dict = None) -> FileResponse:
    """Export draws to Excel file."""
    draws = get_filtered_draws(db, filters)

    data = []
    for draw in draws:
        special_number = int(draw.open_code.split(',')[-1])
        attributes = GameRules2025.get_number_attributes(special_number)

        numbers = [int(n) for n in draw.open_code.split(',')]
        odd_count = sum(1 for n in numbers if n % 2 != 0)
        big_count = sum(1 for n in numbers if n >= 25)

        data.append({
            "期号": draw.expect,
            "开奖时间": draw.open_time.strftime("%Y-%m-%d %H:%M:%S"),
            "开奖号码": draw.open_code,
            "特码": special_number,
            "特码生肖": attributes["zodiac"],
            "特码波色": attributes["color"],
            "特码单双": "单" if special_number % 2 else "双",
            "特码大小": "大" if special_number >= 25 else "小",
            "特码尾数": attributes["tail_big_small"],
            "特码合数": attributes["sum_odd_even"],
            "特码属性": attributes["animal_type"],
            "特码五行": attributes["wuxing"],
            "总单双比": f"{odd_count}:{len(numbers)-odd_count}",
            "总大小比": f"{big_count}:{len(numbers)-big_count}",
        })

    df = pd.DataFrame(data)

    filename = f"lottery_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    filepath = f"temp/{filename}"
    os.makedirs("temp", exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    df.to_excel(writer, index=False, sheet_name='开奖记录')

    worksheet = writer.sheets['开奖记录']
    for idx, col in enumerate(df.columns):
        max_length = max(df[col].astype(str).apply(len).max(), len(col))
        worksheet.column_dimensions[chr(65 + idx)].width = max_length + 2

    writer.close()

    return FileResponse(
        filepath,
        filename=filename,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
