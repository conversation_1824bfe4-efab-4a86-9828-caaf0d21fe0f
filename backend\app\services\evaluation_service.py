from sqlalchemy.orm import Session
from typing import Dict, List, Optional
import numpy as np
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    roc_auc_score,
    mean_squared_error,
    mean_absolute_error,
    r2_score
)
from app.models.draw import Draw
from app.models.prediction import Prediction
from app.core.cache import cache, CacheManager
from datetime import datetime, timedelta


class ModelEvaluator:
    """模型评估器"""

    @staticmethod
    def calculate_basic_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """计算基础评估指标"""
        return {
            "accuracy": accuracy_score(y_true, y_pred),
            "precision": precision_score(y_true, y_pred, average='weighted'),
            "recall": recall_score(y_true, y_pred, average='weighted'),
            "f1": f1_score(y_true, y_pred, average='weighted')
        }

    @staticmethod
    def calculate_regression_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """计算回归评估指标"""
        return {
            "mse": mean_squared_error(y_true, y_pred),
            "rmse": np.sqrt(mean_squared_error(y_true, y_pred)),
            "mae": mean_absolute_error(y_true, y_pred),
            "r2": r2_score(y_true, y_pred)
        }

    @staticmethod
    def calculate_confusion_matrix(y_true: np.ndarray, y_pred: np.ndarray) -> np.ndarray:
        """计算混淆矩阵"""
        return confusion_matrix(y_true, y_pred)

    @staticmethod
    def calculate_roc_auc(y_true: np.ndarray, y_pred_proba: np.ndarray) -> float:
        """计算ROC AUC分数"""
        return roc_auc_score(y_true, y_pred_proba, multi_class='ovr')


@cache(expire=3600, prefix="model_performance")
async def evaluate_model_performance(db: Session, days: int = 30) -> Dict:
    """评估模型性能"""
    try:
        # 获取指定时间范围内的预测记录
        start_date = datetime.now() - timedelta(days=days)
        predictions = db.query(Prediction).filter(
            Prediction.prediction_time >= start_date
        ).all()

        if not predictions:
            return {"error": "没有找到预测记录"}

        # 准备评估数据
        y_true = []
        y_pred = []
        y_pred_proba = []

        for pred in predictions:
            # 获取实际开奖结果
            draw = db.query(Draw).filter(Draw.expect == pred.expect).first()
            if draw:
                y_true.append(draw.special_number)
                y_pred.append(pred.predicted_numbers[0])  # 取预测概率最高的号码
                y_pred_proba.append(pred.confidence)

        if not y_true:
            return {"error": "没有找到匹配的开奖记录"}

        # 转换为numpy数组
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        y_pred_proba = np.array(y_pred_proba)

        # 计算各项指标
        basic_metrics = ModelEvaluator.calculate_basic_metrics(y_true, y_pred)
        regression_metrics = ModelEvaluator.calculate_regression_metrics(
            y_true, y_pred)
        confusion_mat = ModelEvaluator.calculate_confusion_matrix(
            y_true, y_pred)
        roc_auc = ModelEvaluator.calculate_roc_auc(y_true, y_pred_proba)

        # 计算预测准确率趋势
        accuracy_trend = []
        for i in range(1, len(predictions) + 1):
            recent_preds = predictions[:i]
            correct = sum(1 for p in recent_preds if p.is_correct == 1)
            accuracy_trend.append(correct / i)

        return {
            "basic_metrics": basic_metrics,
            "regression_metrics": regression_metrics,
            "confusion_matrix": confusion_mat.tolist(),
            "roc_auc": roc_auc,
            "accuracy_trend": accuracy_trend,
            "total_predictions": len(predictions),
            "evaluation_period": f"最近{days}天"
        }
    except Exception as e:
        print(f"评估模型性能时出错: {str(e)}")
        return {"error": str(e)}


@cache(expire=3600, prefix="number_distribution")
async def analyze_number_distribution(db: Session, days: int = 30) -> Dict:
    """分析号码分布"""
    try:
        # 获取指定时间范围内的开奖记录
        start_date = datetime.now() - timedelta(days=days)
        draws = db.query(Draw).filter(
            Draw.draw_time >= start_date
        ).all()

        if not draws:
            return {"error": "没有找到开奖记录"}

        # 统计号码出现频率
        number_freq = {}
        for draw in draws:
            if draw.numbers:
                for num in draw.numbers:
                    number_freq[num] = number_freq.get(num, 0) + 1

        # 计算统计指标
        numbers = list(number_freq.keys())
        frequencies = list(number_freq.values())

        return {
            "number_frequency": number_freq,
            "mean_frequency": np.mean(frequencies),
            "std_frequency": np.std(frequencies),
            "max_frequency": max(frequencies),
            "min_frequency": min(frequencies),
            "hot_numbers": sorted(number_freq.items(), key=lambda x: x[1], reverse=True)[:5],
            "cold_numbers": sorted(number_freq.items(), key=lambda x: x[1])[:5]
        }
    except Exception as e:
        print(f"分析号码分布时出错: {str(e)}")
        return {"error": str(e)}


@cache(expire=3600, prefix="prediction_analysis")
async def analyze_predictions(db: Session, days: int = 30) -> Dict:
    """分析预测结果"""
    try:
        # 获取指定时间范围内的预测记录
        start_date = datetime.now() - timedelta(days=days)
        predictions = db.query(Prediction).filter(
            Prediction.prediction_time >= start_date
        ).all()

        if not predictions:
            return {"error": "没有找到预测记录"}

        # 分析预测结果
        analysis = {
            "total_predictions": len(predictions),
            "correct_predictions": sum(1 for p in predictions if p.is_correct == 1),
            "average_confidence": np.mean([p.confidence for p in predictions]),
            "confidence_distribution": {
                "high": sum(1 for p in predictions if p.confidence >= 0.8),
                "medium": sum(1 for p in predictions if 0.5 <= p.confidence < 0.8),
                "low": sum(1 for p in predictions if p.confidence < 0.5)
            },
            "prediction_trend": []
        }

        # 计算预测趋势
        for i in range(1, len(predictions) + 1):
            recent_preds = predictions[:i]
            correct = sum(1 for p in recent_preds if p.is_correct == 1)
            analysis["prediction_trend"].append({
                "day": i,
                "accuracy": correct / i,
                "confidence": np.mean([p.confidence for p in recent_preds])
            })

        return analysis
    except Exception as e:
        print(f"分析预测结果时出错: {str(e)}")
        return {"error": str(e)}


async def get_evaluation_summary(db: Session) -> Dict:
    """获取评估总结"""
    try:
        # 获取各项评估指标
        performance = await evaluate_model_performance(db)
        distribution = await analyze_number_distribution(db)
        predictions = await analyze_predictions(db)

        # 生成评估总结
        summary = {
            "model_performance": performance,
            "number_distribution": distribution,
            "prediction_analysis": predictions,
            "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "recommendations": []
        }

        # 添加建议
        if "basic_metrics" in performance:
            accuracy = performance["basic_metrics"]["accuracy"]
            if accuracy < 0.3:
                summary["recommendations"].append("模型准确率较低，建议重新训练模型")
            elif accuracy < 0.5:
                summary["recommendations"].append("模型准确率一般，建议优化特征工程")

        if "regression_metrics" in performance:
            r2 = performance["regression_metrics"]["r2"]
            if r2 < 0.3:
                summary["recommendations"].append("模型拟合度较低，建议调整模型参数")

        return summary
    except Exception as e:
        print(f"生成评估总结时出错: {str(e)}")
        return {"error": str(e)}
