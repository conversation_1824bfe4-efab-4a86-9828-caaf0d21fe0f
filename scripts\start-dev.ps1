# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== 启动开发环境 ===" -ForegroundColor Green

# 检查必要的目录
$directories = @(
    "data",
    "data/db",
    "data/exports",
    "logs",
    "models"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "创建目录: $dir" -ForegroundColor Yellow
    }
}

# 启动后端服务
Write-Host "`n启动后端服务..." -ForegroundColor Cyan
Set-Location backend

# 创建并激活虚拟环境
if (-not (Test-Path "venv")) {
    python -m venv venv
}
& "./venv/Scripts/Activate.ps1"

# 安装依赖（使用清华源加速）
python -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --upgrade pip
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

$backendJob = Start-Job -ScriptBlock {
    Set-Location backend
    & "./venv/Scripts/python.exe" app.py
}

# 启动前端服务
Write-Host "`n启动前端服务..." -ForegroundColor Cyan
Set-Location ../frontend
npm install
$frontendJob = Start-Job -ScriptBlock {
    Set-Location frontend
    npm run dev
}

Set-Location ..

# 监控任务状态
try {
    Write-Host "`n=== 开发环境启动中 ===" -ForegroundColor Green
    Receive-Job -Job $backendJob, $frontendJob -Wait
}
finally {
    Stop-Job -Job $backendJob, $frontendJob
    Remove-Job -Job $backendJob, $frontendJob
    if (Test-Path "backend/venv") {
        & "backend/venv/Scripts/deactivate.bat"
    }
} 