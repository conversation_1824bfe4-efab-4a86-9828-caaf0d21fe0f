# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 检查并退出可能已经激活的虚拟环境
if ($env:VIRTUAL_ENV) {
    Write-Host "检测到已激活的虚拟环境，正在退出..." -ForegroundColor Yellow
    deactivate
}

# 激活 conda 环境
Write-Host "激活 conda 环境 'prediction'..." -ForegroundColor Yellow
conda activate prediction

# 检查是否成功激活conda环境
if ($env:CONDA_DEFAULT_ENV -ne "prediction") {
    Write-Host "Error: 无法激活 conda 环境 'prediction'。请确保该环境已创建。" -ForegroundColor Red
    Write-Host "可以使用以下命令创建环境：conda create -n prediction python=3.8" -ForegroundColor Yellow
    exit 1
}

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
pip install -r requirements.txt

# 删除旧的数据库文件
Write-Host "清理旧数据..." -ForegroundColor Yellow
Remove-Item -Path "data/lottery.db" -ErrorAction SilentlyContinue

# 启动服务器
Write-Host "启动服务器..." -ForegroundColor Green
python app.py 