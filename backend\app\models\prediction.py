from sqlalchemy import Column, Integer, String, DateTime, Float, JSON, Foreign<PERSON>ey, Date, Boolean
from sqlalchemy.orm import relationship
from ..database import Base
from datetime import datetime
from app.db.base import BaseModel


class Prediction(BaseModel):
    """预测记录模型"""
    __tablename__ = "predictions"

    expect = Column(String, unique=True, index=True)  # 预测期号
    predicted_numbers = Column(JSON)  # 预测号码
    confidence = Column(Float)  # 预测置信度
    model_version = Column(String)  # 模型版本
    prediction_time = Column(DateTime)  # 预测时间
    is_correct = Column(Integer, default=0)  # 预测是否正确


class ModelTrainingHistory(Base):
    """模型训练历史"""
    __tablename__ = "model_training_history"

    id = Column(Integer, primary_key=True, index=True)
    training_time = Column(DateTime, default=datetime.now)  # 训练时间
    model_name = Column(String)  # 模型名称
    parameters = Column(JSON)  # 训练参数
    metrics = Column(JSON)  # 训练指标
    status = Column(String)  # 训练状态
    data_range = Column(String)  # 数据范围类型 (all, recent, custom)
    data_count = Column(Integer)  # 实际使用的数据条数
    period_range = Column(JSON)  # 期号范围信息
    custom_range = Column(JSON, nullable=True)  # 自定义范围设置
    recent_count = Column(Integer, nullable=True)  # 最近数据条数

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "training_time": self.training_time.isoformat() if self.training_time else None,
            "model_name": self.model_name,
            "parameters": self.parameters,
            "metrics": self.metrics,
            "status": self.status,
            "data_range": self.data_range,
            "data_count": self.data_count,
            "period_range": self.period_range,
            "custom_range": self.custom_range,
            "recent_count": self.recent_count
        }


class DrawResult(Base):
    """开奖结果模型"""
    __tablename__ = "draw_results"

    id = Column(Integer, primary_key=True, index=True)
    expect = Column(String, unique=True, index=True)  # 期号
    draw_time = Column(DateTime, index=True, default=datetime.now)  # 开奖时间
    numbers = Column(JSON)  # 开奖号码
    special_number = Column(Integer)  # 特码
    zodiac_list = Column(JSON)  # 生肖列表
    color_list = Column(JSON)  # 波色列表
    source = Column(String)  # 数据来源
    raw_data = Column(String)  # 原始数据


class BacktestResult(Base):
    __tablename__ = "backtest_results"

    id = Column(Integer, primary_key=True, index=True)
    start_date = Column(Date)
    end_date = Column(Date)
    execution_time = Column(DateTime, default=datetime.now)
    total_predictions = Column(Integer)
    hits = Column(Integer)
    average_accuracy = Column(Float)
    roi = Column(Float)
    model_performance = Column(JSON)
    prediction_range = Column(Integer)


class BacktestRecord(Base):
    __tablename__ = "backtest_records"

    id = Column(Integer, primary_key=True, index=True)
    backtest_id = Column(Integer, ForeignKey("backtest_results.id"))
    expect = Column(String)
    prediction_time = Column(DateTime)
    predicted_numbers = Column(JSON)
    actual_number = Column(Integer)
    hit = Column(Boolean)
    profit = Column(Float)
    model = Column(String)
    confidence = Column(Float)

    backtest = relationship("BacktestResult", back_populates="records")


# Add the reverse relationship
BacktestResult.records = relationship(
    "BacktestRecord", back_populates="backtest")
