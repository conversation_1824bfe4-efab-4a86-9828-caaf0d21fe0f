from collections import defaultdict
from typing import List
from ..models.draw import Draw
from .game_rules import GameRules2025


def calculate_statistics(draws: List[Draw]) -> dict:
    """计算统计数据"""
    try:
        if not draws:
            return {
                "basicStats": {
                    "totalCount": 0,
                    "hotNumbers": [],
                    "coldNumbers": [],
                    "hotNumber": None,
                    "hotNumberCount": 0,
                    "coldNumber": None,
                    "coldNumberCount": 0,
                    "averageInterval": 0
                },
                "numberFrequency": {str(i): 0 for i in range(1, 50)},
                "colorFrequency": {'红波': 0, '蓝波': 0, '绿波': 0},
                "tailFrequency": {str(i): 0 for i in range(10)},
                "headFrequency": {str(i): 0 for i in range(5)},
                "zodiacFrequency": {zodiac: 0 for zodiac in GameRules2025.ZODIAC_LIST},
                "wuxingFrequency": {wuxing: 0 for wuxing in GameRules2025.WUXING_LIST},
                "attributes": {
                    "单": 0, "双": 0, "大": 0, "小": 0,
                    "家禽": 0, "野兽": 0,
                    "尾单": 0, "尾双": 0,
                    "尾大": 0, "尾小": 0,
                    "合单": 0, "合双": 0,
                    "合大": 0, "合小": 0
                },
                "missing": {
                    "current": {str(i): 0 for i in range(1, 50)},
                    "max": {str(i): 0 for i in range(1, 50)},
                    "lastAppearance": {str(i): None for i in range(1, 50)}
                }
            }

        # 初始化统计数据
        number_frequency = defaultdict(int)  # 号码频率
        color_frequency = defaultdict(int)   # 波色频率
        tail_frequency = defaultdict(int)    # 尾数频率
        head_frequency = defaultdict(int)    # 头数频率
        zodiac_frequency = defaultdict(int)  # 生肖频率
        wuxing_frequency = defaultdict(int)  # 五行频率
        attributes_frequency = {             # 特码属性频率
            "单": 0, "双": 0, "大": 0, "小": 0,
            "家禽": 0, "野兽": 0,
            "尾单": 0, "尾双": 0,
            "尾大": 0, "尾小": 0,
            "合单": 0, "合双": 0,
            "合大": 0, "合小": 0
        }

        # 按期号排序
        sorted_draws = sorted(draws, key=lambda x: x.expect)

        # 计算每个号码的当前遗漏、最大遗漏和最后出现期
        last_appearance = {str(i): None for i in range(1, 50)}
        current_missing = {str(i): 0 for i in range(1, 50)}
        max_missing = {str(i): 0 for i in range(1, 50)}
        temp_missing = {str(i): 0 for i in range(1, 50)}

        # 遍历开奖记录进行统计
        for draw in sorted_draws:
            try:
                if not draw.open_code:
                    continue

                # 解析开奖号码
                numbers = [int(n.strip()) for n in draw.open_code.split(
                    ',') if n.strip().isdigit()]
                if not numbers:
                    continue

                special_number = numbers[-1]  # 特码
                if special_number < 1 or special_number > 49:
                    continue

                # 更新特码频率
                number_frequency[str(special_number)] += 1

                # 获取号码属性
                attributes = GameRules2025.get_number_attributes(
                    special_number)

                # 更新波色频率
                if attributes['color']:
                    color_frequency[f"{attributes['color']}波"] += 1

                # 更新尾数频率
                if attributes['tail'] >= 0:
                    tail_frequency[str(attributes['tail'])] += 1

                # 更新头数频率
                if attributes['head'] >= 0:
                    head_frequency[str(attributes['head'])] += 1

                # 更新生肖频率
                if attributes['zodiac']:
                    zodiac_frequency[attributes['zodiac']] += 1

                # 更新五行频率
                if attributes['wuxing']:
                    wuxing_frequency[attributes['wuxing']] += 1

                # 更新单双属性
                if attributes['is_odd']:
                    attributes_frequency['单'] += 1
                else:
                    attributes_frequency['双'] += 1

                # 更新大小属性
                if attributes['is_big']:
                    attributes_frequency['大'] += 1
                else:
                    attributes_frequency['小'] += 1

                # 更新家禽野兽属性
                if attributes['is_domestic']:
                    attributes_frequency['家禽'] += 1
                else:
                    attributes_frequency['野兽'] += 1

                # 更新尾数单双属性
                tail = attributes['tail']
                if tail % 2 == 1:
                    attributes_frequency['尾单'] += 1
                else:
                    attributes_frequency['尾双'] += 1

                # 更新尾数大小属性
                if attributes['is_tail_big']:
                    attributes_frequency['尾大'] += 1
                else:
                    attributes_frequency['尾小'] += 1

                # 更新合数单双属性
                if attributes['is_sum_odd']:
                    attributes_frequency['合单'] += 1
                else:
                    attributes_frequency['合双'] += 1

                # 更新合数大小属性
                if attributes['is_sum_big']:
                    attributes_frequency['合大'] += 1
                else:
                    attributes_frequency['合小'] += 1

                # 处理遗漏值
                for i in range(1, 50):
                    key = str(i)
                    if i == special_number:
                        # 如果当前号码是特码，记录最后出现期数
                        last_appearance[key] = draw.expect
                        # 检查是否需要更新最大遗漏值
                        if temp_missing[key] > max_missing[key]:
                            max_missing[key] = temp_missing[key]
                        # 重置临时遗漏值
                        temp_missing[key] = 0
                    else:
                        # 如果当前号码不是特码，增加临时遗漏值
                        temp_missing[key] += 1

            except Exception as e:
                print(f"Error processing draw {draw.expect}: {str(e)}")
                continue

        # 处理最终的遗漏值
        for i in range(1, 50):
            key = str(i)
            # 更新当前遗漏值
            current_missing[key] = temp_missing[key]
            # 检查是否需要更新最大遗漏值
            if temp_missing[key] > max_missing[key]:
                max_missing[key] = temp_missing[key]

        # 确保所有字典的键都是字符串，并包含所有1-49的号码
        number_frequency_str = {str(i): number_frequency.get(
            str(i), 0) for i in range(1, 50)}
        tail_frequency_str = {str(k): v for k, v in tail_frequency.items()}
        head_frequency_str = {str(k): v for k, v in head_frequency.items()}

        # 确保波色字典包含所有波色
        color_frequency_str = {
            '红波': color_frequency.get('红波', 0),
            '蓝波': color_frequency.get('蓝波', 0),
            '绿波': color_frequency.get('绿波', 0)
        }

        # 确保生肖字典包含所有生肖
        zodiac_frequency_str = {}
        for zodiac in GameRules2025.ZODIAC_LIST:
            zodiac_frequency_str[zodiac] = zodiac_frequency.get(zodiac, 0)

        # 确保五行字典包含所有五行
        wuxing_frequency_str = {}
        for wuxing in GameRules2025.WUXING_LIST:
            wuxing_frequency_str[wuxing] = wuxing_frequency.get(wuxing, 0)

        attributes_frequency_str = {
            str(k): v for k, v in attributes_frequency.items()}

        # 获取第一期和最后一期的信息
        first_draw = sorted_draws[0] if sorted_draws else None
        last_draw = sorted_draws[-1] if sorted_draws else None

        # 计算期号范围和日期范围
        period_range = f"{first_draw.expect}-{last_draw.expect}" if first_draw and last_draw else ""
        date_range = f"{first_draw.draw_time.strftime('%Y-%m-%d')}-{last_draw.draw_time.strftime('%Y-%m-%d')}" if first_draw and last_draw else ""

        # 计算热门号码和冷门号码（修复字段名为count）
        hot_numbers_all = sorted(
            [{"number": int(k), "count": v, "frequency": v, "percentage": (v / len(sorted_draws)) * 100 if sorted_draws else 0}
                for k, v in number_frequency_str.items()],
            key=lambda x: (-x["count"], x["number"])
        )

        cold_numbers_all = sorted(
            [{"number": int(k), "count": v, "frequency": v, "percentage": (v / len(sorted_draws)) * 100 if sorted_draws else 0}
                for k, v in number_frequency_str.items()],
            key=lambda x: (x["count"], x["number"])
        )

        # 获取最小出现次数
        min_count = cold_numbers_all[0]["count"] if cold_numbers_all else 0

        # 获取所有具有最小出现次数的号码
        cold_numbers = [
            num for num in cold_numbers_all if num["count"] == min_count]

        # 调试信息
        print(f"DEBUG: 最小出现次数: {min_count}")
        print(f"DEBUG: 相同最小次数的号码数量: {len(cold_numbers)}")
        debug_list = [
            f"{n['number']:02d}({n['count']})" for n in cold_numbers[:5]]
        print(f"DEBUG: 前5个冷门号码: {debug_list}")

        # 如果冷门号码太多，限制为前10个
        if len(cold_numbers) > 10:
            cold_numbers = cold_numbers[:10]

        # 获取热门号码（前10个）
        hot_numbers = hot_numbers_all[:10]

        # 构建返回结果
        missing = {
            "current": current_missing,
            "max": max_missing,
            "lastAppearance": last_appearance
        }

        return {
            "basicStats": {
                "totalCount": len(sorted_draws),
                "hotNumbers": hot_numbers[:5],
                "coldNumbers": cold_numbers[:5],
                "hotNumber": sorted(number_frequency_str.items(), key=lambda x: (-x[1], int(x[0])))[0][0] if number_frequency_str else None,
                "hotNumberCount": max(number_frequency_str.values()) if number_frequency_str else 0,
                "coldNumber": sorted(number_frequency_str.items(), key=lambda x: (x[1], int(x[0])))[0][0] if number_frequency_str else None,
                "coldNumberCount": min(number_frequency_str.values()) if number_frequency_str else 0,
                "averageInterval": 0  # 暂时不计算平均间隔
            },
            "numberFrequency": number_frequency_str,
            "colorFrequency": color_frequency_str,
            "tailFrequency": tail_frequency_str,
            "headFrequency": head_frequency_str,
            "zodiacFrequency": zodiac_frequency_str,
            "wuxingFrequency": wuxing_frequency_str,
            "attributes": attributes_frequency_str,
            "missing": missing,
            "total_draws": len(sorted_draws),
            "period_range": period_range,
            "date_range": date_range,
            "hot_numbers": hot_numbers,
            "cold_numbers": cold_numbers
        }

    except Exception as e:
        print(f"Error calculating statistics: {str(e)}")
        raise
