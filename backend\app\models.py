from sqlalchemy import Column, Integer, String, DateTime, Text, Float
from sqlalchemy.types import TypeDecorator
import json
from datetime import datetime
from sqlalchemy.sql import func
from .database import Base
from .utils.game_rules import GameRules2025


class JSONString(TypeDecorator):
    impl = Text

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return None

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return None


class Draw(Base):
    __tablename__ = "draws"

    id = Column(Integer, primary_key=True, index=True)
    expect = Column(String, unique=True, index=True)
    numbers = Column(Text)  # 存储所有号码的JSON字符串
    draw_time = Column(DateTime)  # 开奖时间
    open_code = Column(Text)  # 开奖号码字符串
    special_number = Column(Integer)  # 特码
    zodiac = Column(Text)  # 生肖
    color = Column(Text)  # 波色
    odd_even = Column(Text)  # 单双
    big_small = Column(Text)  # 大小
    tail_big_small = Column(Text)  # 尾大小
    sum_odd_even = Column(Text)  # 合单双
    animal_type = Column(Text)  # 动物类型
    wuxing = Column(Text)  # 五行
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def calculate_attributes(self):
        """计算并更新开奖号码的所有属性"""
        if not self.open_code:
            return

        try:
            # 解析开奖号码
            numbers = [int(n.strip()) for n in self.open_code.split(',')]
            if not numbers:
                return

            # 获取特码（最后一个号码）
            special_number = numbers[-1]

            # 获取特码的属性
            attributes = GameRules2025.get_number_attributes(special_number)

            # 更新特码属性
            self.special_number = special_number
            self.zodiac = attributes['zodiac']
            self.color = attributes['color']
            self.odd_even = '单' if attributes['is_odd'] else '双'
            self.big_small = '大' if attributes['is_big'] else '小'
            self.tail_big_small = '尾大' if attributes['tail'] >= 5 else '尾小'
            self.sum_odd_even = '合单' if (
                attributes['head'] + attributes['tail']) % 2 != 0 else '合双'
            self.wuxing = attributes['wuxing']

        except Exception as e:
            print(
                f"Error calculating attributes for draw {self.expect}: {str(e)}")
            raise

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "expect": self.expect,
            "numbers": self.numbers,
            "draw_time": self.draw_time.isoformat() if self.draw_time else None,
            "open_code": self.open_code,
            "special_number": self.special_number,
            "zodiac": self.zodiac,
            "color": self.color,
            "odd_even": self.odd_even,
            "big_small": self.big_small,
            "tail_big_small": self.tail_big_small,
            "sum_odd_even": self.sum_odd_even,
            "animal_type": self.animal_type,
            "wuxing": self.wuxing,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class Prediction(Base):
    __tablename__ = "predictions"

    id = Column(Integer, primary_key=True, index=True)
    expect = Column(String, index=True)  # 预测期号
    prediction_time = Column(DateTime(timezone=True),
                             server_default=func.now())
    special_numbers_5 = Column(JSONString)  # 5个特码预测
    special_numbers_10 = Column(JSONString)  # 10个特码预测
    special_numbers_15 = Column(JSONString)  # 15个特码预测
    special_numbers_20 = Column(JSONString)  # 20个特码预测
    special_numbers_30 = Column(JSONString)  # 30个特码预测
    zodiac_3 = Column(JSONString)  # 3个生肖预测
    zodiac_5 = Column(JSONString)  # 5个生肖预测
    zodiac_7 = Column(JSONString)  # 7个生肖预测
    attribute_predictions = Column(JSONString)  # 属性预测（单双、大小、波色等）
    strategy = Column(Text)  # 竞猜策略
    confidence_scores = Column(JSONString)  # 各模型置信度
    actual_result = Column(String, nullable=True)  # 实际开奖结果
    accuracy = Column(Float, nullable=True)  # 预测准确度
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "expect": self.expect,
            "prediction_time": self.prediction_time.isoformat() if self.prediction_time else None,
            "special_numbers_5": self.special_numbers_5,
            "special_numbers_10": self.special_numbers_10,
            "special_numbers_15": self.special_numbers_15,
            "special_numbers_20": self.special_numbers_20,
            "special_numbers_30": self.special_numbers_30,
            "zodiac_3": self.zodiac_3,
            "zodiac_5": self.zodiac_5,
            "zodiac_7": self.zodiac_7,
            "attribute_predictions": self.attribute_predictions,
            "strategy": self.strategy,
            "confidence_scores": self.confidence_scores,
            "actual_result": self.actual_result,
            "accuracy": self.accuracy,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class ModelTrainingHistory(Base):
    __tablename__ = "model_training_history"

    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String)  # 模型名称（LSTM/RF/XGBoost等）
    training_time = Column(DateTime(timezone=True), server_default=func.now())
    parameters = Column(JSONString)  # 训练参数
    metrics = Column(JSONString)  # 训练指标（准确率、损失等）
    training_data_range = Column(JSONString)  # 训练数据范围
    validation_score = Column(Float)  # 验证集得分
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "model_name": self.model_name,
            "training_time": self.training_time.isoformat() if self.training_time else None,
            "parameters": self.parameters,
            "metrics": self.metrics,
            "training_data_range": self.training_data_range,
            "validation_score": self.validation_score,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
