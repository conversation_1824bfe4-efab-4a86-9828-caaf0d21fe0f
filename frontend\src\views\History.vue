<template>

  <div class="history-container">
    <!-- 筛选表单 -->
    <el-card class="filter-form">
      <template #header>
        <div class="card-header">
          <span class="title">历史开奖记录查询</span>
        </div>
      </template>

      <el-form :model="filterForm" ref="filterFormRef" label-width="80px" size="small">
        <el-row :gutter="16">
          <!-- 期号范围 -->
          <el-col :span="8">
            <el-form-item label="期号">
              <div class="input-range">
                <el-input-number v-model="filterForm.startPeriod" placeholder="起始期号" :controls="false"
                  class="period-input" />
                <span class="separator">-</span>
                <el-input-number v-model="filterForm.endPeriod" placeholder="结束期号" :controls="false"
                  class="period-input" />
              </div>
            </el-form-item>
          </el-col>

          <!-- 日期范围 -->
          <el-col :span="10">
            <el-form-item label="日期">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" :shortcuts="dateShortcuts" value-format="YYYY-MM-DD"
                @change="handleDateRangeChange" style="width: 100%" />
            </el-form-item>
          </el-col>

          <!-- 特码号码 -->
          <el-col :span="6">
            <el-form-item label="特码">
              <el-input v-model="filterForm.specialNumber" placeholder="输入号码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <!-- 第一行属性 -->
          <el-col :span="6">
            <el-form-item label="生肖">
              <el-select v-model="filterForm.specialZodiac" placeholder="选择生肖" clearable>
                <el-option v-for="zodiac in zodiacOptions" :key="zodiac" :label="zodiac" :value="zodiac" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="波色">
              <el-select v-model="filterForm.specialColor" placeholder="选择波色" clearable>
                <el-option v-for="color in colorOptions" :key="color" :label="color" :value="color" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="单双">
              <el-select v-model="filterForm.specialOddEven" placeholder="选择单双" clearable>
                <el-option v-for="option in oddEvenOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="大小">
              <el-select v-model="filterForm.specialBigSmall" placeholder="选择大小" clearable>
                <el-option v-for="option in bigSmallOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <!-- 第二行属性 -->
          <el-col :span="6">
            <el-form-item label="尾数">
              <el-select v-model="filterForm.specialTailBigSmall" placeholder="选择尾数" clearable>
                <el-option v-for="option in tailBigSmallOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="合数">
              <el-select v-model="filterForm.specialSumOddEven" placeholder="选择合数" clearable>
                <el-option v-for="option in sumOddEvenOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="属性">
              <el-select v-model="filterForm.specialAnimalType" placeholder="选择属性" clearable>
                <el-option v-for="option in animalTypeOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="五行">
              <el-select v-model="filterForm.specialWuxing" placeholder="选择五行" clearable>
                <el-option v-for="option in wuxingOptions" :key="option" :label="option" :value="option" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 按钮组 -->
        <el-row>
          <el-col :span="24" style="text-align: center;">
            <div class="form-buttons">
              <el-button type="primary" :loading="loading" @click="handleSearch" size="default">
                <el-icon>
                  <Search />
                </el-icon> 搜索
              </el-button>
              <el-button @click="handleReset" size="default">
                <el-icon>
                  <Refresh />
                </el-icon> 重置
              </el-button>
              <el-button type="success" @click="handleExport" size="default">
                <el-icon>
                  <Download />
                </el-icon> 导出
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 视图切换 -->
    <div class="view-mode-switch">
      <el-radio-group v-model="viewMode" size="small" @change="handleViewModeChange">
        <el-radio-button value="table">表格视图</el-radio-button>
        <el-radio-button value="trend">走势图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 开奖记录表格 -->
    <el-card class="mt-4">
      <template #header>
        <div class="card-header">
          <span>开奖记录</span>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-show="viewMode === 'table'" class="table-view">
        <el-table v-loading="loading" :data="historyData" style="width: 100%" :stripe="true">
          <el-table-column prop="expect" label="期号" width="120">
            <template #default="{ row }">
              {{ formatPeriod(row.expect) }}
            </template>
          </el-table-column>

          <el-table-column prop="open_time" label="开奖时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.open_time) }}
            </template>
          </el-table-column>

          <el-table-column label="开奖号码" min-width="300">
            <template #default="{ row }">
              <div class="lottery-numbers">
                <template v-for="(num, index) in getNumberArray(row.open_code) || []" :key="index">
                  <div class="number-wrapper">
                    <span class="lottery-ball" :class="[
                      getBallClass(parseInt(num)),
                      { 'special': index === (getNumberArray(row.open_code).length - 1) }
                    ]">
                      {{ num }}
                    </span>
                    <span class="zodiac">{{ getZodiac(parseInt(num)) }}</span>
                  </div>
                </template>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="特别号码属性" width="550">
            <template #default="{ row }">
              <div class="special-attributes">
                <el-tag size="small" :color="getColorTagColor(getSpecialNumber(row))" style="color: #fff;">
                  {{ getColor(getSpecialNumber(row)) }}
                </el-tag>
                <el-tag size="small" type="info">
                  {{ getZodiac(getSpecialNumber(row)) }}
                </el-tag>
                <el-tag size="small" :type="getOddEvenType(getSpecialNumber(row)) || 'default'">
                  {{ isOdd(getSpecialNumber(row)) ? '单' : '双' }}
                </el-tag>
                <el-tag size="small" :type="getBigSmallType(getSpecialNumber(row)) || 'default'">
                  {{ isBig(getSpecialNumber(row)) ? '大' : '小' }}
                </el-tag>
                <el-tag size="small" :type="getTailType(getSpecialNumber(row)) || 'default'">
                  {{ getTailBigSmall(getSpecialNumber(row)) }}
                </el-tag>
                <el-tag size="small" :type="getSumOddEvenType(getSpecialNumber(row)) || 'default'">
                  {{ getSumOddEven(getSpecialNumber(row)) }}
                </el-tag>
                <el-tag size="small" :type="getAnimalType(getSpecialNumber(row)) || 'default'">
                  {{ isDomestic(getSpecialNumber(row)) ? '家禽' : '野兽' }}
                </el-tag>
                <el-tag size="small" :type="getColorType(getSpecialNumber(row)) || 'default'">
                  {{ getColor(getSpecialNumber(row)) }}
                </el-tag>
                <el-tag size="small" :type="getWuxingType(getSpecialNumber(row)) || 'default'">
                  {{ getWuxing(getSpecialNumber(row)) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>

      <!-- 走势图视图 -->
      <div v-show="viewMode === 'trend'" class="trend-view">
        <el-tabs v-model="activeTrendTab" @tab-click="handleTrendTabChange">
          <el-tab-pane label="特码走势" name="special">
            <div ref="specialTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="生肖走势" name="zodiac">
            <div ref="zodiacTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="波色走势" name="color">
            <div ref="colorTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="家野走势" name="animalType">
            <div ref="animalTypeTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="五行走势" name="wuxing">
            <div ref="wuxingTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="大小走势" name="bigSmall">
            <div ref="bigSmallTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="单双走势" name="oddEven">
            <div ref="oddEvenTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
          <el-tab-pane label="合数走势" name="sumOddEven">
            <div ref="sumOddEvenTrendChart" style="width: 100%; height: 700px;"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, h, onBeforeUnmount } from 'vue'

const formatPeriod = (period) => {
  if (!period) return ''
  const str = period.toString()
  return str.padStart(6, '0')
}
import { useDrawStore } from '@/stores/draw'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import GameRules2025 from '@/utils/GameRules2025'
import { Search, Refresh, Download } from '@element-plus/icons-vue'

const store = useDrawStore()
const loading = ref(false)
const viewMode = ref('table')
const trendType = ref('special')
const statsType = ref('frequency')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const historyData = ref([])
const filterFormRef = ref(null)
const activeTrendTab = ref('special')
const dateRange = ref(null)

// 图表DOM引用
const specialTrendChart = ref(null)
const zodiacTrendChart = ref(null)
const colorTrendChart = ref(null)
const animalTypeTrendChart = ref(null)
const wuxingTrendChart = ref(null)
const bigSmallTrendChart = ref(null)
const oddEvenTrendChart = ref(null)
const sumOddEvenTrendChart = ref(null)
const consecutivePatternChart = ref(null)

// 筛选表单数据
const initialFilterForm = {
  startPeriod: null,
  endPeriod: null,
  startDate: '',
  endDate: '',
  specialNumber: '',
  specialZodiac: '',
  specialColor: '',
  specialOddEven: '',
  specialBigSmall: '',
  specialTailBigSmall: '',
  specialSumOddEven: '',
  specialAnimalType: '',
  specialWuxing: ''
}

const filterForm = ref({ ...initialFilterForm })

// 选项数据
const zodiacOptions = GameRules2025.ZODIAC_LIST || [
  '鼠', '牛', '虎', '兔', '龙', '蛇',
  '马', '羊', '猴', '鸡', '狗', '猪'
]

const colorOptions = ['红波', '蓝波', '绿波']
const oddEvenOptions = ['单', '双']
const bigSmallOptions = ['大', '小']
const tailBigSmallOptions = ['尾大', '尾小']
const sumOddEvenOptions = ['合单', '合双']
const animalTypeOptions = ['家禽', '野兽']
const wuxingOptions = ['金', '木', '水', '火', '土']

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 图表实例
const charts = ref({
  specialTrend: null,
  zodiacTrend: null,
  colorTrend: null,
  animalTypeTrend: null, // 家野走势
  wuxingTrend: null,    // 五行走势
  bigSmallTrend: null,  // 大小走势
  oddEvenTrend: null,   // 单双走势
  sumOddEvenTrend: null, // 合数走势
  consecutivePattern: null
})

// 方法
const formatDateTime = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const calculateSum = (numbers) => {
  return numbers.reduce((sum, num) => sum + num, 0)
}

const calculateTailSum = (numbers) => {
  return numbers.reduce((sum, num) => sum + (num % 10), 0)
}

const hasConsecutive = (numbers) => {
  for (let i = 0; i < numbers.length - 1; i++) {
    if (numbers[i + 1] - numbers[i] === 1) {
      return true
    }
  }
  return false
}

const getOddEvenRatio = (numbers) => {
  const odds = numbers.filter(num => num % 2 !== 0).length
  return `${odds}:${numbers.length - odds}`
}

const getBigSmallRatio = (numbers) => {
  const bigs = numbers.filter(num => num > 24).length
  return `${bigs}:${numbers.length - bigs}`
}

const getColorDistribution = (numbers) => {
  const distribution = { 红波: 0, 蓝波: 0, 绿波: 0 }
  numbers.forEach(num => {
    // TODO: 根据号码判断波色
  })
  return Object.entries(distribution)
    .map(([color, count]) => `${color}:${count}`)
    .join(' ')
}

const calculateAverageInterval = (data) => {
  if (!data || data.length < 2) return 0

  let totalInterval = 0
  let count = 0

  for (let i = 1; i < data.length; i++) {
    const currentPeriod = parseInt(data[i].expect)
    const prevPeriod = parseInt(data[i - 1].expect)
    if (!isNaN(currentPeriod) && !isNaN(prevPeriod)) {
      totalInterval += currentPeriod - prevPeriod
      count++
    }
  }

  return count > 0 ? (totalInterval / count).toFixed(2) : 0
}

const fetchHistoryData = async () => {
  try {
    loading.value = true
    console.log('开始获取历史数据...')

    // 确保日期格式为字符串 YYYY-MM-DD
    const startDateStr = filterForm.value.startDate ? filterForm.value.startDate.toString() : undefined
    const endDateStr = filterForm.value.endDate ? filterForm.value.endDate.toString() : undefined

    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      startPeriod: filterForm.value.startPeriod || undefined,
      endPeriod: filterForm.value.endPeriod || undefined,
      startDate: startDateStr && startDateStr.trim() !== '' ? startDateStr : undefined,
      endDate: endDateStr && endDateStr.trim() !== '' ? endDateStr : undefined,
      specialNumber: filterForm.value.specialNumber || undefined,
      specialZodiac: filterForm.value.specialZodiac || undefined,
      specialColor: filterForm.value.specialColor || undefined,
      specialOddEven: filterForm.value.specialOddEven || undefined,
      specialBigSmall: filterForm.value.specialBigSmall || undefined,
      specialTailBigSmall: filterForm.value.specialTailBigSmall || undefined,
      specialSumOddEven: filterForm.value.specialSumOddEven || undefined,
      specialAnimalType: filterForm.value.specialAnimalType || undefined,
      specialWuxing: filterForm.value.specialWuxing || undefined
    }

    console.log('请求参数:', params)

    // 移除所有 undefined、空字符串的参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    console.log('处理后的请求参数:', params)

    try {
      const response = await store.fetchHistoryDraws(params)
      console.log('获取历史数据响应:', response)

      if (response && response.code === 200) {
        console.log('响应数据:', JSON.stringify(response.data))
        console.log('响应总数:', response.total)

        if (Array.isArray(response.data)) {
          historyData.value = response.data
          console.log('设置历史数据数组，长度:', response.data.length)
          console.log('historyData.value:', JSON.stringify(historyData.value));
        } else {
          console.warn('响应数据不是数组:', response.data)
          historyData.value = []
        }
        total.value = response.total || 0
        console.log('设置总数:', total.value)

        // 如果是走势图模式，更新图表
        if (viewMode.value === 'trend') {
          console.log('更新走势图...')
          nextTick(() => {
            updateTrendCharts()
          })
        }
      } else {
        console.error('响应格式无效:', response)
        throw new Error(response?.message || 'Invalid response format')
      }
    } catch (apiError) {
      console.error('API调用失败:', apiError)
      throw apiError
    }
  } catch (error) {
    console.error('获取历史数据失败:', error)
    ElMessage.error(error.message || '获取历史数据失败')
    historyData.value = []
    total.value = 0
  } finally {
    loading.value = false
    console.log('历史数据获取完成')
  }
}

const handleSearch = async () => {
  try {
    currentPage.value = 1
    await fetchHistoryData()
  } catch (error) {
    console.error('Search failed:', error)
    ElMessage.error('搜索失败，请重试')
  }
}

const handleReset = async () => {
  try {
    // 重置表单到初始状态
    Object.assign(filterForm.value, initialFilterForm)
    // 重置分页
    currentPage.value = 1
    pageSize.value = 20
    // 重新获取数据
    await fetchHistoryData()
    ElMessage.success('重置成功')
  } catch (error) {
    console.error('Reset failed:', error)
    ElMessage.error('重置失败，请重试')
  }
}

const handleExport = async () => {
  try {
    const params = {
      startPeriod: filterForm.value.startPeriod || undefined,
      endPeriod: filterForm.value.endPeriod || undefined,
      startDate: filterForm.value.startDate || undefined,
      endDate: filterForm.value.endDate || undefined,
      specialNumber: filterForm.value.specialNumber || undefined,
      specialZodiac: filterForm.value.specialZodiac || undefined,
      specialColor: filterForm.value.specialColor || undefined,
      specialOddEven: filterForm.value.specialOddEven || undefined,
      specialBigSmall: filterForm.value.specialBigSmall || undefined,
      specialTailBigSmall: filterForm.value.specialTailBigSmall || undefined,
      specialSumOddEven: filterForm.value.specialSumOddEven || undefined,
      specialAnimalType: filterForm.value.specialAnimalType || undefined,
      specialWuxing: filterForm.value.specialWuxing || undefined
    }

    // 移除所有 undefined 的参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key]
      }
    })

    await store.exportData(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export failed:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchHistoryData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchHistoryData()
}

const handleViewModeChange = (mode) => {
  viewMode.value = mode
  if (mode === 'trend') {
    nextTick(() => {
      initCharts()
      updateTrendCharts()
    })
  }
}

const handleTrendTabChange = async () => {
  await nextTick()
  initCharts()
  updateTrendCharts()
}

const initCharts = () => {
  if (viewMode.value !== 'trend') return;

  // 获取图表容器引用
  const chartRefs = {
    specialTrend: specialTrendChart.value,
    zodiacTrend: zodiacTrendChart.value,
    colorTrend: colorTrendChart.value,
    animalTypeTrend: animalTypeTrendChart.value,
    wuxingTrend: wuxingTrendChart.value,
    bigSmallTrend: bigSmallTrendChart.value,
    oddEvenTrend: oddEvenTrendChart.value,
    sumOddEvenTrend: sumOddEvenTrendChart.value,
    consecutivePattern: consecutivePatternChart.value
  };

  // 初始化每个图表
  Object.entries(chartRefs).forEach(([name, ref]) => {
    console.log(`Initializing chart: ${name}`);
    if (ref) {
      // 确保容器可见且有尺寸
      if (ref.clientWidth === 0 || ref.clientHeight === 0) {
        console.warn(`Chart container ${name} has zero width or height. Setting default dimensions.`)
        ref.style.width = '100%'
        ref.style.height = '500px'
      }

      // 清理旧的图表实例
      if (charts.value[name]) {
        charts.value[name].dispose()
      }

      try {
        // 初始化图表
        charts.value[name] = echarts.init(ref)

        // 设置默认配置
        charts.value[name].setOption({
          title: {
            text: '',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          }
        })
      } catch (error) {
        console.error(`Error initializing chart ${name}:`, error)
      }
    } else {
      console.log(`Ref for chart ${name} is null`);
    }
  })
}

const updateTrendCharts = () => {
  console.log('updateTrendCharts called');
  console.log('historyData.value in updateTrendCharts:', JSON.stringify(historyData.value));
  // 特码、生肖、波色、连号已存在
  // 新增家野走势
  const updateAnimalTypeTrendChart = () => {
    console.log('updateAnimalTypeTrendChart called');
    if (!charts.value.animalTypeTrend) {
    console.log('charts.value.animalTypeTrend is null');
      return;
    }
    console.log('charts.value.animalTypeTrend:', charts.value.animalTypeTrend);
    let data = [];
    try {
      data = historyData.value ? historyData.value.map(draw => ({
        period: draw.expect,
        number: parseInt(draw.open_code.split(',').pop()),
        animalType: GameRules2025.isDomestic(parseInt(draw.open_code.split(',').pop())) ? '家禽' : '野兽'
      })).reverse() : [];
    } catch (error) {
      console.error('Error in updateAnimalTypeTrendChart:', error);
    }
    const typeList = ['家禽', '野兽']
    const option = {
      title: { text: '家野走势', left: 'center', textStyle: { fontSize: 16, fontWeight: 'bold' } },
      tooltip: { trigger: 'item', formatter: params => `期号：${data[params.dataIndex]?.period}<br/>属性：${typeList[params.value]}` },
      grid: { left: '5%', right: '5%', bottom: '15%', top: '10%', containLabel: true },
      xAxis: { type: 'category', data: data.map(item => `${item.period}期`), axisLabel: { interval: 0, rotate: 45, fontSize: 12 } },
      yAxis: { type: 'category', data: typeList, inverse: true, axisLabel: { fontSize: 14, margin: 10 } },
      series: [{
        name: '家野',
        type: 'line',
        data: data.map(item => typeList.indexOf(item.animalType)),
        symbolSize: 14,
        symbol: 'circle',
        lineStyle: { width: 2, shadowColor: 'rgba(0,0,0,0.15)', shadowBlur: 8, shadowOffsetY: 6 },
        itemStyle: { color: params => params.value === 0 ? '#ffb300' : '#4caf50' },
        label: {
          show: true, position: 'top', formatter: params => typeList[params.value], fontSize: 12, fontWeight: 'bold', color: '#333', backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 4, padding: [2, 4]
        },
        animation: true, animationDuration: 1000, animationEasing: 'cubicInOut'
      }]
    }
    charts.value.animalTypeTrend.setOption(option)
  }
  // 五行走势
  const updateWuxingTrendChart = () => {
    if (!charts.value.wuxingTrend) return
    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      wuxing: GameRules2025.getWuxing(parseInt(draw.open_code.split(',').pop()))
    })).reverse()
    const wuxingList = ['金', '木', '水', '火', '土']
    const option = {
      title: { text: '五行走势', left: 'center', textStyle: { fontSize: 16, fontWeight: 'bold' } },
      tooltip: { trigger: 'item', formatter: params => `期号：${data[params.dataIndex]?.period}<br/>五行：${wuxingList[params.value]}` },
      grid: { left: '5%', right: '5%', bottom: '15%', top: '10%', containLabel: true },
      xAxis: { type: 'category', data: data.map(item => `${item.period}期`), axisLabel: { interval: 0, rotate: 45, fontSize: 12 } },
      yAxis: { type: 'category', data: wuxingList, inverse: true, axisLabel: { fontSize: 14, margin: 10 } },
      series: [{
        name: '五行',
        type: 'line',
        data: data.map(item => wuxingList.indexOf(item.wuxing)),
        symbolSize: 14,
        symbol: 'circle',
        lineStyle: { width: 2, shadowColor: 'rgba(0,0,0,0.15)', shadowBlur: 8, shadowOffsetY: 6 },
        itemStyle: { color: params => ['#bfae6a','#43a047','#2196f3','#e53935','#bcaaa4'][params.value] },
        label: {
          show: true, position: 'top', formatter: params => wuxingList[params.value], fontSize: 12, fontWeight: 'bold', color: '#333', backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 4, padding: [2, 4]
        },
        animation: true, animationDuration: 1000, animationEasing: 'cubicInOut'
      }]
    }
    charts.value.wuxingTrend.setOption(option)
  }
  // 大小走势
  const updateBigSmallTrendChart = () => {
    if (!charts.value.bigSmallTrend) return
    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      bigSmall: GameRules2025.isBig(parseInt(draw.open_code.split(',').pop())) ? '大' : '小'
    })).reverse()
    const bsList = ['大', '小']
    const option = {
      title: { text: '大小走势', left: 'center', textStyle: { fontSize: 16, fontWeight: 'bold' } },
      tooltip: { trigger: 'item', formatter: params => `期号：${data[params.dataIndex]?.period}<br/>属性：${bsList[params.value]}` },
      grid: { left: '5%', right: '5%', bottom: '15%', top: '10%', containLabel: true },
      xAxis: { type: 'category', data: data.map(item => `${item.period}期`), axisLabel: { interval: 0, rotate: 45, fontSize: 12 } },
      yAxis: { type: 'category', data: bsList, inverse: true, axisLabel: { fontSize: 14, margin: 10 } },
      series: [{
        name: '大小',
        type: 'line',
        data: data.map(item => bsList.indexOf(item.bigSmall)),
        symbolSize: 14,
        symbol: 'circle',
        lineStyle: { width: 2, shadowColor: 'rgba(0,0,0,0.15)', shadowBlur: 8, shadowOffsetY: 6 },
        itemStyle: { color: params => params.value === 0 ? '#ff7043' : '#42a5f5' },
        label: {
          show: true, position: 'top', formatter: params => bsList[params.value], fontSize: 12, fontWeight: 'bold', color: '#333', backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 4, padding: [2, 4]
        },
        animation: true, animationDuration: 1000, animationEasing: 'cubicInOut'
      }]
    }
    charts.value.bigSmallTrend.setOption(option)
  }
  // 单双走势
  const updateOddEvenTrendChart = () => {
    if (!charts.value.oddEvenTrend) return
    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      oddEven: GameRules2025.isOdd(parseInt(draw.open_code.split(',').pop())) ? '单' : '双'
    })).reverse()
    const oeList = ['单', '双']
    const option = {
      title: { text: '单双走势', left: 'center', textStyle: { fontSize: 16, fontWeight: 'bold' } },
      tooltip: { trigger: 'item', formatter: params => `期号：${data[params.dataIndex]?.period}<br/>属性：${oeList[params.value]}` },
      grid: { left: '5%', right: '5%', bottom: '15%', top: '10%', containLabel: true },
      xAxis: { type: 'category', data: data.map(item => `${item.period}期`), axisLabel: { interval: 0, rotate: 45, fontSize: 12 } },
      yAxis: { type: 'category', data: oeList, inverse: true, axisLabel: { fontSize: 14, margin: 10 } },
      series: [{
        name: '单双',
        type: 'line',
        data: data.map(item => oeList.indexOf(item.oddEven)),
        symbolSize: 14,
        symbol: 'circle',
        lineStyle: { width: 2, shadowColor: 'rgba(0,0,0,0.15)', shadowBlur: 8, shadowOffsetY: 6 },
        itemStyle: { color: params => params.value === 0 ? '#f06292' : '#64b5f6' },
        label: {
          show: true, position: 'top', formatter: params => oeList[params.value], fontSize: 12, fontWeight: 'bold', color: '#333', backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 4, padding: [2, 4]
        },
        animation: true, animationDuration: 1000, animationEasing: 'cubicInOut'
      }]
    }
    charts.value.oddEvenTrend.setOption(option)
  }
  // 合数走势
  const updateSumOddEvenTrendChart = () => {
    if (!charts.value.sumOddEvenTrend) return
    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      sumOddEven: GameRules2025.isSumOdd(parseInt(draw.open_code.split(',').pop())) ? '单' : '双'
    })).reverse()
    const soeList = ['单', '双']
    const option = {
      title: { text: '合数单双走势', left: 'center', textStyle: { fontSize: 16, fontWeight: 'bold' } },
      tooltip: { trigger: 'item', formatter: params => `期号：${data[params.dataIndex]?.period}<br/>属性：${soeList[params.value]}` },
      grid: { left: '5%', right: '5%', bottom: '15%', top: '10%', containLabel: true },
      xAxis: { type: 'category', data: data.map(item => `${item.period}期`), axisLabel: { interval: 0, rotate: 45, fontSize: 12 } },
      yAxis: { type: 'category', data: soeList, inverse: true, axisLabel: { fontSize: 14, margin: 10 } },
      series: [{
        name: '合数单双',
        type: 'line',
        data: data.map(item => soeList.indexOf(item.sumOddEven)),
        symbolSize: 14,
        symbol: 'circle',
        lineStyle: { width: 2, shadowColor: 'rgba(0,0,0,0.15)', shadowBlur: 8, shadowOffsetY: 6 },
        itemStyle: { color: params => params.value === 0 ? '#ffb74d' : '#90caf9' },
        label: {
          show: true, position: 'top', formatter: params => soeList[params.value], fontSize: 12, fontWeight: 'bold', color: '#333', backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 4, padding: [2, 4]
        },
        animation: true, animationDuration: 1000, animationEasing: 'cubicInOut'
      }]
    }
    charts.value.sumOddEvenTrend.setOption(option)
  }
  // 调用
  updateAnimalTypeTrendChart()
  updateWuxingTrendChart()
  updateBigSmallTrendChart()
  updateOddEvenTrendChart()
  updateSumOddEvenTrendChart()

  if (!historyData.value.length) return

  const updateSpecialTrendChart = () => {
    if (!charts.value.specialTrend) return

    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop())
    })).reverse()

    const option = {
      title: {
        text: '特码走势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const data = params[0]
          return `期号：${data.name}<br/>特码：${data.value}`
        }
      },
      legend: {
        data: ['特码'],
        bottom: 10
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => `${item.period}期`),
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '特码',
        min: 1,
        max: 49,
        interval: 2, // 增加间隔，减少y轴标签数量
        axisLabel: {
          fontSize: 14, // 增大字体
          margin: 10, // 增加标签与轴线的距离
          formatter: '{value}' // 简化标签显示
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [{
        name: '特码',
        type: 'line',
        data: data.map(item => item.number),
        symbolSize: 12, // 增大数据点大小
        symbol: 'circle', // 使用圆形数据点
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 14, // 增大标签字体
          fontWeight: 'bold', // 加粗标签
          distance: 5 // 增加标签与数据点的距离
        },
        itemStyle: {
          color: (params) => {
            const num = params.value
            if (GameRules2025.RED_NUMBERS.includes(num)) return '#ff4d4f'
            if (GameRules2025.BLUE_NUMBERS.includes(num)) return '#1890ff'
            if (GameRules2025.GREEN_NUMBERS.includes(num)) return '#52c41a'
            return '#666'
          }
        },
        lineStyle: {
          width: 2,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 10,
          shadowOffsetY: 8
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicInOut'
      }]
    }

    charts.value.specialTrend.setOption(option)
  }

  const updateZodiacTrendChart = () => {
    if (!charts.value.zodiacTrend) return

    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      zodiac: GameRules2025.getZodiac(parseInt(draw.open_code.split(',').pop()))
    })).reverse()

    const zodiacList = GameRules2025.ZODIAC_LIST
    const option = {
      title: {
        text: '生肖走势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const idx = params.value[0]
          const zodiac = params.value[1]
          const period = data[idx]?.period
          return `期号：${period}<br/>生肖：${zodiac}`
        }
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => `${item.period}期`),
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'category',
        data: zodiacList,
        inverse: true,
        axisLabel: {
          fontSize: 14,
          margin: 10,
          formatter: function(value) {
            return value + (GameRules2025.ZODIAC_ICONS?.[value] || ''); // 添加生肖图标
          }
        }
      },
      series: [
        {
          name: '生肖',
          type: 'line',
          data: data.map(item => zodiacList.indexOf(item.zodiac)),
          symbolSize: 14,
          symbol: 'circle',
          lineStyle: {
            width: 2,
            shadowColor: 'rgba(0,0,0,0.15)',
            shadowBlur: 8,
            shadowOffsetY: 6
          },
          itemStyle: {
            color: (params) => {
              const number = data[params.dataIndex].number
              if (GameRules2025.RED_NUMBERS.includes(number)) return '#ff4d4f'
              if (GameRules2025.BLUE_NUMBERS.includes(number)) return '#1890ff'
              if (GameRules2025.GREEN_NUMBERS.includes(number)) return '#52c41a'
              return '#666'
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: (params) => zodiacList[params.value],
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333',
            backgroundColor: 'rgba(255,255,255,0.7)',
            borderRadius: 4,
            padding: [2, 4]
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        }
      ]
    }

    charts.value.zodiacTrend.setOption(option)
  }

  const updateColorTrendChart = () => {
    if (!charts.value.colorTrend) return

    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop()),
      color: GameRules2025.getColor(parseInt(draw.open_code.split(',').pop()))
    })).reverse()

    // 创建波色走势数据
    const colorMap = {
      '红波': 1,
      '蓝波': 2,
      '绿波': 3
    }

    const option = {
      title: {
        text: '特码波色走势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          const data = params[0]
          const colorName = data.value === 1 ? '红波' : data.value === 2 ? '蓝波' : '绿波'
          return `期号：${data.name}<br/>波色：${colorName}`
        }
      },
      legend: {
        data: ['波色'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => `${item.period}期`),
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'category',
        data: ['红波', '蓝波', '绿波'],
        inverse: true
      },
      series: [
        {
          name: '波色',
          type: 'line',
          data: data.map(item => {
            if(item.color === '红波') return 0;
            if(item.color === '蓝波') return 1;
            if(item.color === '绿波') return 2;
            return null;
          }),
          symbolSize: 14,
          symbol: 'circle',
          lineStyle: {
            width: 2,
            shadowColor: 'rgba(0,0,0,0.15)',
            shadowBlur: 8,
            shadowOffsetY: 6
          },
          itemStyle: {
            color: (params) => {
              const colorIdx = params.value;
              if(colorIdx === 0) return '#ff4d4f';
              if(colorIdx === 1) return '#1890ff';
              if(colorIdx === 2) return '#52c41a';
              return '#666';
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: (params) => {
              if(params.value === 0) return '红波';
              if(params.value === 1) return '蓝波';
              if(params.value === 2) return '绿波';
              return '';
            },
            fontSize: 12,
            fontWeight: 'bold',
            color: '#333',
            backgroundColor: 'rgba(255,255,255,0.7)',
            borderRadius: 4,
            padding: [2, 4]
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: 'cubicInOut'
        }
      ]
    }

    charts.value.colorTrend.setOption(option)
  }

  const updateConsecutiveChart = () => {
    if (!charts.value.consecutivePattern) return

    const data = historyData.value.map(draw => ({
      period: draw.expect,
      number: parseInt(draw.open_code.split(',').pop())
    })).reverse()

    // 分析连号
    let consecutiveCount = 0
    let nonConsecutiveCount = 0

    for (let i = 1; i < data.length; i++) {
      const currentNum = data[i].number
      const prevNum = data[i - 1].number
      if (Math.abs(currentNum - prevNum) === 1) {
        consecutiveCount++
      } else {
        nonConsecutiveCount++
      }
    }

    const option = {
      title: {
        text: '特码连号分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['连号', '非连号']
      },
      series: [
        {
          name: '连号分析',
          type: 'pie',
          radius: '50%',
          data: [
            {
              value: consecutiveCount,
              name: '连号',
              itemStyle: { color: '#ff4d4f' }
            },
            {
              value: nonConsecutiveCount,
              name: '非连号',
              itemStyle: { color: '#1890ff' }
            }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          }
        }
      ]
    }

    charts.value.consecutivePattern.setOption(option)
  }

  updateSpecialTrendChart()
  updateZodiacTrendChart()
  updateColorTrendChart()
  updateConsecutiveChart()
}

const updateStatsCharts = async () => {
  try {
    loading.value = true
    const result = await store.getStatistics({
      startDate: filterForm.value.dateRange?.[0],
      endDate: filterForm.value.dateRange?.[1]
    })

    const stats = result.data
    if (stats && historyData.value.length > 0) {
      stats.averageInterval = calculateAverageInterval(historyData.value)
    }

    switch (statsType.value) {
      case 'frequency':
        updateFrequencyCharts(stats)
        break
      case 'pattern':
        updatePatternCharts(stats)
        break
      case 'distribution':
        updateDistributionCharts(stats)
        break
    }
  } catch (error) {
    console.error('Failed to fetch statistics:', error)
  } finally {
    loading.value = false
  }
}

const updateFrequencyCharts = (stats) => {
  // TODO: 实现频率分析图表
}

const updatePatternCharts = (stats) => {
  // TODO: 实现规律分析图表
}

const updateDistributionCharts = (stats) => {
  // TODO: 实现分布分析图表
}

// 生命周期钩子
onMounted(() => {
  fetchHistoryData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  Object.values(charts.value).forEach(chart => {
    if (chart && chart.dispose && typeof chart.dispose === 'function') {
      try {
        chart.dispose()
      } catch (error) {
        console.error('Error disposing chart:', error)
      }
    }
  })
  // 清空图表实例，补充缺失的图表键
  charts.value = {
    specialTrend: null,
    zodiacTrend: null,
    colorTrend: null,
    animalTypeTrend: null,
    wuxingTrend: null,
    bigSmallTrend: null,
    oddEvenTrend: null,
    sumOddEvenTrend: null,
    consecutivePattern: null
  }
})

const handleResize = () => {
  if (viewMode.value !== 'trend') return

  Object.values(charts.value).forEach(chart => {
    if (chart && chart.resize && typeof chart.resize === 'function') {
      try {
        chart.resize()
      } catch (error) {
        console.error('Error resizing chart:', error)
      }
    }
  })
}

// 获取号码球颜色
const getBallClass = (num) => {
  const classes = []
  if (GameRules2025.RED_NUMBERS.includes(num)) classes.push('red')
  if (GameRules2025.BLUE_NUMBERS.includes(num)) classes.push('blue')
  if (GameRules2025.GREEN_NUMBERS.includes(num)) classes.push('green')
  return classes
}

// 获取号码属性
const getNumberAttributes = (number) => {
  return GameRules2025.getNumberAttributes(number)
}

// 获取特殊号码
const getSpecialNumber = (row) => {
  if (!row.open_code) return null
  const numbers = getNumberArray(row.open_code)
  if (numbers.length === 0) return null
  return parseInt(numbers[numbers.length - 1])
}

// 获取颜色
const getColor = (num) => {
  if (num === null) return ''
  return GameRules2025.getColor(num)
}

// 获取生肖
const getZodiac = (num) => {
  if (num === null) return ''
  return GameRules2025.getZodiac(num)
}

// 获取五行
const getWuxing = (num) => {
  if (num === null) return ''
  return GameRules2025.getWuxing(num)
}

// 获取合数单双
const getSumOddEven = (num) => {
  if (num === null) return ''
  const attrs = GameRules2025.getNumberAttributes(num)
  return attrs.sumOddEven
}

// 获取尾数大小
const getTailBigSmall = (num) => {
  if (num === null) return ''
  const attrs = GameRules2025.getNumberAttributes(num)
  return attrs.tailBigSmall
}

// 判断是否为单
const isOdd = (num) => {
  if (num === null) return false
  return num % 2 === 1
}

// 判断是否为大
const isBig = (num) => {
  if (num === null) return false
  return num > 24
}

// 判断是否为家禽
const isDomestic = (num) => {
  if (num === null) return false
  return GameRules2025.isDomestic(num)
}

// 获取颜色标签颜色
const getColorTagColor = (num) => {
  if (num === null) return ''
  const color = getColor(num)
  if (color === '红波') return '#ff4d4f'
  if (color === '蓝波') return '#1890ff'
  if (color === '绿波') return '#52c41a'
  return ''
}

// 添加辅助函数来转换属性为 Element Plus tag 类型
const getTagType = (attribute) => {
  switch (attribute) {
    case '单':
    case '合单':
    case '尾单':
      return 'warning'
    case '双':
    case '合双':
    case '尾双':
      return 'info'
    case '大':
    case '尾大':
    case '合大':
      return 'success'
    case '小':
    case '尾小':
    case '合小':
      return 'danger'
    case '家禽':
      return 'success'
    case '野兽':
      return 'warning'
    case '红波':
      return 'danger'
    case '蓝波':
      return 'primary'
    case '绿波':
      return 'success'
    default:
      return ''
  }
}

// 更新属性获取函数以返回正确的 tag 类型
const getTailType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.tailBigSmall)
}

const getOddEvenType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.oddEven)
}

const getBigSmallType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.bigSmall)
}

const getSumOddEvenType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.sumOddEven)
}

const getAnimalType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.animalType)
}

const getColorType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  return getTagType(attributes.color)
}

const getWuxingType = (number) => {
  const attributes = GameRules2025.getNumberAttributes(number)
  const wuxing = attributes.wuxing
  switch (wuxing) {
    case '金': return 'warning'
    case '木': return 'success'
    case '水': return 'primary'
    case '火': return 'danger'
    case '土': return 'info'
    default: return ''
  }
}

const trendTabs = [
  { label: '特码走势', value: 'special' },
  { label: '生肖走势', value: 'zodiac' },
  { label: '波色走势', value: 'color' },
  { label: '家野走势', value: 'animalType' },
  { label: '五行走势', value: 'wuxing' },
  { label: '大小走势', value: 'bigSmall' },
  { label: '单双走势', value: 'oddEven' },
  { label: '合数走势', value: 'sumOddEven' },
  { label: '连号分析', value: 'consecutive' }
]

// 将号码转换为数组
const getNumberArray = (number) => {
  if (!number) return []

  // 如果是字符串并且包含逗号，处理分割
  if (typeof number === 'string' && number.includes(',')) {
    // 处理包含+号的格式，如 "48,06,23,33,43,47+47"
    let numberStr = number
    if (number.includes('+')) {
      // 将+号替换为逗号，确保所有号码都被包含
      numberStr = number.replace('+', ',')
    }
    return numberStr.split(',').map(num => num.trim()).filter(num => num !== '')
  }

  // 如果是数字，转换为字符串并返回单元素数组
  return [String(number)]
}

const handleDateRangeChange = (val) => {
  if (val) {
    [filterForm.value.startDate, filterForm.value.endDate] = val
  } else {
    filterForm.value.startDate = undefined
    filterForm.value.endDate = undefined
  }
}
</script>

<style lang="scss" scoped>
.history-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  .view-mode-switch {
    margin: 20px 0;
    display: flex;
    justify-content: center;
  }

  .trend-view {
    margin-top: 20px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #409eff;
    }

    .controls {
      display: flex;
      gap: 10px;
    }
  }

  .search-bar {
    margin-bottom: 20px;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;

      .form-item {
        flex: 1;
        min-width: 200px;
      }

      .search-btn {
        margin-left: auto;
      }
    }
  }

  .table-container {
    margin-top: 1rem;
  }

  .expand-content {
    padding: 1rem;
  }

  .mt-4 {
    margin-top: 1rem;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }

    70% {
      box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }

    100% {
      box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
  }

  .table-view {
    .lottery-numbers {
      display: flex;
      gap: 12px;
      justify-content: center;

      .number-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .lottery-ball {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          color: #fff;
          font-weight: bold;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &.red {
            background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
          }

          &.blue {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          }

          &.green {
            background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
          }

          &.special {
            transform: scale(1.1);
            border: 2px solid #ffd700;
          }
        }

        .zodiac {
          font-size: 12px;
          color: #666;
          background-color: #f5f7fa;
          padding: 2px 6px;
          border-radius: 4px;
          min-width: 28px;
          text-align: center;
        }
      }
    }

    .number-attributes {
      display: flex;
      gap: 8px;
      justify-content: center;
    }
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  @media screen and (max-width: 768px) {
    .history-container {
      padding: 10px;
    }
  }
}
</style>
