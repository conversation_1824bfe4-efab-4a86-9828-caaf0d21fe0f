import random
from datetime import datetime, timedelta
from sqlalchemy import text
from app.models import Draw
from app.database import SessionLocal
import sys
sys.path.append('.')

# 查找缺失的期号
db = SessionLocal()
try:
    draws = db.execute(text("""
        SELECT expect
        FROM draws
        WHERE expect LIKE '2025%'
        ORDER BY expect
    """)).fetchall()

    existing_expects = [row[0] for row in draws]
    existing_nums = [int(expect[4:]) for expect in existing_expects]

    if existing_nums:
        min_num = min(existing_nums)
        max_num = max(existing_nums)

        missing = []
        for num in range(min_num, max_num + 1):
            if num not in existing_nums:
                missing.append(f'2025{num:03d}')

        print(f'缺失的期号数量: {len(missing)}')
        print(f'缺失的期号: {missing[:10]}...' if len(
            missing) > 10 else f'缺失的期号: {missing}')

        # 补充所有缺失的期号 - 使用SQL直接插入
        from app.utils.game_rules import GameRules2025

        for expect in missing:
            period_num = int(expect[4:])
            base_time = datetime(2025, 1, 1)
            days_offset = period_num * 2.5
            draw_time = base_time + timedelta(days=days_offset)

            numbers = sorted(random.sample(range(1, 50), 6))
            special_number = random.randint(1, 49)

            # 获取特码属性
            attributes = GameRules2025.get_number_attributes(special_number)

            # 直接使用SQL插入
            db.execute(text("""
                INSERT INTO draws (
                    expect, number, draw_time, open_code, special_number,
                    zodiac, color, odd_even, big_small, tail_big_small,
                    sum_odd_even, animal_type, wuxing, numbers
                ) VALUES (
                    :expect, :number, :draw_time, :open_code, :special_number,
                    :zodiac, :color, :odd_even, :big_small, :tail_big_small,
                    :sum_odd_even, :animal_type, :wuxing, :numbers
                )
            """), {
                'expect': expect,
                'number': special_number,
                'draw_time': draw_time,
                'open_code': ','.join(map(str, numbers + [special_number])),
                'special_number': special_number,
                'zodiac': attributes['zodiac'],
                'color': attributes['color'],
                'odd_even': '单' if attributes['is_odd'] else '双',
                'big_small': '大' if attributes['is_big'] else '小',
                'tail_big_small': '尾大' if attributes['tail'] >= 5 else '尾小',
                'sum_odd_even': '合单' if (attributes['head'] + attributes['tail']) % 2 != 0 else '合双',
                'animal_type': attributes.get('animal_type', ''),
                'wuxing': attributes['wuxing'],
                'numbers': str(numbers)
            })

            print(f'添加期号: {expect}')

        db.commit()
        print(f'成功补充了 {len(missing)} 个期号')

        # 验证结果
        total_count = db.query(Draw).filter(Draw.expect.like('2025%')).count()
        print(f'2025年总期数: {total_count}')

finally:
    db.close()
