import time
from functools import wraps
from flask import request, g
import logging

logger = logging.getLogger(__name__)


def init_performance_monitor(app):
    """初始化性能监控"""

    @app.before_request
    def before_request():
        """请求开始前的处理"""
        g.start_time = time.time()

    @app.after_request
    def after_request(response):
        """请求结束后的处理"""
        if hasattr(g, 'start_time'):
            elapsed_time = time.time() - g.start_time
            logger.info(f"请求 {request.path} 处理时间: {elapsed_time:.3f}秒")
        return response


def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed_time = time.time() - start_time
        logger.info(f"函数 {func.__name__} 执行时间: {elapsed_time:.3f}秒")
        return result
    return wrapper
