#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的统计功能
"""

import requests
import json

def test_statistics_api():
    """测试统计API数据"""
    print("🔍 测试统计API数据...")
    
    try:
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        if response.status_code == 200:
            data = response.json()
            basic_stats = data.get('data', {}).get('basicStats', {})
            number_frequency = data.get('data', {}).get('numberFrequency', {})
            missing_data = basic_stats.get('missing', {})
            
            print(f"✅ API调用成功")
            print(f"📊 总期数: {basic_stats.get('totalCount', 0)}")
            print(f"📈 号码频率数据: {len(number_frequency)} 个号码")
            print(f"📉 遗漏数据: {len(missing_data.get('current', {}))} 个号码")
            
            return {
                'number_frequency': number_frequency,
                'missing_current': missing_data.get('current', {}),
                'missing_max': missing_data.get('max', {}),
                'total_count': basic_stats.get('totalCount', 0)
            }
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return None

def test_probability_calculation(stats_data):
    """测试开出概率计算"""
    print(f"\n🎯 测试开出概率计算...")
    
    if not stats_data:
        print("❌ 没有统计数据")
        return
    
    number_frequency = stats_data['number_frequency']
    total_draws = sum(int(count) for count in number_frequency.values())
    theoretical_prob = 100 / 49  # 理论概率约2.04%
    
    print(f"📊 总开奖期数: {total_draws}")
    print(f"🎲 理论概率: {theoretical_prob:.2f}%")
    
    # 分析概率分布
    probabilities = []
    for number in range(1, 50):
        count = int(number_frequency.get(str(number), 0))
        actual_prob = (count / total_draws * 100) if total_draws > 0 else 0
        probabilities.append((number, actual_prob, count))
    
    # 按概率排序
    probabilities.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🔥 概率最高的5个号码:")
    for i, (number, prob, count) in enumerate(probabilities[:5]):
        deviation = prob - theoretical_prob
        print(f"  {i+1}. 号码{number:02d}: {prob:.2f}% ({count}次) 偏差: {deviation:+.2f}%")
    
    print(f"\n❄️ 概率最低的5个号码:")
    for i, (number, prob, count) in enumerate(probabilities[-5:]):
        deviation = prob - theoretical_prob
        print(f"  {i+1}. 号码{number:02d}: {prob:.2f}% ({count}次) 偏差: {deviation:+.2f}%")
    
    return probabilities

def test_rebound_index(stats_data):
    """测试回补指数计算"""
    print(f"\n🔄 测试回补指数计算...")
    
    if not stats_data:
        print("❌ 没有统计数据")
        return
    
    number_frequency = stats_data['number_frequency']
    missing_current = stats_data['missing_current']
    missing_max = stats_data['missing_max']
    
    rebound_indices = []
    
    for number in range(1, 50):
        num_str = str(number)
        count = int(number_frequency.get(num_str, 0))
        current_missing = int(missing_current.get(num_str, 0))
        max_missing = int(missing_max.get(num_str, 0))
        
        # 计算回补指数
        if max_missing == 0:
            rebound_index = 0
        else:
            missing_ratio = current_missing / max_missing
            frequency = count if count > 0 else 1
            rebound_index = min(100, (missing_ratio * 70) + (frequency * 2))
        
        rebound_indices.append((number, round(rebound_index), current_missing, max_missing, count))
    
    # 按回补指数排序
    rebound_indices.sort(key=lambda x: x[1], reverse=True)
    
    print(f"🚀 回补指数最高的5个号码:")
    for i, (number, index, current, max_miss, count) in enumerate(rebound_indices[:5]):
        level = "极高" if index >= 80 else "高" if index >= 60 else "中等" if index >= 40 else "低" if index >= 20 else "极低"
        print(f"  {i+1}. 号码{number:02d}: {index} ({level}) - 当前遗漏{current}, 最大遗漏{max_miss}, 出现{count}次")
    
    print(f"\n📉 回补指数最低的5个号码:")
    for i, (number, index, current, max_miss, count) in enumerate(rebound_indices[-5:]):
        level = "极高" if index >= 80 else "高" if index >= 60 else "中等" if index >= 40 else "低" if index >= 20 else "极低"
        print(f"  {i+1}. 号码{number:02d}: {index} ({level}) - 当前遗漏{current}, 最大遗漏{max_miss}, 出现{count}次")
    
    return rebound_indices

def test_stability_rating(stats_data):
    """测试稳定性评级"""
    print(f"\n⚖️ 测试稳定性评级...")
    
    if not stats_data:
        print("❌ 没有统计数据")
        return
    
    number_frequency = stats_data['number_frequency']
    missing_current = stats_data['missing_current']
    missing_max = stats_data['missing_max']
    total_draws = sum(int(count) for count in number_frequency.values())
    
    stability_ratings = []
    
    for number in range(1, 50):
        num_str = str(number)
        count = int(number_frequency.get(num_str, 0))
        current_missing = int(missing_current.get(num_str, 0))
        max_missing = int(missing_max.get(num_str, 0))
        
        # 计算热度指数
        if total_draws > 0:
            frequency_ratio = count / total_draws
            expected_frequency = 1 / 49
            hot_index = (frequency_ratio / expected_frequency) * 50
        else:
            hot_index = 0
        
        # 计算稳定性
        stability = 0
        
        # 基础分：出现次数
        if count >= 6:
            stability += 2
        elif count >= 3:
            stability += 1
        
        # 遗漏稳定性
        if max_missing > 0:
            missing_stability = 1 - (current_missing / max_missing)
            stability += missing_stability * 2
        
        # 热度稳定性
        if 40 <= hot_index <= 60:
            stability += 1
        
        final_rating = min(5, max(1, round(stability)))
        stability_ratings.append((number, final_rating, count, current_missing, hot_index))
    
    # 按稳定性排序
    stability_ratings.sort(key=lambda x: x[1], reverse=True)
    
    print(f"⭐ 稳定性最高的5个号码:")
    for i, (number, rating, count, missing, hot_index) in enumerate(stability_ratings[:5]):
        stars = "⭐" * rating
        print(f"  {i+1}. 号码{number:02d}: {rating}星 {stars} - 出现{count}次, 遗漏{missing}期, 热度{hot_index:.1f}")
    
    print(f"\n📉 稳定性最低的5个号码:")
    for i, (number, rating, count, missing, hot_index) in enumerate(stability_ratings[-5:]):
        stars = "⭐" * rating
        print(f"  {i+1}. 号码{number:02d}: {rating}星 {stars} - 出现{count}次, 遗漏{missing}期, 热度{hot_index:.1f}")
    
    return stability_ratings

def test_frontend_integration():
    """测试前端集成"""
    print(f"\n🌐 测试前端集成...")
    
    try:
        response = requests.get("http://localhost:5181/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            print("💡 请在浏览器中:")
            print("   1. 打开统计页面")
            print("   2. 切换到'特码综合分析'标签")
            print("   3. 在列显示控制中选择不同的分析维度:")
            print("      - 概率分析：查看开出概率列")
            print("      - 回补分析：查看回补指数列")
            print("      - 稳定性：查看稳定性评级列")
            print("   4. 验证新增统计指标的显示效果")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新增统计功能...")
    print("=" * 60)
    
    # 1. 测试API数据
    stats_data = test_statistics_api()
    
    # 2. 测试概率计算
    probabilities = test_probability_calculation(stats_data)
    
    # 3. 测试回补指数
    rebound_indices = test_rebound_index(stats_data)
    
    # 4. 测试稳定性评级
    stability_ratings = test_stability_rating(stats_data)
    
    # 5. 测试前端集成
    frontend_ok = test_frontend_integration()
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 新增统计功能测试报告")
    print("=" * 60)
    
    tests = [
        ("API数据获取", bool(stats_data)),
        ("概率计算", bool(probabilities)),
        ("回补指数计算", bool(rebound_indices)),
        ("稳定性评级", bool(stability_ratings)),
        ("前端集成", frontend_ok)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    print(f"测试结果: {passed}/{total} 通过")
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！新增统计功能开发成功！")
        print("💡 新增功能亮点:")
        print("   - 📊 开出概率：基于历史数据的实际概率分析")
        print("   - 🔄 回补指数：预测长期遗漏号码的回补可能性")
        print("   - ⭐ 稳定性评级：综合评估号码表现的稳定程度")
        print("   - 🎯 智能筛选：支持按不同维度筛选和分析")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
