# 设置控制台编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=== 开始合并文件 ===" -ForegroundColor Green

# 合并前端文件
Write-Host "`n合并前端文件..." -ForegroundColor Cyan
$frontendSrcPath = "src/frontend"
$frontendDestPath = "frontend/src"

if (Test-Path $frontendSrcPath) {
    # 创建临时目录
    $tempDir = "temp_frontend"
    New-Item -ItemType Directory -Path $tempDir -Force

    # 复制所有文件到临时目录
    Copy-Item "$frontendSrcPath/*" $tempDir -Recurse -Force
    Copy-Item "$frontendDestPath/*" $tempDir -Recurse -Force

    # 清空目标目录
    Remove-Item "$frontendDestPath/*" -Recurse -Force
    
    # 将合并后的文件移动到目标目录
    Copy-Item "$tempDir/*" $frontendDestPath -Recurse -Force
    
    # 清理临时目录和源目录
    Remove-Item $tempDir -Recurse -Force
    Remove-Item $frontendSrcPath -Recurse -Force
}

# 合并后端文件
Write-Host "`n合并后端文件..." -ForegroundColor Cyan
$backendSrcPath = "src"
$backendDestPath = "backend"

if (Test-Path $backendSrcPath) {
    # 创建临时目录
    $tempDir = "temp_backend"
    New-Item -ItemType Directory -Path $tempDir -Force

    # 复制所有文件到临时目录
    Copy-Item "$backendSrcPath/*" $tempDir -Recurse -Force
    Copy-Item "$backendDestPath/*" $tempDir -Recurse -Force

    # 清空目标目录
    Remove-Item "$backendDestPath/*" -Recurse -Force
    
    # 将合并后的文件移动到目标目录
    Copy-Item "$tempDir/*" $backendDestPath -Recurse -Force
    
    # 清理临时目录和源目录
    Remove-Item $tempDir -Recurse -Force
    Remove-Item $backendSrcPath -Recurse -Force
}

# 创建环境配置文件
Write-Host "`n创建环境配置文件..." -ForegroundColor Cyan

# 前端环境配置
$frontendEnv = @"
VITE_APP_API_BASE_URL=http://localhost:5000/api/v1
VITE_APP_ENV=development
"@
Set-Content -Path "frontend/.env" -Value $frontendEnv

# 后端环境配置
$backendEnv = @"
# Flask配置
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///data/lottery.db

# 应用配置
APP_NAME=智能数字竞猜预测系统
APP_VERSION=1.0.0
"@
Set-Content -Path "backend/.env" -Value $backendEnv

# 移动Docker文件
Write-Host "`n整理Docker文件..." -ForegroundColor Cyan

# 创建docker目录结构
New-Item -ItemType Directory -Path "docker/frontend" -Force
New-Item -ItemType Directory -Path "docker/backend" -Force

# 移动Dockerfile文件
if (Test-Path "Dockerfile.prod") {
    Move-Item "Dockerfile.prod" "docker/frontend/Dockerfile" -Force
}
if (Test-Path "backend/Dockerfile") {
    Move-Item "backend/Dockerfile" "docker/backend/" -Force
}
if (Test-Path "docker-compose.yml") {
    Move-Item "docker-compose.yml" "docker/" -Force
}

Write-Host "`n=== 文件合并完成 ===" -ForegroundColor Green 