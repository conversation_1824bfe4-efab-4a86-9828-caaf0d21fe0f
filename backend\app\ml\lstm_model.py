import numpy as np
from typing import Dict, List, Any, Tuple
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from .base_model import BaseModel


class LSTMModel(BaseModel):
    def __init__(self):
        super().__init__("lstm")
        self.sequence_length = 10  # 使用前10期数据预测
        self.feature_size = None
        self.scaler = None

    def build_model(self, input_shape: Tuple[int, int, int]):
        """构建LSTM模型"""
        model = Sequential([
            LSTM(128, input_shape=(
                input_shape[1], input_shape[2]), return_sequences=True),
            Dropout(0.2),
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            Dense(32, activation='relu'),
            Dense(1)  # 输出层
        ])
        model.compile(optimizer='adam', loss='mse', metrics=['mae'])
        return model

    def preprocess_data(self, data: List[Dict[str, Any]]) -> <PERSON>ple[np.ndarray, np.ndarray]:
        """预处理数据"""
        # 提取特征
        features = []
        targets = []

        for i in range(len(data) - self.sequence_length):
            sequence = data[i:i + self.sequence_length]
            target = data[i + self.sequence_length]['special_number']

            # 构建特征向量
            sequence_features = []
            for item in sequence:
                feature_vector = [
                    item['special_number'],
                    1 if item['special_odd_even'] == '单' else 0,
                    1 if item['special_big_small'] == '大' else 0,
                    1 if item['special_color'] == '红波' else (
                        2 if item['special_color'] == '蓝波' else 0),
                ]
                sequence_features.append(feature_vector)

            features.append(sequence_features)
            targets.append(target)

        X = np.array(features)
        y = np.array(targets)

        return X, y

    def train(self, X: np.ndarray, y: np.ndarray, params: Dict[str, Any] = None) -> Dict[str, float]:
        """训练模型"""
        if params is None:
            params = {
                'epochs': 100,
                'batch_size': 32,
                'validation_split': 0.2
            }

        if self.model is None:
            self.model = self.build_model(X.shape)

        history = self.model.fit(
            X, y,
            epochs=params['epochs'],
            batch_size=params['batch_size'],
            validation_split=params['validation_split'],
            verbose=1
        )

        metrics = {
            'loss': float(history.history['loss'][-1]),
            'val_loss': float(history.history['val_loss'][-1]),
            'mae': float(history.history['mae'][-1]),
            'val_mae': float(history.history['val_mae'][-1])
        }

        return metrics

    def predict(self, X: np.ndarray) -> Dict[str, Any]:
        """预测结果"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        predictions = self.model.predict(X)

        # 获取最可能的特码
        special_numbers = []
        probabilities = self._calculate_probabilities(predictions)

        # 获取前30个最可能的号码
        top_30_indices = np.argsort(probabilities)[-30:][::-1]

        result = {
            'special_numbers_5': top_30_indices[:5].tolist(),
            'special_numbers_10': top_30_indices[:10].tolist(),
            'special_numbers_15': top_30_indices[:15].tolist(),
            'special_numbers_20': top_30_indices[:20].tolist(),
            'special_numbers_30': top_30_indices.tolist(),
            'confidence': float(np.max(probabilities))
        }

        return result

    def _calculate_probabilities(self, predictions: np.ndarray) -> np.ndarray:
        """计算每个号码的概率"""
        # 将预测值转换为概率分布
        probabilities = np.zeros(49)  # 1-49的号码
        for pred in predictions:
            # 使用高斯分布将预测值转换为概率
            mu = pred[0]
            sigma = 2.0
            for i in range(49):
                prob = np.exp(-((i+1 - mu)**2)/(2*sigma**2))
                probabilities[i] += prob

        # 归一化
        probabilities = probabilities / np.sum(probabilities)

        return probabilities

    def calculate_confidence(self, X: np.ndarray) -> float:
        """计算预测置信度"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        predictions = self.model.predict(X)
        probabilities = self._calculate_probabilities(predictions)

        # 使用最高概率作为置信度
        confidence = float(np.max(probabilities))

        return confidence
