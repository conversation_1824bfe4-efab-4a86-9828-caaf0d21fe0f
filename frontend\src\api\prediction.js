import request from '@/utils/request'

// 获取单个预测
export function getPrediction(id) {
  // 确保id是字符串
  const expectId = typeof id === 'object' && id.expect ? id.expect : id
  console.log('调用getPrediction API，期号:', expectId)
  return request({
    url: `/api/prediction/predictions/${expectId}`,
    method: 'get'
  })
}

// 创建预测
export function createPrediction(data) {
  return request({
    url: '/api/prediction/',
    method: 'post',
    data
  })
}

// 更新预测
export function updatePrediction(id, data) {
  return request({
    url: `/api/prediction/${id}`,
    method: 'put',
    data
  })
}

// 删除预测
export function deletePrediction(id) {
  return request({
    url: `/api/prediction/${id}`,
    method: 'delete'
  })
}

// 获取预测历史
export function getPredictionHistory(params) {
  return request({
    url: '/api/prediction/predictions',
    method: 'get',
    params: {
      limit: params.pageSize || 10,
      offset: (params.page - 1) * (params.pageSize || 10) || 0
    }
  })
}

// 获取预测准确率统计
export function getPredictionAccuracy() {
  return request({
    url: '/api/prediction/accuracy',
    method: 'get'
  })
}

// 生成新的预测
export function generatePrediction(data) {
  return request({
    url: '/api/prediction/generate',
    method: 'post',
    data
  })
}

/**
 * 预测下一期结果
 * @param {string} expect - 期号
 * @param {string} model - 模型类型
 * @param {object} options - 高级选项
 * @returns {Promise} - 预测结果
 */
export const predictNext = async (expect, model, options) => {
  console.log(`调用预测API: 期号=${expect}, 模型=${model}, 选项=`, options)
  return request({
    url: `/api/prediction/predict/${expect}`,
    method: 'post',
    data: { model, options },
    timeout: 30000  // 增加超时时间到30秒
  })
}

/**
 * 异步训练模型
 * @param {object} params - 训练参数
 * @returns {Promise} - 训练任务ID
 */
export const trainModelAsync = async (params) => {
  return request({
    url: '/api/advanced-prediction/train-async',
    method: 'post',
    data: params,
    timeout: 10000  // 快速返回任务ID
  })
}

/**
 * 获取模型训练状态
 * @param {string} taskId - 训练任务ID
 * @returns {Promise} - 训练状态
 */
export const getTrainingStatus = async (taskId) => {
  return request({
    url: `/api/advanced-prediction/training-status/${taskId}`,
    method: 'get'
  })
}

/**
 * 训练高级预测模型
 * @param {Object} params - 训练参数
 * @param {number} params.days - 训练数据天数
 * @param {boolean} params.force - 是否强制重新训练
 * @param {Array} params.models - 要训练的模型类型
 * @returns {Promise} - 训练结果
 */
export const trainAdvancedModel = async (params) => {
  return request({
    url: '/api/advanced-prediction/train',
    method: 'post',
    data: params,
    timeout: 60000  // 将超时时间从15000ms增加到60000ms（1分钟）
  })
}

// 运行模型训练
export function trainModel(data) {
  return request({
    url: '/api/prediction/train',
    method: 'post',
    data,
    timeout: 60000  // 增加超时时间至60秒
  })
}

// 批量训练模型
export function trainModels(params) {
  return request({
    url: '/api/prediction/train-batch',
    method: 'post',
    data: params,
    timeout: 60000  // 增加超时时间至60秒
  })
}

// 获取训练历史
export function getTrainingHistory(params) {
  return request({
    url: '/api/prediction/training-history',
    method: 'get',
    params: {
      page: params.page || 1,
      page_size: params.pageSize || 10
    }
  })
}

// 获取回测历史
export function getBacktestHistory(params) {
  return request({
    url: '/api/prediction/backtest-history',
    method: 'get',
    params: {
      page: params.page || 1,
      page_size: params.pageSize || 10
    }
  })
}

// 运行回测
export function runBacktest(data) {
  return request({
    url: '/api/prediction/backtest',
    method: 'post',
    data
  })
}

// 获取单个回测结果
export function getBacktestResult(id) {
  return request({
    url: `/api/prediction/backtest/${id}`,
    method: 'get'
  })
}

// 获取模型参数
export function getModelParameters() {
  return request({
    url: '/api/prediction/model-parameters',
    method: 'get'
  })
}

// 更新模型参数
export function updateModelParameters(data) {
  return request({
    url: '/api/prediction/model-parameters',
    method: 'put',
    data
  })
}

// 获取模型状态
export function getModelStatus() {
  return request({
    url: '/api/prediction/model-status',
    method: 'get'
  })
}

// 评估预测结果
export function evaluatePrediction(expect, actualResult) {
  return request({
    url: `/api/prediction/predictions/${expect}/evaluate`,
    method: 'post',
    data: { actual_result: actualResult }
  })
}

// 获取下一期期号
export function getNextExpect() {
  return request({
    url: '/api/prediction/next-expect',
    method: 'get'
  })
}

// 获取预测分析数据
export function getPredictionAnalysis(params) {
  return request({
    url: '/api/prediction/analysis',
    method: 'get',
    params
  })
}

// 简化版本，使用上面的函数
export function getPredictions(params) {
  return getPredictionHistory(params)
}

// 获取训练数据可视化信息
export function getTrainingDataVisualization(trainingId) {
  return request({
    url: `/api/prediction/training-data-visualization/${trainingId}`,
    method: 'get'
  })
}

// 统一错误处理的API包装器
export function safeApiCall(apiFunction, ...args) {
  return apiFunction(...args).catch(error => {
    console.error('API调用失败:', error);
    // 返回一个模拟结果，避免前端崩溃
    return {
      data: {
        message: '数据获取失败，请稍后再试',
        status: 'error',
        error: error.message
      }
    };
  });
}