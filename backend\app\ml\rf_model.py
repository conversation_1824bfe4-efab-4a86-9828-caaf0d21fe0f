import numpy as np
from typing import Dict, List, Any, <PERSON><PERSON>
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from .base_model import BaseModel


class RandomForestModel(BaseModel):
    def __init__(self):
        super().__init__("random_forest")
        self.sequence_length = 10  # 使用前10期数据预测
        self.scaler = StandardScaler()

    def preprocess_data(self, data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """预处理数据"""
        features = []
        targets = []

        for i in range(len(data) - self.sequence_length):
            sequence = data[i:i + self.sequence_length]
            target = data[i + self.sequence_length]['special_number']

            # 构建特征向量
            feature_vector = []
            for item in sequence:
                feature_vector.extend([
                    item['special_number'],
                    1 if item['special_odd_even'] == '单' else 0,
                    1 if item['special_big_small'] == '大' else 0,
                    1 if item['special_color'] == '红波' else (
                        2 if item['special_color'] == '蓝波' else 0),
                ])

            features.append(feature_vector)
            targets.append(target)

        X = np.array(features)
        y = np.array(targets)

        # 标准化特征
        X = self.scaler.fit_transform(X)

        return X, y

    def train(self, X: np.ndarray, y: np.ndarray, params: Dict[str, Any] = None) -> Dict[str, float]:
        """训练模型"""
        if params is None:
            params = {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 2,
                'min_samples_leaf': 1
            }

        self.model = RandomForestRegressor(
            n_estimators=params['n_estimators'],
            max_depth=params['max_depth'],
            min_samples_split=params['min_samples_split'],
            min_samples_leaf=params['min_samples_leaf'],
            random_state=42
        )

        self.model.fit(X, y)

        # 计算训练指标
        train_predictions = self.model.predict(X)
        mse = np.mean((y - train_predictions) ** 2)
        mae = np.mean(np.abs(y - train_predictions))

        metrics = {
            'mse': float(mse),
            'mae': float(mae),
            'r2_score': float(self.model.score(X, y))
        }

        return metrics

    def predict(self, X: np.ndarray) -> Dict[str, Any]:
        """预测结果"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        # 标准化输入数据
        X = self.scaler.transform(X)

        # 获取预测值和特征重要性
        predictions = self.model.predict(X)
        feature_importance = self.model.feature_importances_

        # 计算每个号码的概率
        probabilities = self._calculate_probabilities(
            predictions, feature_importance)

        # 获取前30个最可能的号码
        top_30_indices = np.argsort(probabilities)[-30:][::-1]

        result = {
            'special_numbers_5': top_30_indices[:5].tolist(),
            'special_numbers_10': top_30_indices[:10].tolist(),
            'special_numbers_15': top_30_indices[:15].tolist(),
            'special_numbers_20': top_30_indices[:20].tolist(),
            'special_numbers_30': top_30_indices.tolist(),
            'confidence': float(np.max(probabilities))
        }

        return result

    def _calculate_probabilities(self, predictions: np.ndarray, feature_importance: np.ndarray) -> np.ndarray:
        """计算每个号码的概率"""
        probabilities = np.zeros(49)  # 1-49的号码

        for pred, importance in zip(predictions, feature_importance):
            # 使用高斯分布和特征重要性计算概率
            mu = pred
            sigma = 2.0 / (importance + 1e-6)  # 避免除零
            for i in range(49):
                prob = np.exp(-((i+1 - mu)**2)/(2*sigma**2))
                probabilities[i] += prob * importance

        # 归一化
        probabilities = probabilities / np.sum(probabilities)

        return probabilities

    def calculate_confidence(self, X: np.ndarray) -> float:
        """计算预测置信度"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        X = self.scaler.transform(X)
        predictions = self.model.predict(X)
        feature_importance = self.model.feature_importances_
        probabilities = self._calculate_probabilities(
            predictions, feature_importance)

        # 使用最高概率作为置信度
        confidence = float(np.max(probabilities))

        return confidence
