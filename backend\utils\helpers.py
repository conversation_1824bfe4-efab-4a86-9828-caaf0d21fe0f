def get_number_attributes(number):
    """获取号码的属性（颜色、生肖等）"""
    number = int(number)
    
    # 颜色判断
    red_numbers = [1,2,7,8,12,13,18,19,23,24,29,30,34,35,40,45,46]
    blue_numbers = [3,4,9,10,14,15,20,25,26,31,36,37,41,42,47,48]
    
    if number in red_numbers:
        color = '红波'
    elif number in blue_numbers:
        color = '蓝波'
    else:
        color = '绿波'
    
    # 单双判断
    odd_even = '单' if number % 2 else '双'
    
    # 大小判断
    size = '大' if number > 24 else '小'
    
    # 生肖判断（简化版）
    zodiac_map = {
        0: '鼠', 1: '牛', 2: '虎', 3: '兔',
        4: '龙', 5: '蛇', 6: '马', 7: '羊',
        8: '猴', 9: '鸡', 10: '狗', 11: '猪'
    }
    zodiac = zodiac_map[number % 12]
    
    return {
        'color': color,
        'oddEven': odd_even,
        'size': size,
        'zodiac': zodiac
    } 

