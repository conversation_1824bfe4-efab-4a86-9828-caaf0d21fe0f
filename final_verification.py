#!/usr/bin/env python3
"""
最终验证脚本 - 验证所有修复是否成功
"""
import requests
import json


def test_api_endpoints():
    """测试所有API端点"""
    base_url = "http://localhost:8000"

    print("=== API端点测试 ===")

    # 1. 测试最新开奖记录
    print("\n1. 测试最新开奖记录 (/api/draw/latest)")
    try:
        response = requests.get(f"{base_url}/api/draw/latest")
        if response.status_code == 200:
            data = response.json()
            if data['data']:
                latest = data['data'][0]
                print(f"   ✅ 最新期号: {latest['expect']}")
                print(f"   ✅ 特码: {latest['special_number']}")
                print(f"   ✅ 开奖号码: {latest['open_code']}")
            else:
                print("   ❌ 没有返回数据")
        else:
            print(f"   ❌ API调用失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

    # 2. 测试下期开奖信息
    print("\n2. 测试下期开奖信息 (/api/draw/next)")
    try:
        response = requests.get(f"{base_url}/api/draw/next")
        if response.status_code == 200:
            data = response.json()
            next_info = data['data']
            print(f"   ✅ 下期期号: {next_info['expect']}")
            print(f"   ✅ 预计开奖时间: {next_info['draw_time']}")
        else:
            print(f"   ❌ API调用失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

    # 3. 测试历史记录
    print("\n3. 测试历史记录 (/api/draw/history)")
    try:
        response = requests.get(
            f"{base_url}/api/draw/history?page=1&page_size=5")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 总记录数: {data['total']}")
            print(f"   ✅ 返回记录数: {len(data['data'])}")
            if data['data']:
                first_record = data['data'][0]
                print(f"   ✅ 第一条记录期号: {first_record['expect']}")
        else:
            print(f"   ❌ API调用失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")


def check_data_consistency():
    """检查数据一致性"""
    print("\n=== 数据一致性检查 ===")

    try:
        # 获取最新记录
        response = requests.get(
            "http://localhost:8000/api/draw/latest?limit=1")
        latest_data = response.json()['data'][0]

        # 获取下期信息
        response = requests.get("http://localhost:8000/api/draw/next")
        next_data = response.json()['data']

        # 检查期号连续性
        latest_expect = int(latest_data['expect'])
        next_expect = int(next_data['expect'])

        if next_expect == latest_expect + 1:
            print(f"   ✅ 期号连续性正确: {latest_expect} -> {next_expect}")
        else:
            print(f"   ❌ 期号连续性错误: {latest_expect} -> {next_expect}")

        # 检查开奖号码格式
        open_code = latest_data['open_code']
        numbers = open_code.split(',')
        if len(numbers) == 7:
            print(f"   ✅ 开奖号码格式正确: 7个号码")
            special_number = int(numbers[-1])
            if special_number == latest_data['special_number']:
                print(f"   ✅ 特码一致: {special_number}")
            else:
                print(
                    f"   ❌ 特码不一致: {special_number} vs {latest_data['special_number']}")
        else:
            print(f"   ❌ 开奖号码格式错误: {len(numbers)}个号码")

    except Exception as e:
        print(f"   ❌ 数据一致性检查失败: {e}")


def check_2025_data_completeness():
    """检查2025年数据完整性"""
    print("\n=== 2025年数据完整性检查 ===")

    try:
        response = requests.get(
            "http://localhost:8000/api/draw/history?page=1&page_size=1000")
        data = response.json()

        # 筛选2025年数据
        all_records = data.get('data', [])
        records_2025 = [
            record for record in all_records if record['expect'].startswith('2025')]

        print(f"   ✅ 2025年总记录数: {len(records_2025)}")

        if records_2025:
            expects = [record['expect'] for record in records_2025]
            expects.sort()

            min_expect = expects[0]
            max_expect = expects[-1]
            print(f"   ✅ 期号范围: {min_expect} - {max_expect}")

            # 检查期号连续性
            expect_nums = [int(expect[4:]) for expect in expects]
            missing = []
            for i in range(min(expect_nums), max(expect_nums) + 1):
                if i not in expect_nums:
                    missing.append(f'2025{i:03d}')

            if missing:
                print(
                    f"   ❌ 缺失期号: {missing[:10]}{'...' if len(missing) > 10 else ''}")
            else:
                print(f"   ✅ 期号连续，无缺失")

    except Exception as e:
        print(f"   ❌ 2025年数据检查失败: {e}")


def main():
    """主函数"""
    print("开始最终验证...")

    test_api_endpoints()
    check_data_consistency()
    check_2025_data_completeness()

    print("\n=== 验证完成 ===")
    print("如果所有项目都显示 ✅，说明修复成功！")
    print("如果有 ❌ 项目，请检查相应的问题。")


if __name__ == "__main__":
    main()
