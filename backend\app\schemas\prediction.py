from pydantic import BaseModel, Field, conlist
from typing import List, Dict, Any, Optional
from datetime import datetime


class ModelConfig(BaseModel):
    hidden_size: int = Field(128, description="LSTM隐藏层大小")
    epochs: int = Field(100, description="训练轮数")
    batch_size: int = Field(32, description="批次大小")
    learning_rate: float = Field(0.01, description="学习率")


class TrainingParams(BaseModel):
    lstm: ModelConfig
    rf: dict = Field(
        default={
            "n_estimators": 100,
            "max_depth": 10,
            "random_state": 42
        },
        description="随机森林参数"
    )
    xgboost: dict = Field(
        default={
            "n_estimators": 100,
            "max_depth": 6,
            "learning_rate": 0.1,
            "random_state": 42
        },
        description="XGBoost参数"
    )
    gbdt: dict = Field(
        default={
            "n_estimators": 100,
            "max_depth": 5,
            "learning_rate": 0.1,
            "random_state": 42
        },
        description="GBDT参数"
    )


class PredictionRequest(BaseModel):
    use_historical_data: bool = Field(True, description="是否使用历史数据")
    custom_data: Optional[List[int]] = Field(None, description="自定义数据")
    range_size: Optional[int] = Field(30, description="预测范围大小")
    expect: Optional[str] = Field(None, description="预测期号，如果为空则预测下一期")


class TrainingRequest(BaseModel):
    use_historical_data: bool = Field(True, description="是否使用历史数据")
    custom_data: Optional[List[int]] = Field(None, description="自定义数据")
    parameters: Optional[Dict[str, Any]] = Field(None, description="模型参数")


class TrainingResponse(BaseModel):
    status: str
    training_result: Dict[str, Any]
    message: str


class PredictionAttributes(BaseModel):
    special_odd_even: str
    special_big_small: str
    special_color: str
    special_animal_type: str
    special_element: str
    special_tail_big_small: str
    special_sum_odd_even: str


class ConfidenceScores(BaseModel):
    lstm: Optional[float] = None
    rf: Optional[float] = None
    xgboost: Optional[float] = None
    gbdt: Optional[float] = None
    combined: Optional[float] = None


class PredictionResponse(BaseModel):
    status: str
    prediction: Dict[str, Any]
    message: str


class PredictionHistoryItem(BaseModel):
    expect: str
    prediction_time: datetime
    special_numbers_5: List[int]
    special_numbers_10: List[int]
    attribute_predictions: Dict[str, str]
    zodiac_3: List[str]
    actual_result: Optional[int] = None
    accuracy: Optional[float] = None


class PredictionHistoryResponse(BaseModel):
    total: int
    items: List[PredictionHistoryItem]


class PredictionAnalysis(BaseModel):
    statistics: Dict[str, Any]
    model_performance: List[Dict[str, Any]]
    hot_numbers: List[Dict[str, Any]]
    zodiac_accuracy: List[Dict[str, Any]]


class BacktestParams(BaseModel):
    start_date: str
    end_date: str
    models: List[str] = Field(default=["lstm", "rf", "xgboost"])
    prediction_range: int = Field(5, ge=5, le=30)


class BacktestResponse(BaseModel):
    totalPredictions: int
    hits: int
    averageAccuracy: float
    roi: float
    modelPerformance: List[Dict[str, Any]]
    records: List[Dict[str, Any]]


class PredictionBase(BaseModel):
    expect: str
    predicted_numbers: conlist(int, min_length=6, max_length=6)
    confidence: float
    model_version: str
    prediction_time: datetime
    is_correct: Optional[bool] = None


class PredictionCreate(PredictionBase):
    pass


class PredictionResponse(PredictionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
