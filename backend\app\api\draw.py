from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from sqlalchemy import and_, or_
import logging
import pandas as pd
import os
from fastapi.responses import FileResponse
from collections import defaultdict

from ..database import get_db
from ..models import Draw
from ..schemas.draw import DrawResponse, DrawList
from ..utils.game_rules import GameRules2025
from ..utils.statistics import calculate_statistics

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/latest")
def get_latest_draws(limit: int = 5, db: Session = Depends(get_db)):
    """获取最新开奖结果"""
    draws = db.query(Draw).order_by(Draw.expect.desc()).limit(limit).all()
    if not draws:
        raise HTTPException(status_code=404, detail="No draw data found")
    return draws  # 始终返回数组


@router.get("/history")
async def get_history_draws(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    number: Optional[str] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取历史开奖记录"""
    try:
        query = db.query(Draw)
        conditions = []

        # 期号范围
        if start_period:
            conditions.append(Draw.expect >= start_period)
        if end_period:
            conditions.append(Draw.expect <= end_period)

        # 日期范围
        if start_date:
            conditions.append(Draw.open_time >= f"{start_date} 00:00:00")
        if end_date:
            conditions.append(Draw.open_time <= f"{end_date} 23:59:59")

        # 特码号码
        if number:
            conditions.append(Draw.open_code.like(f"%,{number}"))  # 确保是最后一个号码

        # 特码属性筛选
        if any([zodiac, color, odd_even, big_small, tail_big_small, sum_odd_even, animal_type, wuxing]):
            draws = db.query(Draw).all()
            filtered_ids = []

            for draw in draws:
                # 获取特码（最后一个号码）
                special_number = int(draw.open_code.split(',')[-1])
                # 获取特码的属性
                attrs = GameRules2025.get_number_attributes(special_number)

                # 只检查特码的属性
                match = True
                if zodiac and attrs['zodiac'] != zodiac:  # 特码生肖
                    match = False
                if color:  # 特码波色
                    # 前端传来的可能是"红"、"蓝"、"绿"，也可能是"红波"、"蓝波"、"绿波"
                    backend_color = attrs['color'].lower().replace('波', '')
                    frontend_color = color.lower().replace('波', '')
                    if backend_color != frontend_color:
                        match = False
                if odd_even:  # 特码单双
                    backend_odd_even = '单' if '单' in attrs[
                        'oddEven'] else '双' if '双' in attrs['oddEven'] else attrs['oddEven']
                    if backend_odd_even != odd_even:
                        match = False
                if big_small:  # 特码大小
                    backend_big_small = '大' if '大' in attrs[
                        'bigSmall'] else '小' if '小' in attrs['bigSmall'] else attrs['bigSmall']
                    if backend_big_small != big_small:
                        match = False
                if tail_big_small:  # 特码尾数大小
                    backend_tail = '大' if '大' in attrs['tailBigSmall'] else '小' if '小' in attrs[
                        'tailBigSmall'] else attrs['tailBigSmall']
                    if backend_tail != tail_big_small:
                        match = False
                if sum_odd_even:  # 特码合数单双
                    backend_sum = '单' if '单' in attrs['sumOddEven'] else '双' if '双' in attrs[
                        'sumOddEven'] else attrs['sumOddEven']
                    if backend_sum != sum_odd_even:
                        match = False
                if animal_type:  # 特码家野
                    backend_type = '家' if '家' in attrs['animalType'] else '野' if '野' in attrs[
                        'animalType'] else attrs['animalType']
                    if backend_type != animal_type:
                        match = False
                if wuxing:  # 特码五行
                    # 确保大小写和空格不影响匹配
                    backend_wuxing = attrs['wuxing'].strip().lower()
                    frontend_wuxing = wuxing.strip().lower()
                    if backend_wuxing != frontend_wuxing:
                        match = False

                if match:
                    filtered_ids.append(draw.id)

            if filtered_ids:
                conditions.append(Draw.id.in_(filtered_ids))
            else:
                return {
                    "code": 200,
                    "message": "success",
                    "data": [],
                    "total": 0
                }

        # 应用所有查询条件
        if conditions:
            query = query.filter(and_(*conditions))

        # 计算总数
        total = query.count()

        # 分页
        query = query.order_by(Draw.expect.desc())
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 获取结果
        draws = query.all()

        # 转换结果
        result = []
        for draw in draws:
            draw_dict = draw.to_dict()
            # 添加特码属性
            special_number = int(draw.open_code.split(',')[-1])
            draw_dict['special_number'] = special_number
            # 获取特码的所有属性
            attrs = GameRules2025.get_number_attributes(special_number)
            # 添加特码属性到返回数据中
            draw_dict['special_attributes'] = {
                'zodiac': attrs['zodiac'],
                'color': attrs['color'],
                'oddEven': '单' if '单' in attrs['oddEven'] else '双',
                'bigSmall': '大' if '大' in attrs['bigSmall'] else '小',
                'tailBigSmall': '尾大' if '大' in attrs['tailBigSmall'] else '尾小',
                'sumOddEven': '合单' if '单' in attrs['sumOddEven'] else '合双',
                'animalType': '家禽' if '家' in attrs['animalType'] else '野兽',
                'wuxing': attrs['wuxing']
            }
            result.append(draw_dict)

        return {
            "code": 200,
            "message": "success",
            "data": result,
            "total": total
        }
    except Exception as e:
        logger.error(f"Error fetching history draws: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/next")
def get_next_draw(db: Session = Depends(get_db)):
    """获取下一期开奖信息"""
    latest = db.query(Draw).order_by(Draw.expect.desc()).first()
    if not latest:
        raise HTTPException(status_code=404, detail="No draw data found")

    # 计算下一期期号
    current_period = int(latest.expect)
    next_period = str(current_period + 1)

    # 计算下一期开奖时间（当前期开奖时间 + 1天）
    next_draw_time = latest.open_time + timedelta(days=1)

    return {
        "period": next_period,
        "drawTime": next_draw_time.strftime("%Y-%m-%d %H:%M:%S")
    }


@router.get("/export")
async def export_draws(
    start_period: Optional[str] = None,
    end_period: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    number: Optional[str] = None,
    zodiac: Optional[str] = None,
    color: Optional[str] = None,
    odd_even: Optional[str] = None,
    big_small: Optional[str] = None,
    tail_big_small: Optional[str] = None,
    sum_odd_even: Optional[str] = None,
    animal_type: Optional[str] = None,
    wuxing: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """导出历史开奖记录"""
    try:
        # 创建临时目录（使用绝对路径）
        temp_dir = os.path.join(os.path.dirname(
            os.path.dirname(__file__)), 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        # 生成临时文件路径
        filename = f"lottery_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join(temp_dir, filename)

        # 查询数据
        query = db.query(Draw)
        conditions = []

        # 基础筛选条件
        if start_period:
            conditions.append(Draw.expect >= start_period)
        if end_period:
            conditions.append(Draw.expect <= end_period)
        if start_date:
            conditions.append(Draw.open_time >= f"{start_date} 00:00:00")
        if end_date:
            conditions.append(Draw.open_time <= f"{end_date} 23:59:59")
        if number:
            conditions.append(Draw.open_code.like(
                f"%,{number}"))  # 确保是最后一个号码（特码）

        # 应用基础筛选条件
        if conditions:
            query = query.filter(and_(*conditions))

        # 获取所有符合基础条件的记录
        draws = query.order_by(Draw.expect.desc()).all()

        # 如果没有数据，直接返回404
        if not draws:
            raise HTTPException(status_code=404, detail="No data found")

        # 进行特码属性筛选
        filtered_draws = []
        for draw in draws:
            # 获取特码（最后一个号码）
            special_number = int(draw.open_code.split(',')[-1])
            # 获取特码的属性
            attrs = GameRules2025.get_number_attributes(special_number)

            # 只检查特码的属性
            match = True
            if zodiac and attrs['zodiac'] != zodiac:  # 特码生肖
                match = False
            if color:  # 特码波色
                # 前端传来的可能是"红"、"蓝"、"绿"，也可能是"红波"、"蓝波"、"绿波"
                backend_color = attrs['color'].lower().replace('波', '')
                frontend_color = color.lower().replace('波', '')
                if backend_color != frontend_color:
                    match = False
            if odd_even:  # 特码单双
                backend_odd_even = '单' if '单' in attrs['oddEven'] else '双' if '双' in attrs['oddEven'] else attrs['oddEven']
                if backend_odd_even != odd_even:
                    match = False
            if big_small:  # 特码大小
                backend_big_small = '大' if '大' in attrs[
                    'bigSmall'] else '小' if '小' in attrs['bigSmall'] else attrs['bigSmall']
                if backend_big_small != big_small:
                    match = False
            if tail_big_small:  # 特码尾数大小
                backend_tail = '大' if '大' in attrs['tailBigSmall'] else '小' if '小' in attrs[
                    'tailBigSmall'] else attrs['tailBigSmall']
                if backend_tail != tail_big_small:
                    match = False
            if sum_odd_even:  # 特码合数单双
                backend_sum = '单' if '单' in attrs['sumOddEven'] else '双' if '双' in attrs['sumOddEven'] else attrs['sumOddEven']
                if backend_sum != sum_odd_even:
                    match = False
            if animal_type:  # 特码家野
                backend_type = '家' if '家' in attrs['animalType'] else '野' if '野' in attrs[
                    'animalType'] else attrs['animalType']
                if backend_type != animal_type:
                    match = False
            if wuxing:  # 特码五行
                # 确保大小写和空格不影响匹配
                backend_wuxing = attrs['wuxing'].strip().lower()
                frontend_wuxing = wuxing.strip().lower()
                if backend_wuxing != frontend_wuxing:
                    match = False

            # 如果所有条件都匹配，添加到结果中
            if match:
                numbers = [int(n) for n in draw.open_code.split(',')]
                odd_count = sum(1 for n in numbers if n % 2 != 0)
                big_count = sum(1 for n in numbers if n >= 25)

                filtered_draws.append({
                    "期号": draw.expect,
                    "开奖时间": draw.open_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "开奖号码": draw.open_code,
                    "特码": special_number,
                    "特码生肖": attrs["zodiac"],
                    "特码波色": attrs["color"],
                    "特码单双": attrs["oddEven"],
                    "特码大小": attrs["bigSmall"],
                    "特码尾数": attrs["tailBigSmall"],
                    "特码合数": attrs["sumOddEven"],
                    "特码属性": attrs["animalType"],
                    "特码五行": attrs["wuxing"],
                    "总单双比": f"{odd_count}:{len(numbers)-odd_count}",
                    "总大小比": f"{big_count}:{len(numbers)-big_count}"
                })

        # 如果筛选后没有数据，返回404
        if not filtered_draws:
            raise HTTPException(
                status_code=404, detail="No data found matching the filters")

        # 创建DataFrame并导出到Excel
        df = pd.DataFrame(filtered_draws)
        df.to_excel(filepath, index=False, engine='openpyxl')

        # 返回文件
        return FileResponse(
            filepath,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            background=None  # 确保同步返回
        )

    except Exception as e:
        logger.error(f"Error exporting draws: {str(e)}")
        if 'filepath' in locals() and os.path.exists(filepath):
            try:
                os.remove(filepath)
            except Exception as cleanup_error:
                logger.error(
                    f"Error cleaning up temp file: {str(cleanup_error)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    year: Optional[int] = None,  # 新增年份筛选
    start_period: Optional[str] = None,  # 新增期数范围筛选
    end_period: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取开奖统计数据"""
    try:
        query = db.query(Draw)
        conditions = []

        # 年份筛选
        if year:
            conditions.append(Draw.expect.like(f"{year}%"))

        # 期数范围筛选
        if start_period:
            conditions.append(Draw.expect >= start_period)
        if end_period:
            conditions.append(Draw.expect <= end_period)

        # 日期范围筛选
        if start_date:
            conditions.append(Draw.open_time >= f"{start_date} 00:00:00")
        if end_date:
            conditions.append(Draw.open_time <= f"{end_date} 23:59:59")

        if conditions:
            query = query.filter(and_(*conditions))

        draws = query.order_by(Draw.expect.desc()).all()

        logger.info(f"Statistics query returned {len(draws)} records")

        if not draws:
            # 返回空数据的默认结构
            empty_stats = {
                "basicStats": {
                    "totalCount": 0,
                    "hotNumber": None,
                    "hotNumberCount": 0,
                    "coldNumber": None,
                    "coldNumberCount": 0,
                    "averageInterval": 0
                },
                "numberFrequency": {str(i): 0 for i in range(1, 50)},
                "colorFrequency": {'红波': 0, '蓝波': 0, '绿波': 0},
                "tailFrequency": {str(i): 0 for i in range(10)},
                "headFrequency": {str(i): 0 for i in range(5)},
                "zodiacFrequency": {'鼠': 0, '牛': 0, '虎': 0, '兔': 0, '龙': 0, '蛇': 0, '马': 0, '羊': 0, '猴': 0, '鸡': 0, '狗': 0, '猪': 0},
                "wuxingFrequency": {'金': 0, '木': 0, '水': 0, '火': 0, '土': 0},
                "attributes": {
                    "单": 0, "双": 0, "大": 0, "小": 0,
                    "家禽": 0, "野兽": 0,
                    "尾单": 0, "尾双": 0,
                    "尾大": 0, "尾小": 0,
                    "合单": 0, "合双": 0,
                    "合大": 0, "合小": 0
                },
                "missing": {
                    "current": {str(i): 0 for i in range(1, 50)},
                    "max": {str(i): 0 for i in range(1, 50)},
                    "lastAppearance": {str(i): None for i in range(1, 50)}
                }
            }

            return {
                "code": 200,
                "message": "success",
                "data": empty_stats
            }

        # 计算统计数据
        stats = calculate_statistics(draws)

        # 确保所有必要的字段都存在
        if 'basicStats' not in stats:
            stats['basicStats'] = {
                "totalCount": len(draws),
                "hotNumber": None,
                "hotNumberCount": 0,
                "coldNumber": None,
                "coldNumberCount": 0,
                "averageInterval": 0
            }

        # 确保 numberFrequency 字段存在
        if 'numberFrequency' not in stats:
            stats['numberFrequency'] = {str(i): 0 for i in range(1, 50)}

        # 确保 colorFrequency 字段存在
        if 'colorFrequency' not in stats:
            stats['colorFrequency'] = {'红波': 0, '蓝波': 0, '绿波': 0}

        # 确保 tailFrequency 字段存在
        if 'tailFrequency' not in stats:
            stats['tailFrequency'] = {str(i): 0 for i in range(10)}

        # 确保 headFrequency 字段存在
        if 'headFrequency' not in stats:
            stats['headFrequency'] = {str(i): 0 for i in range(5)}

        # 确保 zodiacFrequency 字段存在
        if 'zodiacFrequency' not in stats:
            stats['zodiacFrequency'] = {'鼠': 0, '牛': 0, '虎': 0, '兔': 0,
                                        '龙': 0, '蛇': 0, '马': 0, '羊': 0, '猴': 0, '鸡': 0, '狗': 0, '猪': 0}

        # 确保 wuxingFrequency 字段存在
        if 'wuxingFrequency' not in stats:
            stats['wuxingFrequency'] = {'金': 0, '木': 0, '水': 0, '火': 0, '土': 0}

        # 确保 attributes 字段存在
        if 'attributes' not in stats:
            stats['attributes'] = {
                "单": 0, "双": 0, "大": 0, "小": 0,
                "家禽": 0, "野兽": 0,
                "尾单": 0, "尾双": 0,
                "尾大": 0, "尾小": 0,
                "合单": 0, "合双": 0,
                "合大": 0, "合小": 0
            }

        # 移除 number_distribution 字段，避免验证错误
        if 'number_distribution' in stats:
            del stats['number_distribution']

        logger.info(
            f"Statistics calculated successfully with {len(stats.get('numberFrequency', {}))} number entries")

        return {
            "code": 200,
            "message": "success",
            "data": stats
        }

    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
