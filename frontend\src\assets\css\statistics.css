/* 统计卡片增强样式 */
.stat-card {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e8eaed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff4d4f, #1890ff, #52c41a, #faad14);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #d9d9d9;
}

.stat-card .card-header {
  background: transparent;
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
}

.stat-card .stat-content {
  padding: 20px;
}

.stat-card .stat-icon {
  background: linear-gradient(135deg, var(--icon-color, #409EFF) 0%, var(--icon-color-light, #66b3ff) 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.stat-card .stat-value {
  background: linear-gradient(135deg, #333 0%, #666 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-desc {
  color: #8c8c8c;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 特码综合分析表格样式 */
.filter-buttons {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  text-align: center;
}

.filter-buttons .el-button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-buttons .el-button {
  margin: 5px 2px;
  flex: 0 0 auto;
}

.table-controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  flex-wrap: wrap;
}
.number-cell {
  font-weight: bold;
  font-size: 16px;
}

.count-cell, .missing-cell, .max-missing-cell {
  font-weight: 500;
}

.unit {
  font-size: 12px;
  color: #999;
  margin-left: 2px;
}

.zodiac-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.zodiac-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.zodiac-text {
  font-size: 12px;
}

/* 走势点样式 */
.trend-dots {
  display: flex;
  justify-content: center;
  gap: 4px;
  height: 20px;
}

.trend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  border: 1px solid #ddd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.trend-dot:hover {
  transform: scale(1.2);
}

.trend-dot.miss {
  background-color: #f0f0f0;
}

.trend-dot.hit {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
}

.trend-dot.hit.red {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  box-shadow: 0 0 5px rgba(255, 77, 79, 0.5);
}

.trend-dot.hit.blue {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
}

.trend-dot.hit.green {
  background-color: #52c41a;
  border-color: #52c41a;
  box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
}

/* 热度指数样式 */
.hot-index-value {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: block;
  text-align: center;
}

.color-tag, .element-tag {
  width: 100%;
  text-align: center;
}

.tail-number {
  font-weight: bold;
  font-size: 16px;
  color: #1890ff;
}

.sum-digits {
  font-weight: bold;
  font-size: 16px;
  color: #722ed1;
}

/* 号码详情对话框样式 */
.number-detail {
  padding: 10px;
}

.number-detail h3 {
  text-align: center;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.number-detail h4 {
  margin: 15px 0 10px;
  padding-bottom: 5px;
  border-bottom: 2px solid #f0f0f0;
  color: #1890ff;
  font-size: 16px;
}

.detail-section {
  margin-bottom: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  border-radius: 4px;
}

.detail-item .label {
  font-weight: bold;
  color: #666;
}

.detail-item .value {
  font-weight: 500;
}

/* 走势图对话框样式 */
.number-trend-dialog .el-message-box__message {
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.number-trend-dialog .el-message-box__content {
  padding: 0;
}

.number-trend-dialog .el-message-box {
  width: 90%;
  max-width: 1000px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.number-trend-dialog .el-message-box__header {
  padding: 15px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.number-trend-dialog .el-message-box__title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.number-trend-dialog .el-message-box__headerbtn {
  top: 15px;
  right: 20px;
}

.number-trend-dialog .el-message-box__headerbtn .el-message-box__close {
  color: #909399;
  font-size: 18px;
}

.number-trend-dialog .el-button--primary {
  background-color: #1890ff;
  border-color: #1890ff;
  padding: 10px 20px;
  font-size: 14px;
}

/* 自定义号码详情对话框样式 */
.number-detail-dialog .el-message-box__content {
  padding: 20px;
}

.number-detail-dialog .el-message-box__header {
  padding: 15px 20px;
  background-color: #f5f7fa;
}

.number-detail-dialog .el-message-box__title {
  font-size: 18px;
  font-weight: bold;
}

.number-detail-dialog .el-message-box__headerbtn {
  top: 15px;
}
