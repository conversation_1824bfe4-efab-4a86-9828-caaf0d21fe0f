/* 增强统计卡片样式 */
.enhanced-stat-card {
  position: relative;
  height: 140px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.enhanced-stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--card-color);
}

.enhanced-stat-card .card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  transition: all 0.4s ease;
}

.enhanced-stat-card:hover .card-background {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    color-mix(in srgb, var(--card-color) 5%, white) 100%);
}

.enhanced-stat-card .card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.enhanced-stat-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.enhanced-stat-card .card-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.5px;
}

.enhanced-stat-card .info-icon {
  color: #9ca3af;
  transition: all 0.3s ease;
  cursor: help;
}

.enhanced-stat-card .info-icon:hover {
  color: var(--card-color);
  transform: scale(1.1);
}

.enhanced-stat-card .card-body {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 16px;
}

.enhanced-stat-card .stat-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.enhanced-stat-card .stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.enhanced-stat-card .icon-glow {
  position: absolute;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  opacity: 0;
  filter: blur(8px);
  transition: all 0.4s ease;
  z-index: 1;
}

.enhanced-stat-card:hover .stat-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.enhanced-stat-card:hover .icon-glow {
  opacity: 0.3;
  transform: scale(1.2);
}

.enhanced-stat-card .stat-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.enhanced-stat-card .stat-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.enhanced-stat-card .value-number {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  transition: all 0.3s ease;
}

.enhanced-stat-card:hover .value-number {
  background: linear-gradient(135deg, var(--card-color) 0%, color-mix(in srgb, var(--card-color) 80%, black) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.enhanced-stat-card .value-unit {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-left: 2px;
}

.enhanced-stat-card .stat-description {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.enhanced-stat-card:hover .stat-description {
  color: #374151;
}

.enhanced-stat-card .card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-color) 0%, color-mix(in srgb, var(--card-color) 60%, white) 100%);
  opacity: 0;
  transition: all 0.4s ease;
}

.enhanced-stat-card:hover .card-decoration {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-stat-card {
    height: 120px;
  }

  .enhanced-stat-card .card-content {
    padding: 16px;
  }

  .enhanced-stat-card .stat-icon {
    width: 48px;
    height: 48px;
  }

  .enhanced-stat-card .icon-glow {
    width: 48px;
    height: 48px;
  }

  .enhanced-stat-card .value-number {
    font-size: 24px;
  }
}

/* 特码综合分析表格样式 */
.filter-buttons {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  text-align: center;
}

.filter-buttons .el-button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-buttons .el-button {
  margin: 5px 2px;
  flex: 0 0 auto;
}

.table-controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  flex-wrap: wrap;
}
.number-cell {
  font-weight: bold;
  font-size: 16px;
}

.count-cell, .missing-cell, .max-missing-cell {
  font-weight: 500;
}

.unit {
  font-size: 12px;
  color: #999;
  margin-left: 2px;
}

.zodiac-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.zodiac-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.zodiac-text {
  font-size: 12px;
}

/* 走势点样式 */
.trend-dots {
  display: flex;
  justify-content: center;
  gap: 4px;
  height: 20px;
}

.trend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  border: 1px solid #ddd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.trend-dot:hover {
  transform: scale(1.2);
}

.trend-dot.miss {
  background-color: #f0f0f0;
}

.trend-dot.hit {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
}

.trend-dot.hit.red {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  box-shadow: 0 0 5px rgba(255, 77, 79, 0.5);
}

.trend-dot.hit.blue {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
}

.trend-dot.hit.green {
  background-color: #52c41a;
  border-color: #52c41a;
  box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
}

/* 热度指数样式 */
.hot-index-value {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: block;
  text-align: center;
}

.color-tag, .element-tag {
  width: 100%;
  text-align: center;
}

.tail-number {
  font-weight: bold;
  font-size: 16px;
  color: #1890ff;
}

.sum-digits {
  font-weight: bold;
  font-size: 16px;
  color: #722ed1;
}

/* 号码详情对话框样式 */
.number-detail {
  padding: 10px;
}

.number-detail h3 {
  text-align: center;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.number-detail h4 {
  margin: 15px 0 10px;
  padding-bottom: 5px;
  border-bottom: 2px solid #f0f0f0;
  color: #1890ff;
  font-size: 16px;
}

.detail-section {
  margin-bottom: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  border-radius: 4px;
}

.detail-item .label {
  font-weight: bold;
  color: #666;
}

.detail-item .value {
  font-weight: 500;
}

/* 走势图对话框样式 */
.number-trend-dialog .el-message-box__message {
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.number-trend-dialog .el-message-box__content {
  padding: 0;
}

.number-trend-dialog .el-message-box {
  width: 90%;
  max-width: 1000px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.number-trend-dialog .el-message-box__header {
  padding: 15px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.number-trend-dialog .el-message-box__title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.number-trend-dialog .el-message-box__headerbtn {
  top: 15px;
  right: 20px;
}

.number-trend-dialog .el-message-box__headerbtn .el-message-box__close {
  color: #909399;
  font-size: 18px;
}

.number-trend-dialog .el-button--primary {
  background-color: #1890ff;
  border-color: #1890ff;
  padding: 10px 20px;
  font-size: 14px;
}

/* 自定义号码详情对话框样式 */
.number-detail-dialog .el-message-box__content {
  padding: 20px;
}

.number-detail-dialog .el-message-box__header {
  padding: 15px 20px;
  background-color: #f5f7fa;
}

.number-detail-dialog .el-message-box__title {
  font-size: 18px;
  font-weight: bold;
}

.number-detail-dialog .el-message-box__headerbtn {
  top: 15px;
}
