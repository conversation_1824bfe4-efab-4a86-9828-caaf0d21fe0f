const GameRules2025 = {
  // 波色号码定义
  RED_NUMBERS: [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
  BLUE_NUMBERS: [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
  GREEN_NUMBERS: [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49],

  // 生肖列表（按顺序）
  ZODIAC_LIST: ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],

  // 生肖对应规则
  ZODIAC_MAP: {
    1: "蛇", 13: "蛇", 25: "蛇", 37: "蛇", 49: "蛇",
    2: "龙", 14: "龙", 26: "龙", 38: "龙",
    3: "兔", 15: "兔", 27: "兔", 39: "兔",
    4: "虎", 16: "虎", 28: "虎", 40: "虎",
    5: "牛", 17: "牛", 29: "牛", 41: "牛",
    6: "鼠", 18: "鼠", 30: "鼠", 42: "鼠",
    7: "猪", 19: "猪", 31: "猪", 43: "猪",
    8: "狗", 20: "狗", 32: "狗", 44: "狗",
    9: "鸡", 21: "鸡", 33: "鸡", 45: "鸡",
    10: "猴", 22: "猴", 34: "猴", 46: "猴",
    11: "羊", 23: "羊", 35: "羊", 47: "羊",
    12: "马", 24: "马", 36: "马", 48: "马"
  },

  // 五行对应规则
  WUXING_MAP: {
    3: "金", 4: "金", 11: "金", 12: "金", 25: "金", 26: "金", 33: "金", 34: "金", 41: "金", 42: "金",
    7: "木", 8: "木", 15: "木", 16: "木", 23: "木", 24: "木", 37: "木", 38: "木", 45: "木", 46: "木",
    13: "水", 14: "水", 21: "水", 22: "水", 29: "水", 30: "水", 43: "水", 44: "水",
    1: "火", 2: "火", 9: "火", 10: "火", 17: "火", 18: "火", 31: "火", 32: "火", 39: "火", 40: "火", 47: "火", 48: "火",
    5: "土", 6: "土", 19: "土", 20: "土", 27: "土", 28: "土", 35: "土", 36: "土", 49: "土"
  },

  // 获取号码的波色
  getColor(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) return ''
    if (this.RED_NUMBERS.includes(Number(number))) return '红波'
    if (this.BLUE_NUMBERS.includes(Number(number))) return '蓝波'
    if (this.GREEN_NUMBERS.includes(Number(number))) return '绿波'
    return ''
  },

  // 获取号码的生肖
  getZodiac(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) return ''
    return this.ZODIAC_MAP[number] || ''
  },

  // 获取号码的五行
  getWuxing(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) return ''
    return this.WUXING_MAP[number] || ''
  },

  // 获取号码的尾数
  getTail(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) return -1
    return number % 10
  },

  // 获取号码的头数
  getHead(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) return -1
    return Math.floor(number / 10)
  },

  // 判断号码是否为单数
  isOdd(number) {
    if (!number || isNaN(number)) return false
    return number % 2 !== 0
  },

  // 判断号码是否为大数
  isBig(number) {
    if (!number || isNaN(number)) return false
    return number >= 25
  },

  // 判断尾数是否为大数
  isTailBig(number) {
    const tail = this.getTail(number)
    return tail >= 5
  },

  // 判断合数是否为单数
  isSumOdd(number) {
    const sum = String(number).split('').reduce((acc, digit) => acc + parseInt(digit), 0)
    return sum % 2 === 1
  },

  // 判断合数是否为大数
  isSumBig(number) {
    if (!number || isNaN(number)) return false
    const sum = String(number).split('').reduce((acc, digit) => acc + parseInt(digit), 0)
    return sum >= 7
  },

  // 判断是否为家禽
  isDomestic(number) {
    const zodiac = this.getZodiac(number)
    return ['鸡', '狗', '猪', '牛', '羊', '马'].includes(zodiac)
  },

  // 获取号码的所有属性
  getNumberAttributes(number) {
    if (!number || isNaN(number) || number < 1 || number > 49) {
      return {
        number,
        color: '',
        zodiac: '',
        wuxing: '',
        tail: -1,
        head: -1,
        isOdd: false,
        isBig: false,
        isTailBig: false,
        isSumOdd: false,
        isSumBig: false,
        isDomestic: false,
        oddEven: '',
        bigSmall: '',
        tailBigSmall: '',
        sumOddEven: '',
        sumBigSmall: '',
        animalType: ''
      }
    }

    const isOddValue = this.isOdd(number)
    const isBigValue = this.isBig(number)
    const isTailBigValue = this.isTailBig(number)
    const isSumOddValue = this.isSumOdd(number)
    const isSumBigValue = this.isSumBig(number)
    const isDomesticValue = this.isDomestic(number)

    const sum = String(number).split('').reduce((acc, digit) => acc + parseInt(digit), 0)

    return {
      number,
      color: this.getColor(number),
      zodiac: this.getZodiac(number),
      wuxing: this.getWuxing(number),
      tail: this.getTail(number),
      head: this.getHead(number),
      isOdd: isOddValue,
      isBig: isBigValue,
      isTailBig: isTailBigValue,
      isSumOdd: isSumOddValue,
      isSumBig: isSumBigValue,
      isDomestic: isDomesticValue,
      oddEven: isOddValue ? '单' : '双',
      bigSmall: isBigValue ? '大' : '小',
      tailBigSmall: isTailBigValue ? '尾大' : '尾小',
      sumOddEven: isSumOddValue ? '合单' : '合双',
      sumBigSmall: isSumBigValue ? '合大' : '合小',
      animalType: isDomesticValue ? '家禽' : '野兽'
    }
  }
}

export default GameRules2025