import numpy as np
from typing import Dict, List, Any, <PERSON><PERSON>
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
from .base_model import BaseModel


class XGBoostModel(BaseModel):
    def __init__(self):
        super().__init__("xgboost")
        self.sequence_length = 10  # 使用前10期数据预测
        self.scaler = StandardScaler()

    def preprocess_data(self, data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """预处理数据"""
        features = []
        targets = []

        for i in range(len(data) - self.sequence_length):
            sequence = data[i:i + self.sequence_length]
            target = data[i + self.sequence_length]['special_number']

            # 构建特征向量
            feature_vector = []
            for item in sequence:
                feature_vector.extend([
                    item['special_number'],
                    1 if item['special_odd_even'] == '单' else 0,
                    1 if item['special_big_small'] == '大' else 0,
                    1 if item['special_color'] == '红波' else (
                        2 if item['special_color'] == '蓝波' else 0),
                ])

            features.append(feature_vector)
            targets.append(target)

        X = np.array(features)
        y = np.array(targets)

        # 标准化特征
        X = self.scaler.fit_transform(X)

        return X, y

    def train(self, X: np.ndarray, y: np.ndarray, params: Dict[str, Any] = None) -> Dict[str, float]:
        """训练模型"""
        if params is None:
            params = {
                'max_depth': 6,
                'learning_rate': 0.1,
                'n_estimators': 100,
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            }

        dtrain = xgb.DMatrix(X, label=y)

        # 训练模型
        self.model = xgb.train(
            params,
            dtrain,
            num_boost_round=params['n_estimators']
        )

        # 计算训练指标
        train_predictions = self.model.predict(dtrain)
        mse = np.mean((y - train_predictions) ** 2)
        mae = np.mean(np.abs(y - train_predictions))
        rmse = np.sqrt(mse)

        metrics = {
            'mse': float(mse),
            'mae': float(mae),
            'rmse': float(rmse)
        }

        return metrics

    def predict(self, X: np.ndarray) -> Dict[str, Any]:
        """预测结果"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        # 标准化输入数据
        X = self.scaler.transform(X)

        # 转换为DMatrix格式
        dtest = xgb.DMatrix(X)

        # 获取预测值和特征重要性
        predictions = self.model.predict(dtest)
        importance_scores = self.model.get_score(importance_type='gain')

        # 将特征重要性转换为数组
        feature_importance = np.zeros(X.shape[1])
        for feature, score in importance_scores.items():
            feature_idx = int(feature.replace('f', ''))
            feature_importance[feature_idx] = score

        # 计算每个号码的概率
        probabilities = self._calculate_probabilities(
            predictions, feature_importance)

        # 获取前30个最可能的号码
        top_30_indices = np.argsort(probabilities)[-30:][::-1]

        result = {
            'special_numbers_5': top_30_indices[:5].tolist(),
            'special_numbers_10': top_30_indices[:10].tolist(),
            'special_numbers_15': top_30_indices[:15].tolist(),
            'special_numbers_20': top_30_indices[:20].tolist(),
            'special_numbers_30': top_30_indices.tolist(),
            'confidence': float(np.max(probabilities))
        }

        return result

    def _calculate_probabilities(self, predictions: np.ndarray, feature_importance: np.ndarray) -> np.ndarray:
        """计算每个号码的概率"""
        probabilities = np.zeros(49)  # 1-49的号码

        # 计算特征重要性的总和用于归一化
        total_importance = np.sum(feature_importance) + 1e-6

        for pred, importance in zip(predictions, feature_importance / total_importance):
            # 使用高斯分布和特征重要性计算概率
            mu = pred
            sigma = 2.0 / (importance + 1e-6)  # 避免除零
            for i in range(49):
                prob = np.exp(-((i+1 - mu)**2)/(2*sigma**2))
                probabilities[i] += prob * importance

        # 归一化
        probabilities = probabilities / np.sum(probabilities)

        return probabilities

    def calculate_confidence(self, X: np.ndarray) -> float:
        """计算预测置信度"""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        X = self.scaler.transform(X)
        dtest = xgb.DMatrix(X)
        predictions = self.model.predict(dtest)
        importance_scores = self.model.get_score(importance_type='gain')

        # 将特征重要性转换为数组
        feature_importance = np.zeros(X.shape[1])
        for feature, score in importance_scores.items():
            feature_idx = int(feature.replace('f', ''))
            feature_importance[feature_idx] = score

        probabilities = self._calculate_probabilities(
            predictions, feature_importance)

        # 使用最高概率作为置信度
        confidence = float(np.max(probabilities))

        return confidence
