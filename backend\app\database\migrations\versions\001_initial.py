"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2024-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # predictions table
    op.create_table('predictions',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('expect', sa.String(), nullable=False),
                    sa.Column('prediction_time',
                              sa.DateTime(), nullable=False),
                    sa.Column('special_numbers_5', sa.String(),
                              nullable=True),  # JSON as string for SQLite
                    sa.Column('special_numbers_10',
                              sa.String(), nullable=True),
                    sa.Column('special_numbers_15',
                              sa.String(), nullable=True),
                    sa.Column('special_numbers_20',
                              sa.String(), nullable=True),
                    sa.Column('special_numbers_30',
                              sa.String(), nullable=True),
                    sa.Column('attribute_predictions',
                              sa.String(), nullable=True),
                    sa.Column('zodiac_3', sa.String(), nullable=True),
                    sa.Column('zodiac_5', sa.String(), nullable=True),
                    sa.Column('zodiac_7', sa.String(), nullable=True),
                    sa.Column('strategy', sa.String(), nullable=True),
                    sa.Column('confidence_scores', sa.String(), nullable=True),
                    sa.Column('actual_result', sa.Integer(), nullable=True),
                    sa.Column('accuracy', sa.Float(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_predictions_expect'),
                    'predictions', ['expect'], unique=True)
    op.create_index(op.f('ix_predictions_id'),
                    'predictions', ['id'], unique=False)

    # model_training_history table
    op.create_table('model_training_history',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('training_time', sa.DateTime(), nullable=False),
                    sa.Column('model_name', sa.String(), nullable=False),
                    sa.Column('parameters', sa.String(),
                              nullable=True),  # JSON as string
                    sa.Column('metrics', sa.String(),
                              nullable=True),  # JSON as string
                    sa.Column('status', sa.String(), nullable=False),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_model_training_history_id'),
                    'model_training_history', ['id'], unique=False)

    # backtest_results table
    op.create_table('backtest_results',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('start_date', sa.Date(), nullable=False),
                    sa.Column('end_date', sa.Date(), nullable=False),
                    sa.Column('execution_time', sa.DateTime(), nullable=False),
                    sa.Column('total_predictions',
                              sa.Integer(), nullable=False),
                    sa.Column('hits', sa.Integer(), nullable=False),
                    sa.Column('average_accuracy', sa.Float(), nullable=False),
                    sa.Column('roi', sa.Float(), nullable=False),
                    sa.Column('model_performance', sa.String(),
                              nullable=True),  # JSON as string
                    sa.Column('prediction_range',
                              sa.Integer(), nullable=False),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_backtest_results_id'),
                    'backtest_results', ['id'], unique=False)

    # backtest_records table
    op.create_table('backtest_records',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('backtest_id', sa.Integer(), nullable=False),
                    sa.Column('expect', sa.String(), nullable=False),
                    sa.Column('prediction_time',
                              sa.DateTime(), nullable=False),
                    sa.Column('predicted_numbers', sa.String(),
                              nullable=True),  # JSON as string
                    sa.Column('actual_number', sa.Integer(), nullable=True),
                    sa.Column('hit', sa.Boolean(), nullable=False),
                    sa.Column('profit', sa.Float(), nullable=False),
                    sa.Column('model', sa.String(), nullable=False),
                    sa.Column('confidence', sa.Float(), nullable=False),
                    sa.ForeignKeyConstraint(
                        ['backtest_id'], ['backtest_results.id'], ),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_backtest_records_id'),
                    'backtest_records', ['id'], unique=False)


def downgrade():
    op.drop_index(op.f('ix_backtest_records_id'),
                  table_name='backtest_records')
    op.drop_table('backtest_records')
    op.drop_index(op.f('ix_backtest_results_id'),
                  table_name='backtest_results')
    op.drop_table('backtest_results')
    op.drop_index(op.f('ix_model_training_history_id'),
                  table_name='model_training_history')
    op.drop_table('model_training_history')
    op.drop_index(op.f('ix_predictions_id'), table_name='predictions')
    op.drop_index(op.f('ix_predictions_expect'), table_name='predictions')
    op.drop_table('predictions')
