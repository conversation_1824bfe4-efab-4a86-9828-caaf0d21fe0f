from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.session import get_db
from app.models.prediction import Prediction
from app.schemas.prediction import PredictionCreate, PredictionResponse
from app.services.prediction_service import get_next_prediction

router = APIRouter()


@router.get("/next", response_model=PredictionResponse)
def predict_next_draw(db: Session = Depends(get_db)):
    """获取下一期预测结果"""
    prediction = get_next_prediction(db)
    if not prediction:
        raise HTTPException(status_code=404, detail="No prediction available")
    return prediction


@router.get("/history", response_model=List[PredictionResponse])
def get_prediction_history(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取历史预测记录"""
    predictions = db.query(Prediction).offset(skip).limit(limit).all()
    return predictions


@router.get("/accuracy", response_model=dict)
def get_prediction_accuracy(db: Session = Depends(get_db)):
    """获取预测准确率统计"""
    # TODO: 实现预测准确率统计逻辑
    return {"accuracy": 0.0, "total_predictions": 0}
