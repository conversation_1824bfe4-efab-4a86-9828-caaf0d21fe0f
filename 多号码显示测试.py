#!/usr/bin/env python3
"""
多号码显示逻辑测试脚本
"""
import requests
import json

def test_multiple_hot_numbers():
    """测试多个热门号码的显示逻辑"""
    print("=== 多号码显示逻辑测试 ===")
    
    try:
        response = requests.get("http://localhost:8000/api/draw/statistics")
        data = response.json()['data']
        hot_numbers = data['basicStats']['hotNumbers']
        
        print("📊 热门号码数据:")
        for i, num_data in enumerate(hot_numbers[:10]):  # 显示前10个
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次 ({num_data['percentage']:.2f}%)")
        
        # 分析相同次数的号码
        max_count = hot_numbers[0]['count']
        same_count_numbers = [n for n in hot_numbers if n['count'] == max_count]
        
        print(f"\n🔥 最高出现次数: {max_count}次")
        print(f"🔢 相同次数的号码数量: {len(same_count_numbers)}个")
        
        if len(same_count_numbers) > 1:
            print("📋 所有最热号码:")
            for num_data in same_count_numbers:
                print(f"   号码{num_data['number']:02d}: {num_data['count']}次")
        
        return simulate_frontend_display(same_count_numbers, max_count, "热门")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_cold_numbers():
    """测试多个冷门号码的显示逻辑"""
    print("\n=== 冷门号码显示逻辑测试 ===")
    
    try:
        response = requests.get("http://localhost:8000/api/draw/statistics")
        data = response.json()['data']
        cold_numbers = data['basicStats']['coldNumbers']
        
        print("📊 冷门号码数据:")
        for i, num_data in enumerate(cold_numbers[:10]):  # 显示前10个
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次 ({num_data['percentage']:.2f}%)")
        
        # 分析相同次数的号码
        min_count = cold_numbers[0]['count']
        same_count_numbers = [n for n in cold_numbers if n['count'] == min_count]
        
        print(f"\n🧊 最低出现次数: {min_count}次")
        print(f"🔢 相同次数的号码数量: {len(same_count_numbers)}个")
        
        if len(same_count_numbers) > 1:
            print("📋 所有最冷号码:")
            for num_data in same_count_numbers:
                print(f"   号码{num_data['number']:02d}: {num_data['count']}次")
        
        return simulate_frontend_display(same_count_numbers, min_count, "冷门")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_frontend_display(same_count_numbers, count, type_name):
    """模拟前端显示逻辑"""
    print(f"\n🎨 前端显示逻辑模拟 ({type_name}):")
    
    # 获取号码列表
    numbers = [str(n['number']).zfill(2) for n in same_count_numbers]
    
    # 模拟前端逻辑
    if len(numbers) == 1:
        display_value = numbers[0]
        description = f"出现{count}次"
        tooltip = f"出现次数最{type_name}的特码号码: {numbers[0]}"
    elif len(numbers) <= 3:
        display_value = ','.join(numbers)
        description = f"{len(numbers)}个号码各{count}次"
        tooltip = f"出现次数最{type_name}的特码号码({count}次): {', '.join(numbers)}"
    else:
        display_value = ','.join(numbers[:3]) + '...'
        description = f"{len(numbers)}个号码各{count}次"
        tooltip = f"出现次数最{type_name}的特码号码({count}次): {', '.join(numbers)}"
    
    print(f"   📱 卡片显示值: {display_value}")
    print(f"   📝 描述文字: {description}")
    print(f"   💡 提示信息: {tooltip}")
    
    # 分析显示效果
    print(f"\n✨ 显示效果分析:")
    if ',' in display_value:
        print("   • 检测到多号码，将应用 multi-numbers 样式")
        print("   • 字体大小: 20px")
        print("   • 字符间距: 1px")
        print("   • 单词间距: 2px")
    
    if '...' in display_value:
        print("   • 检测到省略号，将应用 has-ellipsis 样式")
        print("   • 字体大小: 18px")
        print("   • 字符间距: 0.5px")
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    test_cases = [
        {
            "name": "单个号码",
            "numbers": [{"number": 25, "count": 6}],
            "expected_display": "25",
            "expected_description": "出现6次"
        },
        {
            "name": "两个号码",
            "numbers": [{"number": 25, "count": 6}, {"number": 20, "count": 6}],
            "expected_display": "25,20",
            "expected_description": "2个号码各6次"
        },
        {
            "name": "三个号码",
            "numbers": [{"number": 25, "count": 6}, {"number": 20, "count": 6}, {"number": 38, "count": 6}],
            "expected_display": "25,20,38",
            "expected_description": "3个号码各6次"
        },
        {
            "name": "五个号码",
            "numbers": [
                {"number": 25, "count": 6}, {"number": 20, "count": 6}, 
                {"number": 38, "count": 6}, {"number": 15, "count": 6}, 
                {"number": 42, "count": 6}
            ],
            "expected_display": "25,20,38...",
            "expected_description": "5个号码各6次"
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 测试用例: {case['name']}")
        result = simulate_frontend_display(case['numbers'], case['numbers'][0]['count'], "测试")
        
        # 验证结果
        numbers = [str(n['number']).zfill(2) for n in case['numbers']]
        if len(numbers) == 1:
            actual_display = numbers[0]
        elif len(numbers) <= 3:
            actual_display = ','.join(numbers)
        else:
            actual_display = ','.join(numbers[:3]) + '...'
        
        if actual_display == case['expected_display']:
            print(f"   ✅ 显示值正确: {actual_display}")
        else:
            print(f"   ❌ 显示值错误: 期望 {case['expected_display']}, 实际 {actual_display}")

def generate_improvement_suggestions():
    """生成改进建议"""
    print("\n=== 改进建议 ===")
    
    suggestions = [
        "🎯 用户体验优化:",
        "  • 悬停时显示完整的号码列表",
        "  • 点击卡片展开显示所有相同次数的号码",
        "  • 添加号码颜色标识(红蓝绿波)",
        "",
        "📱 响应式优化:",
        "  • 移动端自动调整显示号码数量",
        "  • 小屏幕设备优化字体大小",
        "  • 触摸设备友好的交互方式",
        "",
        "🎨 视觉增强:",
        "  • 号码之间添加分隔符样式",
        "  • 省略号使用更美观的符号",
        "  • 添加渐变动画效果",
        "",
        "⚡ 性能优化:",
        "  • 缓存计算结果",
        "  • 虚拟滚动长列表",
        "  • 懒加载详细信息"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def main():
    """主函数"""
    print("🔧 多号码显示逻辑测试开始...")
    
    success = True
    
    # 测试热门号码
    if not test_multiple_hot_numbers():
        success = False
    
    # 测试冷门号码  
    if not test_multiple_cold_numbers():
        success = False
    
    # 测试边界情况
    test_edge_cases()
    
    # 生成改进建议
    generate_improvement_suggestions()
    
    if success:
        print("\n✅ 所有测试通过！多号码显示逻辑优化成功！")
    else:
        print("\n❌ 部分测试失败，请检查相关问题。")
    
    return success

if __name__ == "__main__":
    main()
