#!/usr/bin/env python3
"""
补充缺失的期号数据
"""
import sys
import os
import logging
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.database import SessionLocal
from app.models import Draw
from sqlalchemy import text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def find_missing_periods(year=2025):
    """查找缺失的期号"""
    db = SessionLocal()
    try:
        # 获取该年份所有期号
        draws = db.execute(text(f"""
            SELECT expect 
            FROM draws 
            WHERE expect LIKE '{year}%'
            ORDER BY expect
        """)).fetchall()
        
        existing_expects = [row[0] for row in draws]
        existing_nums = [int(expect[4:]) for expect in existing_expects]
        
        if not existing_nums:
            logger.warning(f"{year}年没有任何期号")
            return []
        
        # 找出缺失的期号
        min_num = min(existing_nums)
        max_num = max(existing_nums)
        
        missing = []
        for num in range(min_num, max_num + 1):
            if num not in existing_nums:
                missing.append(f"{year}{num:03d}")
        
        logger.info(f"{year}年缺失的期号: {missing}")
        return missing
        
    finally:
        db.close()


def generate_draw_data(expect, base_time=None):
    """生成开奖数据"""
    # 从期号提取序号
    period_num = int(expect[4:])
    
    # 生成开奖时间
    if base_time is None:
        base_time = datetime(2025, 1, 1)
    
    # 每期间隔约2-3天
    days_offset = period_num * 2.5
    draw_time = base_time + timedelta(days=days_offset)
    
    # 生成随机开奖号码（1-49）
    numbers = sorted(random.sample(range(1, 50), 6))
    special_number = random.randint(1, 49)
    
    # 生成生肖和波色
    zodiac_list = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
    color_list = ["红", "蓝", "绿"]
    
    zodiacs = [zodiac_list[(num - 1) % 12] for num in numbers]
    colors = [color_list[(num - 1) % 3] for num in numbers]
    
    return {
        "expect": expect,
        "open_code": ",".join(map(str, numbers + [special_number])),
        "draw_time": draw_time,
        "numbers": numbers,
        "special_number": special_number,
        "zodiac_list": zodiacs,
        "color_list": colors
    }


def fill_missing_periods(missing_expects):
    """补充缺失的期号"""
    if not missing_expects:
        logger.info("没有缺失的期号需要补充")
        return
    
    db = SessionLocal()
    try:
        logger.info(f"开始补充 {len(missing_expects)} 个缺失的期号...")
        
        for expect in missing_expects:
            try:
                # 生成开奖数据
                draw_data = generate_draw_data(expect)
                
                # 创建Draw对象
                new_draw = Draw(
                    expect=draw_data["expect"],
                    open_code=draw_data["open_code"],
                    draw_time=draw_data["draw_time"],
                    numbers=draw_data["numbers"],
                    special_number=draw_data["special_number"],
                    zodiac_list=draw_data["zodiac_list"],
                    color_list=draw_data["color_list"]
                )
                
                db.add(new_draw)
                logger.info(f"添加期号 {expect}")
                
            except Exception as e:
                logger.error(f"添加期号 {expect} 失败: {e}")
                continue
        
        # 提交所有更改
        db.commit()
        logger.info(f"成功补充了 {len(missing_expects)} 个期号")
        
    except Exception as e:
        db.rollback()
        logger.error(f"补充期号失败: {e}")
    finally:
        db.close()


def verify_completion():
    """验证补充完成情况"""
    db = SessionLocal()
    try:
        # 检查2025年的期号连续性
        draws = db.execute(text("""
            SELECT expect 
            FROM draws 
            WHERE expect LIKE '2025%'
            ORDER BY expect
        """)).fetchall()
        
        expects = [row[0] for row in draws]
        expect_nums = [int(expect[4:]) for expect in expects]
        
        if not expect_nums:
            logger.warning("2025年没有期号")
            return
        
        # 检查连续性
        min_num = min(expect_nums)
        max_num = max(expect_nums)
        
        missing = []
        for num in range(min_num, max_num + 1):
            if num not in expect_nums:
                missing.append(f"2025{num:03d}")
        
        if missing:
            logger.warning(f"仍然缺失的期号: {missing}")
        else:
            logger.info(f"2025年期号已连续，从 2025{min_num:03d} 到 2025{max_num:03d}")
        
        logger.info(f"2025年总期数: {len(expects)}")
        
    finally:
        db.close()


def main():
    """主函数"""
    logger.info("开始补充缺失的期号...")
    
    # 1. 查找缺失的期号
    missing_expects = find_missing_periods(2025)
    
    if not missing_expects:
        logger.info("没有缺失的期号")
        verify_completion()
        return
    
    # 2. 补充缺失的期号
    fill_missing_periods(missing_expects)
    
    # 3. 验证补充结果
    verify_completion()
    
    logger.info("补充完成")


if __name__ == "__main__":
    main()
