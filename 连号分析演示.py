#!/usr/bin/env python3
"""
特码连号分析演示脚本
展示如何使用和理解四种可视化图表
"""
import requests
import json
from collections import defaultdict

def get_statistics_data():
    """获取统计数据"""
    try:
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        if response.status_code == 200:
            return response.json()['data']
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def analyze_consecutive_patterns(number_frequency):
    """分析连号模式"""
    print("🔗 连号模式分析")
    print("=" * 50)
    
    consecutive_pairs = []
    consecutive_strength = {}
    
    # 计算连号强度
    for i in range(1, 49):  # 1-48，因为49没有下一个号码
        current_num = str(i)
        next_num = str(i + 1)
        
        current_count = number_frequency.get(current_num, 0)
        next_count = number_frequency.get(next_num, 0)
        
        if current_count > 0 and next_count > 0:
            strength = min(current_count, next_count)
            consecutive_pairs.append((i, i + 1, strength))
            consecutive_strength[f"{i}-{i+1}"] = strength
    
    # 按强度排序
    consecutive_pairs.sort(key=lambda x: x[2], reverse=True)
    
    print(f"📊 发现 {len(consecutive_pairs)} 对有效连号")
    print("\n🔥 连号强度排行榜 (前10名):")
    for i, (num1, num2, strength) in enumerate(consecutive_pairs[:10]):
        print(f"   {i+1:2d}. 号码{num1:02d}-{num2:02d}: 强度 {strength}")
    
    print("\n❄️ 连号强度排行榜 (后10名):")
    for i, (num1, num2, strength) in enumerate(consecutive_pairs[-10:]):
        rank = len(consecutive_pairs) - 9 + i
        print(f"   {rank:2d}. 号码{num1:02d}-{num2:02d}: 强度 {strength}")
    
    return consecutive_pairs, consecutive_strength

def analyze_heatmap_patterns(consecutive_pairs):
    """分析热力图模式"""
    print("\n🔥 热力图分析")
    print("=" * 50)
    
    # 创建热力图矩阵
    heatmap_matrix = [[0 for _ in range(49)] for _ in range(49)]
    
    for num1, num2, strength in consecutive_pairs:
        heatmap_matrix[num1-1][num2-1] = strength
        heatmap_matrix[num2-1][num1-1] = strength  # 对称
    
    # 找出热点区域
    hot_zones = []
    for i in range(49):
        for j in range(i+1, 49):
            if heatmap_matrix[i][j] > 0:
                hot_zones.append((i+1, j+1, heatmap_matrix[i][j]))
    
    hot_zones.sort(key=lambda x: x[2], reverse=True)
    
    print("🌡️ 热点区域分析:")
    print("   - 对角线附近的热点表示相邻号码的强连号关系")
    print("   - 颜色越深，连号关系越强")
    print("   - 可以快速识别热门连号组合")
    
    print(f"\n📍 主要热点区域 (前5名):")
    for i, (num1, num2, strength) in enumerate(hot_zones[:5]):
        print(f"   {i+1}. 区域({num1:02d},{num2:02d}): 强度 {strength}")

def analyze_graph_patterns(number_frequency, consecutive_pairs):
    """分析关系图模式"""
    print("\n🌐 关系图分析")
    print("=" * 50)
    
    # 计算节点度数（连接数）
    node_degrees = defaultdict(int)
    for num1, num2, strength in consecutive_pairs:
        node_degrees[num1] += 1
        node_degrees[num2] += 1
    
    # 按度数排序
    sorted_nodes = sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)
    
    print("🔗 节点连接度分析:")
    print("   - 节点大小表示号码出现频率")
    print("   - 连线粗细表示连号关系强度")
    print("   - 高度数节点是连号网络的中心")
    
    print(f"\n📊 连接度排行榜 (前10名):")
    for i, (num, degree) in enumerate(sorted_nodes[:10]):
        freq = number_frequency.get(str(num), 0)
        print(f"   {i+1:2d}. 号码{num:02d}: 连接度 {degree}, 出现 {freq} 次")

def analyze_chord_patterns(consecutive_pairs):
    """分析和弦图模式"""
    print("\n⭕ 和弦图分析")
    print("=" * 50)
    
    print("🎵 圆形关系分析:")
    print("   - 号码按圆形排列，便于观察整体关系")
    print("   - 连线的弯曲程度表示关系强度")
    print("   - 对称性设计便于比较双向关系")
    
    # 分析圆形分布的对称性
    symmetric_pairs = []
    for num1, num2, strength in consecutive_pairs:
        # 检查是否有对称的连号关系
        reverse_pair = next((p for p in consecutive_pairs if p[0] == num2 and p[1] == num1), None)
        if reverse_pair:
            symmetric_pairs.append((num1, num2, strength))
    
    print(f"\n🔄 对称连号关系: {len(symmetric_pairs)} 对")
    if symmetric_pairs:
        print("   前5个对称关系:")
        for i, (num1, num2, strength) in enumerate(symmetric_pairs[:5]):
            print(f"   {i+1}. {num1:02d}↔{num2:02d}: 强度 {strength}")

def analyze_sankey_patterns(consecutive_pairs):
    """分析桑基图模式"""
    print("\n🌊 桑基图分析")
    print("=" * 50)
    
    print("💧 流量分析:")
    print("   - 节点垂直排列，显示层次结构")
    print("   - 连线宽度表示'流量'大小")
    print("   - 可以跟踪主要的连号流向")
    
    # 计算流入和流出
    flow_in = defaultdict(int)
    flow_out = defaultdict(int)
    
    for num1, num2, strength in consecutive_pairs:
        flow_out[num1] += strength
        flow_in[num2] += strength
    
    # 找出主要的流量节点
    major_sources = sorted(flow_out.items(), key=lambda x: x[1], reverse=True)[:5]
    major_sinks = sorted(flow_in.items(), key=lambda x: x[1], reverse=True)[:5]
    
    print(f"\n📤 主要流出节点 (前5名):")
    for i, (num, flow) in enumerate(major_sources):
        print(f"   {i+1}. 号码{num:02d}: 流出量 {flow}")
    
    print(f"\n📥 主要流入节点 (前5名):")
    for i, (num, flow) in enumerate(major_sinks):
        print(f"   {i+1}. 号码{num:02d}: 流入量 {flow}")

def generate_usage_guide():
    """生成使用指南"""
    print("\n📱 使用指南")
    print("=" * 50)
    
    print("🎯 如何使用四种图表:")
    print()
    
    print("1️⃣ 热力图 (Heatmap)")
    print("   ✅ 适用场景: 快速识别整体连号热点")
    print("   🔍 使用方法:")
    print("      - 寻找对角线附近的深色区域")
    print("      - 悬停查看具体连号强度")
    print("      - 比较不同区域的颜色深浅")
    print("   💡 分析技巧: 深绿色区域是重点关注对象")
    print()
    
    print("2️⃣ 关系图 (Graph)")
    print("   ✅ 适用场景: 分析号码网络结构")
    print("   🔍 使用方法:")
    print("      - 拖拽节点调整布局")
    print("      - 点击节点高亮相关连接")
    print("      - 观察节点大小和连线粗细")
    print("   💡 分析技巧: 大节点+粗连线 = 重要连号关系")
    print()
    
    print("3️⃣ 和弦图 (Chord)")
    print("   ✅ 适用场景: 观察整体关系对称性")
    print("   🔍 使用方法:")
    print("      - 观察圆形排列的号码")
    print("      - 查看连线的弯曲程度")
    print("      - 利用旋转标签定位号码")
    print("   💡 分析技巧: 对称的连线表示稳定的连号关系")
    print()
    
    print("4️⃣ 桑基图 (Sankey)")
    print("   ✅ 适用场景: 跟踪连号流向趋势")
    print("   🔍 使用方法:")
    print("      - 观察节点的垂直排列")
    print("      - 跟踪'流量'的主要路径")
    print("      - 拖拽节点调整位置")
    print("   💡 分析技巧: 粗流量线表示强连号趋势")

def main():
    """主函数"""
    print("🔗 特码连号分析演示")
    print("=" * 60)
    
    # 获取数据
    print("📡 正在获取统计数据...")
    data = get_statistics_data()
    
    if not data:
        print("❌ 无法获取数据，请确保后端服务正在运行")
        return
    
    number_frequency = data.get('numberFrequency', {})
    
    if not number_frequency:
        print("❌ 号码频率数据为空")
        return
    
    print(f"✅ 成功获取数据，包含 {len(number_frequency)} 个号码的频率信息")
    
    # 分析连号模式
    consecutive_pairs, consecutive_strength = analyze_consecutive_patterns(number_frequency)
    
    if not consecutive_pairs:
        print("❌ 没有发现有效的连号关系")
        return
    
    # 分析各种图表模式
    analyze_heatmap_patterns(consecutive_pairs)
    analyze_graph_patterns(number_frequency, consecutive_pairs)
    analyze_chord_patterns(consecutive_pairs)
    analyze_sankey_patterns(consecutive_pairs)
    
    # 生成使用指南
    generate_usage_guide()
    
    print("\n🎉 分析完成！")
    print("💡 建议: 在浏览器中打开统计页面，切换不同的图表类型进行实际体验")
    print("🔗 访问路径: http://localhost:3000/statistics")

if __name__ == "__main__":
    main()
