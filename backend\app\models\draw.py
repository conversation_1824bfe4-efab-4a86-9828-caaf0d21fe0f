from sqlalchemy import Column, Integer, String, DateTime, func, Boolean, JSON
from ..database import Base


class Draw(Base):
    __tablename__ = "draws"

    id = Column(Integer, primary_key=True, index=True)
    expect = Column(String, unique=True, index=True, nullable=False)
    numbers = Column(JSON)  # 开奖号码
    draw_time = Column(DateTime, nullable=False)
    open_code = Column(String, nullable=False)  # 开奖号码字符串，必填
    special_number = Column(Integer, nullable=True)  # 特码
    zodiac = Column(String, nullable=True)  # 改为可选字段
    color = Column(String, nullable=True)  # 改为可选字段
    odd_even = Column(String, nullable=True)  # 改为可选字段
    big_small = Column(String, nullable=True)  # 改为可选字段
    tail_big_small = Column(String, nullable=True)  # 改为可选字段
    sum_odd_even = Column(String, nullable=True)  # 改为可选字段
    animal_type = Column(String, nullable=True)  # 改为可选字段
    wuxing = Column(String, nullable=True)  # 改为可选字段
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(),
                        onupdate=func.now())

    # 添加属性，使open_time成为draw_time的别名
    @property
    def open_time(self):
        return self.draw_time

    @open_time.setter
    def open_time(self, value):
        self.draw_time = value

    # 添加特码相关的属性
    @property
    def special_zodiac(self):
        return self.zodiac

    @special_zodiac.setter
    def special_zodiac(self, value):
        self.zodiac = value

    @property
    def special_color(self):
        return self.color

    @special_color.setter
    def special_color(self, value):
        self.color = value

    @property
    def special_wuxing(self):
        return self.wuxing

    @special_wuxing.setter
    def special_wuxing(self, value):
        self.wuxing = value
