from flask_sqlalchemy import SQLAlchemy
import logging
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

# 创建全局 SQLAlchemy 实例
db = SQLAlchemy()


class DatabaseManager:
    """数据库管理器"""

    @staticmethod
    def init_app(app):
        """初始化数据库"""
        try:
            # 初始化 SQLAlchemy
            db.init_app(app)

            # 在应用上下文中创建所有表
            with app.app_context():
                db.create_all()
                logger.info("数据库表创建成功")

        except SQLAlchemyError as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    @staticmethod
    def reset_db(app):
        """重置数据库（删除所有表并重新创建）"""
        try:
            with app.app_context():
                db.drop_all()
                db.create_all()
                logger.info("数据库重置成功")
        except SQLAlchemyError as e:
            logger.error(f"数据库重置失败: {str(e)}")
            raise
