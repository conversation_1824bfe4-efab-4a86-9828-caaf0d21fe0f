import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
from app.core.config import settings

# 创建日志目录
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 配置日志格式
log_format = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(log_format)

# 创建文件处理器
file_handler = RotatingFileHandler(
    log_dir / "app.log",
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
file_handler.setFormatter(log_format)

# 创建日志记录器
logger = logging.getLogger("marksix")
logger.setLevel(logging.INFO)
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# 创建错误日志记录器
error_logger = logging.getLogger("marksix.error")
error_logger.setLevel(logging.ERROR)
error_handler = RotatingFileHandler(
    log_dir / "error.log",
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
error_handler.setFormatter(log_format)
error_logger.addHandler(error_handler)

# 创建预测日志记录器
prediction_logger = logging.getLogger("marksix.prediction")
prediction_logger.setLevel(logging.INFO)
prediction_handler = RotatingFileHandler(
    log_dir / "prediction.log",
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
prediction_handler.setFormatter(log_format)
prediction_logger.addHandler(prediction_handler)


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    return logging.getLogger(f"marksix.{name}")
