import sqlite3

conn = sqlite3.connect('marksix.db')
cursor = conn.cursor()

cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
tables = cursor.fetchall()
print('数据库表:', [table[0] for table in tables])

if 'draws' in [table[0] for table in tables]:
    cursor.execute('SELECT COUNT(*) FROM draws')
    count = cursor.fetchone()[0]
    print(f'draws表记录数: {count}')
    
    cursor.execute('PRAGMA table_info(draws)')
    columns = cursor.fetchall()
    print('draws表结构:')
    for col in columns:
        print(f'  {col[1]} {col[2]}')
else:
    print('draws表不存在')

conn.close()
