#!/usr/bin/env python3
"""
测试API修复结果
"""
import requests
import json

def test_api_fix():
    """测试API修复结果"""
    print("=== 测试API修复结果 ===")
    
    try:
        # 测试2025年的数据
        response = requests.get("http://localhost:8000/api/draw/statistics?year=2025")
        data = response.json()['data']
        
        print("📊 API返回的冷门号码:")
        cold_numbers = data['basicStats']['coldNumbers']
        for i, num_data in enumerate(cold_numbers[:10]):
            print(f"   {i+1}. 号码{num_data['number']:02d}: {num_data['count']}次")
        
        # 检查是否包含0次的号码
        zero_count_numbers = [num for num in cold_numbers if num['count'] == 0]
        
        if zero_count_numbers:
            print(f"\n✅ 修复成功！API现在返回了{len(zero_count_numbers)}个0次的号码:")
            for num_data in zero_count_numbers:
                print(f"   号码{num_data['number']:02d}: {num_data['count']}次")
        else:
            print(f"\n❌ 修复失败！API仍然没有返回0次的号码")
            print(f"最少出现次数: {cold_numbers[0]['count']}次")
        
        # 检查numberFrequency字段
        number_freq = data['numberFrequency']
        zero_freq_numbers = [num for num, count in number_freq.items() if count == 0]
        
        print(f"\n📋 numberFrequency中0次的号码:")
        if zero_freq_numbers:
            print(f"   共{len(zero_freq_numbers)}个: {zero_freq_numbers}")
        else:
            print(f"   没有0次的号码")
        
        return len(zero_count_numbers) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始测试API修复结果...")
    
    if test_api_fix():
        print("\n🎉 API修复成功！16、17、19现在会正确显示为最冷特码！")
    else:
        print("\n😞 API修复失败，需要进一步调试...")

if __name__ == "__main__":
    main()
