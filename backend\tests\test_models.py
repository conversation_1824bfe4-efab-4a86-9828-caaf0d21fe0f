import unittest
import numpy as np
from models.base_model import BaseModel
from models.deep_ensemble_model import DeepEnsembleModel
from models.trainer import ModelTrainer
from config.config import MODEL_CONFIG


class TestBaseModel(unittest.TestCase):
    def setUp(self):
        self.config = MODEL_CONFIG.copy()

    def test_model_initialization(self):
        """测试模型初始化"""
        model = BaseModel(self.config)
        self.assertIsNotNone(model)
        self.assertEqual(model.config, self.config)

    def test_data_validation(self):
        """测试数据验证"""
        model = BaseModel(self.config)
        test_data = np.array([1, 2, 3])
        validated_data = model.validate_input(test_data)
        self.assertEqual(validated_data.shape[0], 1)
        self.assertEqual(validated_data.shape[1], 3)


class TestDeepEnsembleModel(unittest.TestCase):
    def setUp(self):
        self.config = MODEL_CONFIG.copy()
        self.model = DeepEnsembleModel(self.config)

    def test_ensemble_initialization(self):
        """测试集成模型初始化"""
        self.assertEqual(len(self.model.models), self.config['n_models'])

    def test_model_prediction(self):
        """测试模型预测"""
        test_input = np.random.rand(
            10, self.config['sequence_length'], self.config['n_features'])
        prediction = self.model.predict(test_input)

        self.assertIn('mean', prediction)
        self.assertIn('std', prediction)
        self.assertIn('predictions', prediction)

    def test_model_save_load(self):
        """测试模型保存和加载"""
        # 保存模型
        self.model.save()

        # 创建新模型并加载
        new_model = DeepEnsembleModel(self.config)
        new_model.load()

        # 验证预测结果一致性
        test_input = np.random.rand(
            10, self.config['sequence_length'], self.config['n_features'])
        pred1 = self.model.predict(test_input)
        pred2 = new_model.predict(test_input)

        np.testing.assert_array_almost_equal(pred1['mean'], pred2['mean'])


class TestModelTrainer(unittest.TestCase):
    def setUp(self):
        self.config = MODEL_CONFIG.copy()
        self.trainer = ModelTrainer(self.config)

    def test_data_preparation(self):
        """测试数据准备"""
        test_data = np.random.rand(100, 10)
        X, y = self.trainer._prepare_data(test_data)

        self.assertEqual(X.shape[1], self.config['sequence_length'])
        self.assertEqual(y.shape[0], X.shape[0])

    def test_model_evaluation(self):
        """测试模型评估"""
        y_true = np.array([1, 2, 3, 4, 5])
        y_pred = np.array([1.1, 2.1, 2.9, 4.2, 5.1])

        metrics = self.trainer.evaluate(None, y_true, y_pred)

        self.assertIn('mse', metrics)
        self.assertIn('mae', metrics)
        self.assertIn('accuracy', metrics)
        self.assertIn('hit_rate', metrics)


if __name__ == '__main__':
    unittest.main()


