import request from '@/utils/request'

// 获取开奖历史
export function getDrawHistory(params) {
  return request({
    url: '/api/draw/history',
    method: 'get',
    params: {
      page: params.page || 1,
      page_size: params.pageSize || 20,
      start_date: params.startDate,
      end_date: params.endDate,
      start_period: params.startPeriod,
      end_period: params.endPeriod,
      number: params.number,
      zodiac: params.zodiac,
      color: params.color,
      odd_even: params.oddEven,
      big_small: params.bigSmall,
      tail_big_small: params.tailBigSmall,
      sum_odd_even: params.sumOddEven,
      animal_type: params.animalType,
      wuxing: params.wuxing
    }
  })
}

// 获取最新一期开奖结果
export function getLatestDraw() {
  return request({
    url: '/api/draw/latest',
    method: 'get',
    params: { limit: 1 }
  }).then(response => {
    console.log('getLatestDraw API响应:', response)
    if (response && response.code === 200 && response.data) {
      // 标准格式响应
      if (Array.isArray(response.data) && response.data.length > 0) {
        return {
          code: 200,
          data: response.data[0],
          message: 'success'
        }
      } else if (typeof response.data === 'object') {
        return response
      }
    } else if (Array.isArray(response) && response.length > 0) {
      // 兼容旧格式，直接返回数组
      return {
        code: 200,
        data: response[0],
        message: 'success'
      }
    } else if (typeof response === 'object' && !Array.isArray(response)) {
      // 直接返回对象
      return {
        code: 200,
        data: response,
        message: 'success'
      }
    }

    // 没有数据的情况
    return {
      code: 200,
      data: null,
      message: 'No data found'
    }
  })
}

// 获取最新几期开奖结果
export function getLatestDraws(limit = 5) {
  return request({
    url: '/api/draw/latest',
    method: 'get',
    params: { limit }
  }).then(response => {
    console.log('getLatestDraws API响应:', response)
    if (response && response.data && Array.isArray(response.data)) {
      return response
    }
    // 兼容旧格式
    return {
      code: 200,
      data: Array.isArray(response) ? response : [],
      message: 'success'
    }
  })
}

// 获取下一期信息
export function getNextDraw() {
  return request({
    url: '/api/draw/next',
    method: 'get'
  }).then(response => {
    console.log('getNextDraw API响应:', response)
    if (response && response.data) {
      return response
    }
    // 兼容旧格式
    return {
      code: 200,
      data: response,
      message: 'success'
    }
  })
}

// 获取开奖统计
export function getDrawStatistics(params = {}) {
  return request({
    url: '/api/draw/statistics',
    method: 'get',
    params: {
      start_date: params.startDate,
      end_date: params.endDate,
      year: params.year,
      start_period: params.startPeriod,
      end_period: params.endPeriod
    }
  })
}

// 获取特定期号的开奖结果
export function getDrawByExpect(expect) {
  return request({
    url: `/api/draw/history/${expect}`,
    method: 'get'
  })
}

// 获取历史开奖记录
export function getHistoryDraws(params) {
  console.log('调用getHistoryDraws API，原始参数:', params)

  const apiParams = {
    // 转换参数名称以匹配后端 API
    page: params.page || 1,
    page_size: params.pageSize || 20,
    start_period: params.startPeriod,
    end_period: params.endPeriod,
    start_date: params.startDate,
    end_date: params.endDate,
    number: params.specialNumber,
    zodiac: params.specialZodiac,
    color: params.specialColor ? params.specialColor.replace('波', '') : undefined,
    odd_even: params.specialOddEven,
    big_small: params.specialBigSmall,
    tail_big_small: params.specialTailBigSmall,
    sum_odd_even: params.specialSumOddEven,
    animal_type: params.specialAnimalType,
    wuxing: params.specialWuxing
  }

  console.log('调用getHistoryDraws API，转换后参数:', apiParams)

  return request({
    url: '/api/draw/history',  // 修改为正确的API路径，添加/api前缀
    method: 'get',
    params: apiParams
  }).then(response => {
    console.log('getHistoryDraws API响应:', response)
    return response
  }).catch(error => {
    console.error('getHistoryDraws API错误:', error)
    throw error
  })
}

// 获取指定期号的开奖结果
export function getDrawByPeriod(period) {
  return request({
    url: `/api/draw/${period}`,
    method: 'get'
  })
}

// 进行预测
export const predict = (data) => {
  return request({
    url: '/api/draw/predict',
    method: 'post',
    data
  })
}

// 开始模型训练
export const startTraining = (data) => {
  return request({
    url: '/api/draw/train',
    method: 'post',
    data
  })
}

// 获取回测数据
export function getBacktestData(params) {
  return request({
    url: '/api/draw/backtest',
    method: 'get',
    params
  })
}

// 获取统计数据
export function getStatistics(params) {
  return request({
    url: '/api/draw/statistics',
    method: 'get',
    params: {
      start_date: params.startDate,
      end_date: params.endDate,
      year: params.year,
      start_period: params.startPeriod,
      end_period: params.endPeriod
    }
  })
}

// 更新开奖数据
export function updateDraw(data) {
  return request({
    url: '/api/draw/update',
    method: 'put',
    data
  })
}

// 导出数据
export function exportDraws(params) {
  return request({
    url: '/api/draw/export',
    method: 'get',
    params: {
      start_period: params.startPeriod,
      end_period: params.endPeriod,
      start_date: params.startDate,
      end_date: params.endDate,
      number: params.number,
      zodiac: params.zodiac,
      color: params.color ? params.color.replace('波', '') : undefined,
      odd_even: params.oddEven,
      big_small: params.bigSmall,
      tail_big_small: params.tailBigSmall,
      sum_odd_even: params.sumOddEven,
      animal_type: params.animalType,
      wuxing: params.wuxing
    },
    responseType: 'blob'
  })
}

// 导入数据
export function importDraws(data) {
  return request({
    url: '/api/draw/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request.get('/api/user/info')
}

// 获取投注历史
export function getBettingHistory(params) {
  return request.get('/api/user/betting/history', { params })
}

// 获取投注统计
export function getBettingStats() {
  return request.get('/api/user/betting/stats')
}

// 提交投注
export function submitBet(data) {
  return request.post('/api/user/betting', data)
}

// 获取号码属性
export function getNumberAttributes(number) {
  return request({
    url: `/api/draw/number-attributes/${number}`,
    method: 'get'
  })
}

// 获取选号建议
export function getNumberSuggestions(params) {
  return request({
    url: '/api/draw/suggestions',
    method: 'get',
    params
  })
}

// 获取智能分析结果
export function getAnalysis(params) {
  return request({
    url: '/api/draw/analysis',
    method: 'get',
    params
  })
}

// 获取模型训练历史
export function getTrainingHistory(params) {
  return request.get('/api/draw/training/history', { params })
}

// 获取模型性能指标
export function getModelMetrics(params) {
  return request.get('/api/draw/model/metrics', { params })
}

// 获取模型参数
export function getModelParameters(modelId) {
  return request.get(`/api/draw/model/${modelId}/parameters`)
}

// 更新模型参数
export function updateModelParameters(modelId, data) {
  return request.put(`/api/draw/model/${modelId}/parameters`, data)
}

// 获取预测历史
export function getPredictionHistory(params) {
  return request.get('/api/draw/history', { params })
}

// 获取预测准确度统计
export function getPredictionAccuracy(params) {
  return request.get('/api/draw/accuracy', { params })
}

export function getDraws(params) {
  return request({
    url: '/api/draw',
    method: 'get',
    params
  })
}