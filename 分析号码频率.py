#!/usr/bin/env python3
"""
分析号码频率，检查为什么16、17、19不是最冷的
"""
import json

def analyze_number_frequency():
    """分析号码频率"""
    print("=== 号码频率详细分析 ===")
    
    try:
        with open('temp_stats.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        number_freq = data['data']['numberFrequency']
        
        print("📊 所有号码出现次数:")
        all_counts = []
        for num in range(1, 50):
            count = number_freq.get(str(num), 0)
            all_counts.append((num, count))
            print(f'   号码{num:02d}: {count}次')
        
        # 按出现次数排序
        all_counts.sort(key=lambda x: x[1])
        
        print("\n🔍 按出现次数排序 (从少到多):")
        for num, count in all_counts[:10]:
            print(f'   号码{num:02d}: {count}次')
        
        # 检查零次出现的号码
        zero_count = [num for num, count in all_counts if count == 0]
        
        if zero_count:
            print(f"\n❄️ 出现0次的号码: {zero_count}")
        else:
            print("\n✅ 没有出现0次的号码")
        
        # 最少出现次数
        min_count = min(count for _, count in all_counts)
        min_numbers = [num for num, count in all_counts if count == min_count]
        
        print(f"\n🧊 最少出现次数: {min_count}次")
        print(f"🔢 最少出现的号码: {min_numbers}")
        
        # API返回的冷门号码
        print("\n📋 API返回的冷门号码:")
        cold_numbers = data['data']['basicStats']['coldNumbers']
        for i, num_data in enumerate(cold_numbers[:10]):
            print(f'   {i+1}. 号码{num_data["number"]:02d}: {num_data["count"]}次')
        
        # 检查特定号码
        check_numbers = [16, 17, 19]
        print(f"\n🔍 检查特定号码 {check_numbers}:")
        for num in check_numbers:
            count = number_freq.get(str(num), 0)
            print(f'   号码{num:02d}: {count}次')
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_backend_logic():
    """检查后端逻辑"""
    print("\n=== 后端逻辑检查 ===")
    
    # 检查后端统计逻辑是否正确
    print("🔍 可能的问题:")
    print("   1. 后端统计逻辑可能有问题")
    print("   2. 数据库中可能没有这些号码的记录")
    print("   3. 统计范围可能有限制")
    print("   4. 特码范围定义可能不包含这些号码")

def analyze_draw_data():
    """分析开奖数据"""
    print("\n=== 开奖数据分析 ===")
    
    try:
        # 获取最近的开奖数据
        import requests
        response = requests.get("http://localhost:8000/api/draw/list?page=1&page_size=50")
        draws = response.json()['data']['items']
        
        print("📊 最近50期开奖号码分析:")
        all_numbers = []
        
        for draw in draws[:10]:  # 分析前10期
            if draw.get('open_code'):
                numbers = [int(x.strip()) for x in draw['open_code'].split(',')]
                all_numbers.extend(numbers)
                print(f"   期号{draw['expect']}: {numbers}")
        
        # 统计这些号码的出现情况
        from collections import Counter
        counter = Counter(all_numbers)
        
        print(f"\n🔢 最近10期号码统计:")
        for num in sorted(counter.keys()):
            print(f"   号码{num:02d}: {counter[num]}次")
        
        # 检查16、17、19是否出现
        check_numbers = [16, 17, 19]
        print(f"\n🔍 检查号码 {check_numbers} 在最近10期的出现情况:")
        for num in check_numbers:
            count = counter.get(num, 0)
            print(f"   号码{num:02d}: {count}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 开奖数据分析失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 号码频率分析开始...")
    
    # 分析号码频率
    analyze_number_frequency()
    
    # 检查后端逻辑
    check_backend_logic()
    
    # 分析开奖数据
    analyze_draw_data()
    
    print("\n📋 结论:")
    print("   如果16、17、19确实没有出现过，但不是最冷的，可能原因:")
    print("   1. 后端统计逻辑错误")
    print("   2. 数据库数据不完整")
    print("   3. 特码范围定义问题")
    print("   4. 统计时间范围限制")

if __name__ == "__main__":
    main()
