from sqlalchemy.orm import Session
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from app.models.draw import Draw
from app.models.prediction import Prediction
from app.models.model_training_history import ModelTrainingHistory
from app.services.feature_engineering import FeatureEngineering
from app.core.cache import cache, CacheManager
import joblib
import os

# 模型文件路径
MODEL_DIR = "models"
os.makedirs(MODEL_DIR, exist_ok=True)


class ModelEnsemble:
    """模型集成类"""

    def __init__(self):
        self.models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=200,
                max_depth=5,
                learning_rate=0.1,
                random_state=42
            ),
            'neural_network': MLPClassifier(
                hidden_layer_sizes=(100, 50),
                max_iter=1000,
                random_state=42
            )
        }
        self.scaler = StandardScaler()
        self.version = None  # 模型版本

    def train(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """训练模型"""
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)

        # 训练各个模型
        metrics = {}
        for name, model in self.models.items():
            model.fit(X_scaled, y)
            score = model.score(X_scaled, y)
            metrics[name] = score

        return metrics

    def predict(self, X: np.ndarray) -> Tuple[List[int], float]:
        """集成预测"""
        # 标准化特征
        X_scaled = self.scaler.transform(X)

        # 获取各个模型的预测概率
        predictions = []
        for model in self.models.values():
            pred_proba = model.predict_proba(X_scaled)[0]
            predictions.append(pred_proba)

        # 集成预测结果
        ensemble_proba = np.mean(predictions, axis=0)
        top_5_indices = np.argsort(ensemble_proba)[-5:][::-1]
        predicted_numbers = [int(i) + 1 for i in top_5_indices]
        confidence = float(ensemble_proba[top_5_indices[0]])

        return predicted_numbers, confidence

    def save(self, version: str):
        """保存模型"""
        self.version = version
        # 保存各个模型
        for name, model in self.models.items():
            joblib.dump(model, os.path.join(
                MODEL_DIR, f"{name}_{version}.joblib"))

        # 保存标准化器
        joblib.dump(self.scaler, os.path.join(
            MODEL_DIR, f"scaler_{version}.joblib"))

    @classmethod
    def load(cls, version: str) -> 'ModelEnsemble':
        """加载模型"""
        ensemble = cls()
        ensemble.version = version

        # 加载各个模型
        for name in ensemble.models.keys():
            model_path = os.path.join(MODEL_DIR, f"{name}_{version}.joblib")
            if os.path.exists(model_path):
                ensemble.models[name] = joblib.load(model_path)

        # 加载标准化器
        scaler_path = os.path.join(MODEL_DIR, f"scaler_{version}.joblib")
        if os.path.exists(scaler_path):
            ensemble.scaler = joblib.load(scaler_path)

        return ensemble


@cache(expire=3600, prefix="prepare_training_data")
async def prepare_training_data(db: Session, lookback: int = 10) -> Tuple[np.ndarray, np.ndarray]:
    """准备训练数据"""
    # 获取所有开奖记录
    draws = db.query(Draw).order_by(Draw.draw_time).all()

    if len(draws) < lookback + 1:
        return None, None

    # 使用特征工程提取特征
    X, y = FeatureEngineering.extract_all_features(draws)

    return X, y


async def train_model(db: Session) -> bool:
    """训练模型"""
    try:
        # 准备训练数据
        X, y = await prepare_training_data(db)
        if X is None or y is None:
            return False

        # 创建并训练模型集成
        ensemble = ModelEnsemble()
        metrics = ensemble.train(X, y)

        # 生成版本号
        version = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存模型
        ensemble.save(version)

        # 记录训练历史
        history = ModelTrainingHistory(
            training_time=datetime.now(),
            model_name="ensemble",
            parameters={
                "lookback": 10,
                "models": list(ensemble.models.keys())
            },
            metrics=metrics,
            status="success",
            data_count=len(X)
        )
        db.add(history)
        db.commit()

        # 清除相关缓存
        CacheManager.clear_pattern("prepare_training_data:*")

        return True
    except Exception as e:
        print(f"训练模型时出错: {str(e)}")
        return False


def load_model(version: Optional[str] = None) -> Optional[ModelEnsemble]:
    """加载模型"""
    try:
        if version is None:
            # 获取最新版本
            model_files = [f for f in os.listdir(
                MODEL_DIR) if f.startswith("random_forest_")]
            if not model_files:
                return None
            version = sorted(model_files)[-1].split("_")[-1].split(".")[0]

        return ModelEnsemble.load(version)
    except Exception as e:
        print(f"加载模型时出错: {str(e)}")
        return None


@cache(expire=300, prefix="next_prediction")
async def get_next_prediction(db: Session) -> Optional[Prediction]:
    """获取下一期预测"""
    try:
        # 加载模型
        model = load_model()
        if model is None:
            # 如果模型不存在，先训练模型
            if not await train_model(db):
                return None
            model = load_model()

        # 获取最新的开奖记录
        latest_draws = db.query(Draw).order_by(
            Draw.draw_time.desc()).limit(10).all()
        if len(latest_draws) < 10:
            return None

        # 准备预测数据
        features = FeatureEngineering.prepare_prediction_features(latest_draws)

        # 进行预测
        predicted_numbers, confidence = model.predict(features)

        # 创建预测记录
        next_prediction = Prediction(
            expect=f"2025{len(latest_draws) + 1:03d}",  # 生成下一期期号
            predicted_numbers=predicted_numbers,
            confidence=confidence,
            model_version=model.version,
            prediction_time=datetime.now(),
            is_correct=0
        )

        # 保存预测记录
        db.add(next_prediction)
        db.commit()

        return next_prediction
    except Exception as e:
        print(f"预测时出错: {str(e)}")
        return None


async def update_prediction_accuracy(db: Session, draw: Draw) -> None:
    """更新预测准确率"""
    try:
        # 获取对应的预测记录
        prediction = db.query(Prediction).filter(
            Prediction.expect == draw.expect
        ).first()

        if prediction:
            # 检查预测是否正确
            is_correct = 1 if draw.special_number in prediction.predicted_numbers else 0
            prediction.is_correct = is_correct
            db.commit()

            # 清除相关缓存
            CacheManager.clear_pattern("next_prediction:*")
    except Exception as e:
        print(f"更新预测准确率时出错: {str(e)}")


@cache(expire=3600, prefix="prediction_accuracy")
async def get_prediction_accuracy(db: Session) -> Dict:
    """获取预测准确率统计"""
    try:
        # 获取所有预测记录
        predictions = db.query(Prediction).all()

        if not predictions:
            return {"accuracy": 0.0, "total_predictions": 0}

        # 计算准确率
        total = len(predictions)
        correct = sum(1 for p in predictions if p.is_correct == 1)
        accuracy = correct / total if total > 0 else 0.0

        return {
            "accuracy": accuracy,
            "total_predictions": total,
            "correct_predictions": correct
        }
    except Exception as e:
        print(f"获取预测准确率统计时出错: {str(e)}")
        return {"accuracy": 0.0, "total_predictions": 0}
