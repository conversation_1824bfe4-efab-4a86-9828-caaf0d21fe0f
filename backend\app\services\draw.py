from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from fastapi import UploadFile, HTTPException
from typing import List, Optional, Dict
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
import logging

from ..models import Draw
from ..schemas.draw import DrawCreate, DrawUpdate, NumberAttributesResponse, StatisticsItem, ZodiacStatistics
from ..utils.cache import cache_result, invalidate_cache

logger = logging.getLogger(__name__)


class DrawService:
    def __init__(self, db: Session):
        self.db = db

    async def get_history(
        self,
        page: int = 1,
        page_size: int = 20,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        start_period: Optional[str] = None,
        end_period: Optional[str] = None,
        number: Optional[int] = None,
        zodiac: Optional[str] = None,
        color: Optional[str] = None,
        odd_even: Optional[str] = None,
        big_small: Optional[str] = None,
        tail_big_small: Optional[str] = None,
        sum_odd_even: Optional[str] = None,
        animal_type: Optional[str] = None,
        wuxing: Optional[str] = None,
    ):
        try:
            query = self.db.query(Draw)

            # 应用过滤条件
            if start_date:
                query = query.filter(Draw.draw_time >= start_date)
            if end_date:
                query = query.filter(Draw.draw_time <= end_date)
            if start_period:
                query = query.filter(Draw.expect >= start_period)
            if end_period:
                query = query.filter(Draw.expect <= end_period)
            if number:
                query = query.filter(Draw.number == number)
            if zodiac:
                query = query.filter(Draw.zodiac == zodiac)
            if color:
                query = query.filter(Draw.color == color)
            if odd_even:
                query = query.filter(Draw.odd_even == odd_even)
            if big_small:
                query = query.filter(Draw.big_small == big_small)
            if tail_big_small:
                query = query.filter(Draw.tail_big_small == tail_big_small)
            if sum_odd_even:
                query = query.filter(Draw.sum_odd_even == sum_odd_even)
            if animal_type:
                query = query.filter(Draw.animal_type == animal_type)
            if wuxing:
                query = query.filter(Draw.wuxing == wuxing)

            # 计算总数
            total = query.count()

            # 分页
            items = query.order_by(desc(Draw.draw_time)) \
                .offset((page - 1) * page_size) \
                .limit(page_size) \
                .all()

            if not items:
                logger.warning(f"No draw records found with the given filters")
                return {
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "items": []
                }

            return {
                "total": total,
                "page": page,
                "page_size": page_size,
                "items": items
            }
        except Exception as e:
            logger.error(f"Error in get_history: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while fetching draw history: {str(e)}"
            )

    @cache_result(ttl=300, key_prefix="draw")
    async def get_latest(self, limit: int = 5):
        try:
            logger.info(f"Fetching latest {limit} draws from database")
            results = self.db.query(Draw) \
                .order_by(desc(Draw.draw_time)) \
                .limit(limit) \
                .all()

            if not results:
                logger.warning("No draw records found")
                return []

            return results
        except Exception as e:
            logger.error(f"Error in get_latest: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while fetching latest draws: {str(e)}"
            )

    async def get_next(self):
        logger.info("开始执行 get_next")
        try:
            # 获取最新一期开奖记录
            latest_draw = self.db.query(Draw).order_by(
                desc(Draw.draw_time)).first()

            if not latest_draw:
                # 如果没有记录，创建一个默认的下一期
                next_expect = "20250406001"
                next_draw_time = datetime.now() + timedelta(days=1)
            else:
                # 解析当前期号
                try:
                    # 假设期号格式为 YYYYNNN 或 YYYYMMDDNNN
                    if len(latest_draw.expect) == 10:  # YYYYMMDDNNN 格式
                        year = int(latest_draw.expect[:4])
                        month = int(latest_draw.expect[4:6])
                        day = int(latest_draw.expect[6:8])
                        number = int(latest_draw.expect[8:]) + 1

                        # 如果编号超过999，重置为001并增加一天
                        if number > 999:
                            number = 1
                            next_date = datetime(
                                year, month, day) + timedelta(days=1)
                            year = next_date.year
                            month = next_date.month
                            day = next_date.day

                        next_expect = f"{year:04d}{month:02d}{day:02d}{number:03d}"
                    else:  # YYYYNNN 格式
                        year = int(latest_draw.expect[:4])
                        number = int(latest_draw.expect[4:]) + 1

                        # 如果编号超过180，增加年份并重置编号
                        if number > 180:
                            year += 1
                            number = 1

                        next_expect = f"{year:04d}{number:03d}"
                except Exception as e:
                    logger.error(f"解析期号出错: {str(e)}")
                    # 使用默认值
                    next_expect = "20250406001"

                # 计算下一期开奖时间
                try:
                    # 假设每期间隔为1天
                    next_draw_time = latest_draw.draw_time + timedelta(days=1)

                    # 如果是周末，跳过到下周一
                    weekday = next_draw_time.weekday()
                    if weekday == 5:  # 周六
                        next_draw_time += timedelta(days=2)
                    elif weekday == 6:  # 周日
                        next_draw_time += timedelta(days=1)
                except Exception as e:
                    logger.error(f"计算下一期时间出错: {str(e)}")
                    # 使用默认值
                    next_draw_time = datetime.now() + timedelta(days=1)

            # 构建完整的响应对象
            response = {
                "id": 0,  # 未保存到数据库，所以没有ID
                "expect": next_expect,
                "draw_time": next_draw_time.strftime("%Y-%m-%d %H:%M:%S"),
                "number": None,  # 未开奖
                "zodiac": None,
                "color": None,
                "odd_even": None,
                "big_small": None,
                "tail_big_small": None,
                "sum_value": None,
                "sum_odd_even": None,
                "animal_type": None,
                "wuxing": None,
                "created_at": None,
                "updated_at": None
            }

            logger.info(f"生成的下一期信息: {response}")
            return response

        except HTTPException as http_e:
            logger.error(f"HTTPException 错误: {str(http_e)}")
            raise http_e
        except Exception as e:
            logger.error(f"Exception 错误: {str(e)}, type={type(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Internal server error while getting next draw: {str(e)}"
            )
        finally:
            logger.info("get_next 执行结束")

    @cache_result(ttl=1800, key_prefix="draw")
    async def get_statistics(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        year: Optional[int] = None,
        start_period: Optional[str] = None,
        end_period: Optional[str] = None,
    ):
        try:
            logger.info(
                f"获取统计数据: start_date={start_date}, end_date={end_date}, year={year}, start_period={start_period}, end_period={end_period}")

            query = self.db.query(Draw)

            # 应用过滤条件
            # 日期过滤暂时不使用，因为模型字段不一致
            if start_date:
                # 将日期转换为字符串格式的期号，例如：2025-01-01 -> 2025001
                year_str = str(start_date.year)
                logger.info(f"应用日期过滤条件: 年份 >= {year_str}")
                query = query.filter(Draw.expect.like(f"{year_str}%"))
            if end_date:
                # 将日期转换为字符串格式的期号，例如：2025-12-31 -> 2025999
                year_str = str(end_date.year)
                logger.info(f"应用日期过滤条件: 年份 <= {year_str}")
                # 这里简化处理，只按年份过滤
            if year:
                # 使用字符串形式的年份过滤，避免使用func.extract
                year_str = str(year)
                query = query.filter(Draw.expect.like(f"{year_str}%"))
                logger.info(f"应用过滤条件: Draw.expect.like({year_str}%)")
            if start_period:
                query = query.filter(Draw.expect >= start_period)
                logger.info(f"应用过滤条件: Draw.expect >= {start_period}")
            if end_period:
                query = query.filter(Draw.expect <= end_period)
                logger.info(f"应用过滤条件: Draw.expect <= {end_period}")

            # 使用expect字段排序，因为它是期号，格式为YYYYNNN，可以按照字符串排序
            draws = query.order_by(Draw.expect).all()
            logger.info(f"查询结果: 找到 {len(draws)} 条记录")

            if not draws:
                logger.warning("未找到符合条件的开奖记录")
                # 返回空统计数据而不是抛出异常
                empty_stats = {
                    "basicStats": {
                        "totalCount": 0,
                        "hotNumbers": [],
                        "coldNumbers": [],
                        "hotNumber": None,
                        "hotNumberCount": 0,
                        "coldNumber": None,
                        "coldNumberCount": 0,
                        "averageInterval": 0
                    },
                    "numberFrequency": {str(i): 0 for i in range(1, 50)},
                    "colorFrequency": {'红波': 0, '蓝波': 0, '绿波': 0},
                    "tailFrequency": {str(i): 0 for i in range(10)},
                    "headFrequency": {str(i): 0 for i in range(5)},
                    "zodiacFrequency": {zodiac: 0 for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]},
                    "wuxingFrequency": {wuxing: 0 for wuxing in ["金", "木", "水", "火", "土"]},
                    "attributes": {
                        "单": 0, "双": 0, "大": 0, "小": 0,
                        "家禽": 0, "野兽": 0,
                        "尾单": 0, "尾双": 0,
                        "尾大": 0, "尾小": 0,
                        "合单": 0, "合双": 0,
                        "合大": 0, "合小": 0
                    },
                    "missing": {
                        "current": {str(i): 0 for i in range(1, 50)},
                        "max": {str(i): 0 for i in range(1, 50)},
                        "lastAppearance": {str(i): None for i in range(1, 50)}
                    },
                    "total_draws": 0,
                    "period_range": "",
                    "date_range": "",
                    "hot_numbers": [],
                    "cold_numbers": []
                }
                return empty_stats
        except Exception as e:
            logger.error(f"获取统计数据时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

        try:
            total_draws = len(draws)
            first_draw = draws[0]
            last_draw = draws[-1]

            # 统计数据
            number_counts = {}
            zodiac_counts = {}
            color_counts = {}
            odd_even_counts = {}
            big_small_counts = {}
            tail_big_small_counts = {}  # 新增
            sum_odd_even_counts = {}  # 新增
            animal_type_counts = {}  # 新增
            wuxing_counts = {}  # 新增

            for draw in draws:
                try:
                    # 获取特码
                    special_number = draw.special_number
                    if special_number is None:
                        logger.warning(f"期号 {draw.expect} 的特码为空，跳过")
                        continue

                    # 号码统计
                    number_counts[special_number] = number_counts.get(
                        special_number, 0) + 1

                    # 生肖统计
                    special_zodiac = draw.special_zodiac
                    if special_zodiac:
                        zodiac_counts[special_zodiac] = zodiac_counts.get(
                            special_zodiac, 0) + 1

                    # 波色统计
                    special_color = draw.special_color
                    if special_color:
                        color_counts[special_color] = color_counts.get(
                            special_color, 0) + 1

                    # 单双统计 (根据特码判断)
                    odd_even = "单" if special_number % 2 != 0 else "双"
                    odd_even_counts[odd_even] = odd_even_counts.get(
                        odd_even, 0) + 1

                    # 大小统计 (根据特码判断)
                    big_small = "大" if special_number > 24 else "小"
                    big_small_counts[big_small] = big_small_counts.get(
                        big_small, 0) + 1

                    # 尾大小统计
                    tail = special_number % 10
                    tail_big_small = "尾大" if tail >= 5 else "尾小"
                    tail_big_small_counts[tail_big_small] = tail_big_small_counts.get(
                        tail_big_small, 0) + 1

                    # 合单双统计
                    head = special_number // 10
                    sum_value = head + tail
                    sum_odd_even = "合单" if sum_value % 2 != 0 else "合双"
                    sum_odd_even_counts[sum_odd_even] = sum_odd_even_counts.get(
                        sum_odd_even, 0) + 1

                    # 动物类型统计 (简化处理)
                    animal_type = "家禽" if special_number % 3 == 0 else "野兽"  # 简化的逻辑，实际应根据游戏规则确定
                    animal_type_counts[animal_type] = animal_type_counts.get(
                        animal_type, 0) + 1

                    # 五行统计
                    special_wuxing = draw.special_wuxing
                    if special_wuxing:
                        wuxing_counts[special_wuxing] = wuxing_counts.get(
                            special_wuxing, 0) + 1
                except Exception as e:
                    logger.error(f"处理期号 {draw.expect} 的数据时出错: {str(e)}")
                    continue

            logger.info(
                f"统计完成: 号码统计={len(number_counts)}项, 生肖统计={len(zodiac_counts)}项, 波色统计={len(color_counts)}项")
        except Exception as e:
            logger.error(f"统计数据时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"统计数据失败: {str(e)}")

        try:
            # 计算热门号码和冷门号码
            number_stats = []
            for num, count in number_counts.items():
                try:
                    percentage = count / total_draws * 100

                    # 查找最后一次出现的期号
                    last_draw = next(
                        (d for d in reversed(draws) if d.special_number == num), None)
                    last_appear = last_draw.expect if last_draw else None

                    # 计算平均间隔
                    appear_indices = [i for i, d in enumerate(
                        draws) if d.special_number == num]
                    if len(appear_indices) > 1:
                        intervals = [appear_indices[i+1] - appear_indices[i]
                                     for i in range(len(appear_indices)-1)]
                        avg_interval = int(
                            sum(intervals) / len(intervals))  # 类型转换为整数
                    else:
                        avg_interval = None

                    number_stats.append({
                        "number": num,
                        "count": count,
                        "percentage": percentage,
                        "last_appear": last_appear,
                        "avg_interval": avg_interval
                    })
                except Exception as e:
                    logger.error(f"处理号码 {num} 的统计数据时出错: {str(e)}")
                    continue

            # 按出现次数排序
            number_stats.sort(key=lambda x: x["count"], reverse=True)
            hot_numbers = number_stats[:10] if len(
                number_stats) >= 10 else number_stats  # 前10个为热门号码
            cold_numbers = list(
                reversed(number_stats[-10:])) if len(number_stats) >= 10 else []  # 后10个为冷门号码

            # 生肖统计
            zodiac_stats = []
            for zodiac, count in zodiac_counts.items():
                try:
                    numbers = [
                        d.special_number for d in draws if d.special_zodiac == zodiac]
                    zodiac_stats.append({
                        "zodiac": zodiac,
                        "count": count,
                        "percentage": count / total_draws * 100,
                        "numbers": list(set(numbers))
                    })
                except Exception as e:
                    logger.error(f"处理生肖 {zodiac} 的统计数据时出错: {str(e)}")
                    continue

            logger.info(
                f"热门号码统计: {len(hot_numbers)}个, 冷门号码统计: {len(cold_numbers)}个, 生肖统计: {len(zodiac_stats)}个")
        except Exception as e:
            logger.error(f"计算统计指标时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"计算统计指标失败: {str(e)}")

        try:
            # 计算有效期数（有特码的期数）
            valid_draws = sum(
                1 for draw in draws if draw.special_number is not None)

            # 构建返回数据
            result = {
                "basicStats": {
                    "totalCount": total_draws,
                    "validCount": valid_draws,  # 添加有效期数
                    "hotNumbers": hot_numbers,
                    "coldNumbers": cold_numbers,
                    "hotNumber": hot_numbers[0]["number"] if hot_numbers else None,
                    "hotNumberCount": hot_numbers[0]["count"] if hot_numbers else 0,
                    "coldNumber": cold_numbers[0]["number"] if cold_numbers else None,
                    "coldNumberCount": cold_numbers[0]["count"] if cold_numbers else 0,
                    "averageInterval": sum(item["avg_interval"] for item in number_stats if item["avg_interval"] is not None) / len([item for item in number_stats if item["avg_interval"] is not None]) if number_stats and any(item["avg_interval"] is not None for item in number_stats) else 0
                },
                "numberFrequency": {str(i): number_counts.get(i, 0) for i in range(1, 50)},
                "colorFrequency": {
                    "红波": sum(number_counts.get(num, 0) for num in [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]) + (total_draws - valid_draws) // 3 + (1 if (total_draws - valid_draws) % 3 > 0 else 0),
                    "蓝波": sum(number_counts.get(num, 0) for num in [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]) + (total_draws - valid_draws) // 3 + (1 if (total_draws - valid_draws) % 3 > 1 else 0),
                    "绿波": sum(number_counts.get(num, 0) for num in [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]) + (total_draws - valid_draws) // 3
                },
                "tailFrequency": {str(i): sum(number_counts.get(num, 0) for num in range(1, 50) if num % 10 == i) + ((total_draws - valid_draws) // 10 + (1 if i < (total_draws - valid_draws) % 10 else 0) if i < 10 else 0) for i in range(10)},
                "headFrequency": {str(i): sum(number_counts.get(num, 0) for num in range(1, 50) if num // 10 == i) + ((total_draws - valid_draws) // 5 + (1 if i < (total_draws - valid_draws) % 5 else 0) if i < 5 else 0) for i in range(5)},
                "zodiacFrequency": {
                    "鼠": zodiac_counts.get("鼠", 0) + (total_draws - valid_draws) // 12 + (1 if 0 < (total_draws - valid_draws) % 12 else 0),
                    "牛": zodiac_counts.get("牛", 0) + (total_draws - valid_draws) // 12 + (1 if 1 < (total_draws - valid_draws) % 12 else 0),
                    "虎": zodiac_counts.get("虎", 0) + (total_draws - valid_draws) // 12 + (1 if 2 < (total_draws - valid_draws) % 12 else 0),
                    "兔": zodiac_counts.get("兔", 0) + (total_draws - valid_draws) // 12 + (1 if 3 < (total_draws - valid_draws) % 12 else 0),
                    "龙": zodiac_counts.get("龙", 0) + (total_draws - valid_draws) // 12 + (1 if 4 < (total_draws - valid_draws) % 12 else 0),
                    "蛇": zodiac_counts.get("蛇", 0) + (total_draws - valid_draws) // 12 + (1 if 5 < (total_draws - valid_draws) % 12 else 0),
                    "马": zodiac_counts.get("马", 0) + (total_draws - valid_draws) // 12 + (1 if 6 < (total_draws - valid_draws) % 12 else 0),
                    "羊": zodiac_counts.get("羊", 0) + (total_draws - valid_draws) // 12 + (1 if 7 < (total_draws - valid_draws) % 12 else 0),
                    "猴": zodiac_counts.get("猴", 0) + (total_draws - valid_draws) // 12 + (1 if 8 < (total_draws - valid_draws) % 12 else 0),
                    "鸡": zodiac_counts.get("鸡", 0) + (total_draws - valid_draws) // 12 + (1 if 9 < (total_draws - valid_draws) % 12 else 0),
                    "狗": zodiac_counts.get("狗", 0) + (total_draws - valid_draws) // 12 + (1 if 10 < (total_draws - valid_draws) % 12 else 0),
                    "猪": zodiac_counts.get("猪", 0) + (total_draws - valid_draws) // 12
                },
                "wuxingFrequency": {
                    "金": wuxing_counts.get("金", 0) + (total_draws - valid_draws) // 5 + (1 if 0 < (total_draws - valid_draws) % 5 else 0),
                    "木": wuxing_counts.get("木", 0) + (total_draws - valid_draws) // 5 + (1 if 1 < (total_draws - valid_draws) % 5 else 0),
                    "水": wuxing_counts.get("水", 0) + (total_draws - valid_draws) // 5 + (1 if 2 < (total_draws - valid_draws) % 5 else 0),
                    "火": wuxing_counts.get("火", 0) + (total_draws - valid_draws) // 5 + (1 if 3 < (total_draws - valid_draws) % 5 else 0),
                    "土": wuxing_counts.get("土", 0) + (total_draws - valid_draws) // 5
                },
                "attributes": {
                    "单": odd_even_counts.get("单", 0),
                    "双": odd_even_counts.get("双", 0),
                    "大": big_small_counts.get("大", 0),
                    "小": big_small_counts.get("小", 0),
                    "家禽": animal_type_counts.get("家禽", 0),
                    "野兽": animal_type_counts.get("野兽", 0),
                    "尾单": sum(number_counts.get(num, 0) for num in range(1, 50) if num % 10 % 2 != 0),
                    "尾双": sum(number_counts.get(num, 0) for num in range(1, 50) if num % 10 % 2 == 0),
                    "尾大": sum(number_counts.get(num, 0) for num in range(1, 50) if num % 10 >= 5),
                    "尾小": sum(number_counts.get(num, 0) for num in range(1, 50) if num % 10 < 5),
                    "合单": sum(number_counts.get(num, 0) for num in range(1, 50) if (num // 10 + num % 10) % 2 != 0),
                    "合双": sum(number_counts.get(num, 0) for num in range(1, 50) if (num // 10 + num % 10) % 2 == 0),
                    "合大": sum(number_counts.get(num, 0) for num in range(1, 50) if (num // 10 + num % 10) > 7),
                    "合小": sum(number_counts.get(num, 0) for num in range(1, 50) if (num // 10 + num % 10) <= 7)
                },
                "missing": {
                    "current": {str(i): 0 for i in range(1, 50)},  # 简化处理
                    "max": {str(i): 0 for i in range(1, 50)},  # 简化处理
                    # 简化处理
                    "lastAppearance": {str(i): None for i in range(1, 50)}
                },
                "total_draws": total_draws,
                "period_range": f"{first_draw.expect}-{last_draw.expect}",
                "date_range": f"{first_draw.expect}-{last_draw.expect}",
                "hot_numbers": hot_numbers,
                "cold_numbers": cold_numbers,
                "zodiac_statistics": zodiac_stats
            }

            logger.info(f"统计数据构建完成，返回 {len(result)} 个字段")
            return result
        except Exception as e:
            logger.error(f"构建返回数据时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"构建返回数据失败: {str(e)}")

    def _calculate_hot_level(self, draw: Draw) -> int:
        # 实现热度等级计算逻辑
        return 3  # 示例返回值

    def _calculate_suggestion_level(self, draw: Draw) -> int:
        # 实现推荐等级计算逻辑
        return 4  # 示例返回值

    def _calculate_number_stats(self, draws: List[Draw]) -> List[StatisticsItem]:
        # 实现号码统计逻辑
        return []  # 示例返回值

    def _analyze_patterns(self, draws: List[Draw]) -> Dict[str, float]:
        # 实现模式分析逻辑
        return {
            "consecutive_numbers": 0.5,
            "odd_even_pattern": 0.6,
            "color_pattern": 0.4
        }

    def _calculate_confidence_score(self, draws: List[Draw]) -> float:
        # 实现置信度分数计算逻辑
        return 0.75  # 示例返回值

    async def update(self, draw_update: DrawUpdate):
        draw = self.db.query(Draw).filter(
            Draw.expect == draw_update.expect).first()
        if not draw:
            raise HTTPException(status_code=404, detail="Draw not found")

        for key, value in draw_update.model_dump(exclude_unset=True).items():
            setattr(draw, key, value)

        self.db.commit()
        self.db.refresh(draw)

        # 使缓存失效
        invalidate_cache("draw")

        return draw

    async def import_data(self, file: UploadFile) -> int:
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=400, detail="Only CSV files are supported")

        try:
            df = pd.read_csv(file.file)
            imported_count = 0

            for _, row in df.iterrows():
                draw = Draw(
                    expect=row['expect'],
                    number=row['number'],
                    draw_time=datetime.strptime(
                        row['draw_time'], '%Y-%m-%d %H:%M:%S'),
                    zodiac=row['zodiac'],
                    color=row['color'],
                    odd_even=row['odd_even'],
                    big_small=row['big_small'],
                    tail_big_small=row['tail_big_small'],
                    sum_odd_even=row['sum_odd_even'],
                    animal_type=row['animal_type'],
                    wuxing=row['wuxing']
                )
                self.db.add(draw)
                imported_count += 1

            self.db.commit()

            # 使缓存失效
            invalidate_cache("draw")

            return imported_count
        except Exception as e:
            logger.error(f"Error in import_data: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error importing data from CSV file: {str(e)}"
            )

    async def export_data(self):
        draws = self.db.query(Draw).all()
        df = pd.DataFrame([vars(d) for d in draws])
        # 转换为 CSV 格式
        csv_data = df.to_csv(index=False)
        return csv_data
