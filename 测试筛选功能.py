#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的筛选功能
"""

import requests
import json
import math

def test_filter_functionality():
    """测试筛选功能"""
    print("🔍 测试筛选功能...")
    
    # 测试数据：模拟49个号码的基本信息
    test_numbers = []
    
    # 生肖映射（基于GameRules2025.js）
    zodiac_map = {
        1: "蛇", 13: "蛇", 25: "蛇", 37: "蛇", 49: "蛇",
        2: "龙", 14: "龙", 26: "龙", 38: "龙",
        3: "兔", 15: "兔", 27: "兔", 39: "兔",
        4: "虎", 16: "虎", 28: "虎", 40: "虎",
        5: "牛", 17: "牛", 29: "牛", 41: "牛",
        6: "鼠", 18: "鼠", 30: "鼠", 42: "鼠",
        7: "猪", 19: "猪", 31: "猪", 43: "猪",
        8: "狗", 20: "狗", 32: "狗", 44: "狗",
        9: "鸡", 21: "鸡", 33: "鸡", 45: "鸡",
        10: "猴", 22: "猴", 34: "猴", 46: "猴",
        11: "羊", 23: "羊", 35: "羊", 47: "羊",
        12: "马", 24: "马", 36: "马", 48: "马"
    }
    
    # 波色映射
    red_numbers = [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46]
    blue_numbers = [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48]
    green_numbers = [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
    
    # 五行映射
    wuxing_map = {
        3: "金", 4: "金", 11: "金", 12: "金", 25: "金", 26: "金", 33: "金", 34: "金", 41: "金", 42: "金",
        7: "木", 8: "木", 15: "木", 16: "木", 23: "木", 24: "木", 37: "木", 38: "木", 45: "木", 46: "木",
        13: "水", 14: "水", 21: "水", 22: "水", 29: "水", 30: "水", 43: "水", 44: "水",
        1: "火", 2: "火", 9: "火", 10: "火", 17: "火", 18: "火", 31: "火", 32: "火", 39: "火", 40: "火", 47: "火", 48: "火",
        5: "土", 6: "土", 19: "土", 20: "土", 27: "土", 28: "土", 35: "土", 36: "土", 49: "土"
    }
    
    def is_prime(n):
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(math.sqrt(n)) + 1, 2):
            if n % i == 0:
                return False
        return True
    
    # 生成测试数据
    for number in range(1, 50):
        # 基本属性
        zodiac = zodiac_map.get(number, '未知')
        
        if number in red_numbers:
            color = '红波'
        elif number in blue_numbers:
            color = '蓝波'
        elif number in green_numbers:
            color = '绿波'
        else:
            color = '未知'
        
        wuxing = wuxing_map.get(number, '未知')
        
        # 计算属性
        is_odd = number % 2 == 1
        is_big = number >= 25
        is_prime_num = is_prime(number)
        
        # 模拟统计数据
        count = (number % 7) + 1  # 1-7次
        missing = (number * 3) % 60  # 0-59期遗漏
        
        test_numbers.append({
            'number': number,
            'zodiac': zodiac,
            'color': color,
            'wuxing': wuxing,
            'is_odd': is_odd,
            'is_big': is_big,
            'is_prime': is_prime_num,
            'count': count,
            'missing': missing
        })
    
    return test_numbers

def test_filter_scenarios(test_data):
    """测试各种筛选场景"""
    print(f"\n📊 测试筛选场景（总数据：{len(test_data)}个号码）")
    
    # 场景1：筛选红波单数
    red_odd = [item for item in test_data if item['color'] == '红波' and item['is_odd']]
    print(f"🔴 红波单数：{len(red_odd)}个 - {[item['number'] for item in red_odd]}")
    
    # 场景2：筛选质数
    primes = [item for item in test_data if item['is_prime']]
    print(f"🔢 质数：{len(primes)}个 - {[item['number'] for item in primes]}")
    
    # 场景3：筛选龙生肖
    dragons = [item for item in test_data if item['zodiac'] == '龙']
    print(f"🐲 龙生肖：{len(dragons)}个 - {[item['number'] for item in dragons]}")
    
    # 场景4：筛选土五行
    earth = [item for item in test_data if item['wuxing'] == '土']
    print(f"🌍 土五行：{len(earth)}个 - {[item['number'] for item in earth]}")
    
    # 场景5：筛选大数
    big_numbers = [item for item in test_data if item['is_big']]
    print(f"📏 大数(≥25)：{len(big_numbers)}个 - {[item['number'] for item in big_numbers]}")
    
    # 场景6：筛选遗漏21-50期
    missing_21_50 = [item for item in test_data if 21 <= item['missing'] <= 50]
    print(f"⏰ 遗漏21-50期：{len(missing_21_50)}个 - {[item['number'] for item in missing_21_50]}")
    
    # 场景7：筛选出现3-4次
    count_3_4 = [item for item in test_data if 3 <= item['count'] <= 4]
    print(f"📊 出现3-4次：{len(count_3_4)}个 - {[item['number'] for item in count_3_4]}")
    
    # 场景8：组合筛选 - 绿波质数大数
    green_prime_big = [item for item in test_data 
                       if item['color'] == '绿波' and item['is_prime'] and item['is_big']]
    print(f"🌿 绿波质数大数：{len(green_prime_big)}个 - {[item['number'] for item in green_prime_big]}")

def test_smart_filters(test_data):
    """测试智能筛选功能"""
    print(f"\n🤖 测试智能筛选功能")
    
    # 智能筛选1：热门号码（模拟热度指数≥60）
    hot_numbers = [item for item in test_data if item['count'] >= 5]  # 出现5次以上视为热门
    print(f"🔥 热门号码：{len(hot_numbers)}个 - {[item['number'] for item in hot_numbers]}")
    
    # 智能筛选2：回补候选（遗漏21-50期）
    rebound_candidates = [item for item in test_data if 21 <= item['missing'] <= 50]
    print(f"🔄 回补候选：{len(rebound_candidates)}个 - {[item['number'] for item in rebound_candidates]}")
    
    # 智能筛选3：稳定号码（出现3-4次）
    stable_numbers = [item for item in test_data if 3 <= item['count'] <= 4]
    print(f"⚖️ 稳定号码：{len(stable_numbers)}个 - {[item['number'] for item in stable_numbers]}")
    
    # 智能筛选4：潜力号码（质数）
    potential_numbers = [item for item in test_data if item['is_prime']]
    print(f"💎 潜力号码：{len(potential_numbers)}个 - {[item['number'] for item in potential_numbers]}")

def test_filter_combinations(test_data):
    """测试筛选条件组合"""
    print(f"\n🔗 测试筛选条件组合")
    
    # 组合1：红波 + 单数 + 大数
    combo1 = [item for item in test_data 
              if item['color'] == '红波' and item['is_odd'] and item['is_big']]
    print(f"🔴➕ 红波+单数+大数：{len(combo1)}个 - {[item['number'] for item in combo1]}")
    
    # 组合2：蛇生肖 + 绿波 + 质数
    combo2 = [item for item in test_data 
              if item['zodiac'] == '蛇' and item['color'] == '绿波' and item['is_prime']]
    print(f"🐍➕ 蛇+绿波+质数：{len(combo2)}个 - {[item['number'] for item in combo2]}")
    
    # 组合3：土五行 + 遗漏>30期 + 出现≤2次
    combo3 = [item for item in test_data 
              if item['wuxing'] == '土' and item['missing'] > 30 and item['count'] <= 2]
    print(f"🌍➕ 土+遗漏>30+出现≤2：{len(combo3)}个 - {[item['number'] for item in combo3]}")

def test_frontend_integration():
    """测试前端集成"""
    print(f"\n🌐 测试前端集成...")
    
    try:
        response = requests.get("http://localhost:5181/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            print("💡 请在浏览器中测试筛选功能:")
            print("   1. 打开统计页面 → 特码综合分析")
            print("   2. 尝试不同的筛选条件组合")
            print("   3. 测试智能推荐功能")
            print("   4. 验证筛选统计功能")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def generate_filter_test_report(test_data):
    """生成筛选测试报告"""
    print(f"\n📋 筛选功能测试报告")
    print("=" * 60)
    
    total_numbers = len(test_data)
    
    # 统计各类别数量
    stats = {
        '总号码数': total_numbers,
        '红波': len([item for item in test_data if item['color'] == '红波']),
        '蓝波': len([item for item in test_data if item['color'] == '蓝波']),
        '绿波': len([item for item in test_data if item['color'] == '绿波']),
        '单数': len([item for item in test_data if item['is_odd']]),
        '双数': len([item for item in test_data if not item['is_odd']]),
        '大数': len([item for item in test_data if item['is_big']]),
        '小数': len([item for item in test_data if not item['is_big']]),
        '质数': len([item for item in test_data if item['is_prime']]),
        '合数': len([item for item in test_data if not item['is_prime']]),
    }
    
    print("📊 数据分布统计:")
    for key, value in stats.items():
        percentage = (value / total_numbers * 100) if total_numbers > 0 else 0
        print(f"   {key}: {value}个 ({percentage:.1f}%)")
    
    print(f"\n✅ 筛选功能测试完成")
    print(f"   - 基础筛选: 支持波色、生肖、五行、属性等")
    print(f"   - 高级筛选: 支持遗漏期数、出现次数等")
    print(f"   - 智能筛选: 支持热门、回补、稳定、潜力等")
    print(f"   - 组合筛选: 支持多条件同时筛选")

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的筛选功能...")
    print("=" * 60)
    
    # 1. 生成测试数据
    test_data = test_filter_functionality()
    
    # 2. 测试各种筛选场景
    test_filter_scenarios(test_data)
    
    # 3. 测试智能筛选
    test_smart_filters(test_data)
    
    # 4. 测试筛选组合
    test_filter_combinations(test_data)
    
    # 5. 测试前端集成
    frontend_ok = test_frontend_integration()
    
    # 6. 生成测试报告
    generate_filter_test_report(test_data)
    
    print("\n" + "=" * 60)
    print("🎉 筛选功能优化测试完成！")
    
    if frontend_ok:
        print("✅ 前端服务正常，可以在浏览器中体验新的筛选功能")
    else:
        print("⚠️ 前端服务异常，请检查服务状态")
    
    print("\n🔍 新增筛选功能特性:")
    print("   ✅ 三层筛选架构（热度/高级/智能）")
    print("   ✅ 多维度筛选条件（波色/生肖/五行/属性等）")
    print("   ✅ 智能推荐算法（热门/回补/稳定/潜力）")
    print("   ✅ 实时筛选统计和反馈")
    print("   ✅ 筛选方案保存和重置")

if __name__ == "__main__":
    main()
