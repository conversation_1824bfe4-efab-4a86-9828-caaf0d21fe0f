from pydantic import BaseModel, Field, conlist
from typing import List, Dict, Optional
from datetime import datetime


class DrawBase(BaseModel):
    expect: str = Field(..., description="期号")
    numbers: conlist(int, min_length=6, max_length=6)
    draw_time: datetime = Field(..., description="开奖时间")
    special_number: int = Field(..., description="特码")
    zodiac: str = Field(..., description="生肖")
    color: str = Field(..., description="波色")
    odd_even: str = Field(..., description="单双")
    big_small: str = Field(..., description="大小")
    tail_big_small: str = Field(..., description="尾大小")
    sum_odd_even: str = Field(..., description="和单双")
    animal_type: str = Field(..., description="动物类型")
    wuxing: str = Field(..., description="五行")


class DrawCreate(DrawBase):
    pass


class DrawUpdate(DrawBase):
    expect: Optional[str] = None
    numbers: Optional[List[int]] = None
    draw_time: Optional[datetime] = None
    special_number: Optional[int] = None
    zodiac: Optional[str] = None
    color: Optional[str] = None
    odd_even: Optional[str] = None
    big_small: Optional[str] = None
    tail_big_small: Optional[str] = None
    sum_odd_even: Optional[str] = None
    animal_type: Optional[str] = None
    wuxing: Optional[str] = None


class DrawResponse(DrawBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DrawHistoryResponse(BaseModel):
    total: int
    page: int
    page_size: int
    items: List[DrawResponse]


class NumberAttributeBase(BaseModel):
    zodiac: str
    color: str
    odd_even: str
    big_small: str
    tail_big_small: str
    sum_odd_even: str
    animal_type: str
    wuxing: str


class NumberAttributesResponse(NumberAttributeBase):
    number: int
    frequency: int = Field(0, description="出现频率")
    last_appear: Optional[datetime] = Field(None, description="最近出现时间")
    avg_interval: Optional[int] = Field(None, description="平均间隔期数")


class StatisticsItem(BaseModel):
    number: int
    count: int
    percentage: float
    last_appear: Optional[datetime]
    avg_interval: Optional[int]


class ZodiacStatistics(BaseModel):
    zodiac: str
    count: int
    percentage: float
    numbers: List[int]


class DrawStatisticsResponse(BaseModel):
    total_draws: int
    period_range: str
    date_range: str
    hot_numbers: List[StatisticsItem]
    cold_numbers: List[StatisticsItem]
    zodiac_statistics: List[ZodiacStatistics]
    # 移除 number_distribution 字段，避免验证错误
    zodiac_distribution: dict[str, int]
    color_distribution: dict[str, int]
    odd_even_distribution: dict[str, int]
    big_small_distribution: dict[str, int]


class TrendAnalysis(BaseModel):
    period: str
    number: int
    attributes: NumberAttributeBase
    hot_level: int = Field(..., description="热度等级 1-5")
    suggestion_level: int = Field(..., description="推荐等级 1-5")


class DrawAnalysisResponse(BaseModel):
    trend_analysis: List[TrendAnalysis]
    hot_numbers: List[StatisticsItem]
    cold_numbers: List[StatisticsItem]
    number_suggestions: List[int]
    pattern_analysis: dict[str, float]
    confidence_score: float


class DrawList(BaseModel):
    list: List[DrawResponse]
    total: int


class Draw(BaseModel):
    id: int
    expect: str
    numbers: List[int]
    draw_time: datetime

    class Config:
        from_attributes = True


class DrawList(BaseModel):
    data: List[Draw]
    total: int
    page: int
    page_size: int


class BasicStats(BaseModel):
    totalCount: int
    hotNumber: int
    hotNumberCount: int
    coldNumber: int
    coldNumberCount: int
    avgInterval: float


class Statistics(BaseModel):
    basicStats: BasicStats
    numberFrequency: List[int]
    colorFrequency: dict
    tailFrequency: List[int]
    zodiacFrequency: dict
    attributes: dict
    wuxing: dict
    consecutive: dict


# 新增：用于 /api/draw/next 接口的响应模型
class NextDrawInfoResponse(BaseModel):
    expect: str = Field(..., description="下一期期号")
    draw_time: datetime = Field(..., description="预计开奖时间")

    class Config:
        from_attributes = True  # 如果需要从 ORM 对象转换
