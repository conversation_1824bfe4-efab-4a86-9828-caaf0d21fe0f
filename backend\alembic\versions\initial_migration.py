"""initial migration

Revision ID: 001
Revises: 
Create Date: 2024-03-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 创建开奖记录表
    op.create_table(
        'draws',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('expect', sa.String(), nullable=False),
        sa.Column('numbers', sa.JSON(), nullable=True),
        sa.Column('draw_time', sa.DateTime(), nullable=False),
        sa.Column('special_number', sa.Integer(), nullable=True),
        sa.Column('zodiac', sa.String(), nullable=True),
        sa.Column('color', sa.String(), nullable=True),
        sa.Column('odd_even', sa.String(), nullable=True),
        sa.Column('big_small', sa.String(), nullable=True),
        sa.Column('tail_big_small', sa.String(), nullable=True),
        sa.Column('sum_odd_even', sa.String(), nullable=True),
        sa.Column('animal_type', sa.String(), nullable=True),
        sa.Column('wuxing', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(),
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(),
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_draws_expect'), 'draws', ['expect'], unique=True)

    # 创建预测记录表
    op.create_table(
        'predictions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('expect', sa.String(), nullable=False),
        sa.Column('predicted_numbers', sa.JSON(), nullable=True),
        sa.Column('confidence', sa.Float(), nullable=True),
        sa.Column('model_version', sa.String(), nullable=True),
        sa.Column('prediction_time', sa.DateTime(), nullable=True),
        sa.Column('is_correct', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(),
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(),
                  server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_predictions_expect'),
                    'predictions', ['expect'], unique=True)

    # 创建模型训练历史表
    op.create_table(
        'model_training_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('training_time', sa.DateTime(), nullable=True),
        sa.Column('model_name', sa.String(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('metrics', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('data_range', sa.String(), nullable=True),
        sa.Column('data_count', sa.Integer(), nullable=True),
        sa.Column('period_range', sa.JSON(), nullable=True),
        sa.Column('custom_range', sa.JSON(), nullable=True),
        sa.Column('recent_count', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('model_training_history')
    op.drop_table('predictions')
    op.drop_table('draws')
