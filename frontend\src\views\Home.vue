<template>
  <ErrorBoundary>
    <div class="home">
      <!-- 最新开奖结果和下期开奖信息 -->
      <div class="current-draw">
        <div class="draw-section latest-draw">
          <div class="title-with-sync">
            <h2>最新开奖结果</h2>
            <el-button type="primary" size="small" :loading="loading" @click="syncLatestData" icon="Refresh"
              circle></el-button>
          </div>
          <div class="draw-info" v-if="latestDraws.length > 0">
            <div class="period">第{{ latestDraws[0].expect }}期</div>
            <div class="draw-time">开奖时间：{{ formatDate(latestDraws[0]?.draw_time, 'Latest Draw Time') }}</div>
            <div class="numbers">
              <div class="number-container">
                <div v-for="(num, index) in getNumberArray(latestDraws[0].open_code || latestDraws[0].number)"
                  :key="index" class="number-wrapper">
                  <div :class="[
                    'number-ball',
                    'large',
                    index === getNumberArray(latestDraws[0].open_code || latestDraws[0].number).length - 1 ? 'special' : '',
                    getBallColor(parseInt(num))
                  ]">
                    {{ num }}
                  </div>
                  <div class="zodiac">
                    {{ GameRules2025.getZodiac(parseInt(num)) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="draw-section next-draw">
          <h2>下期开奖信息</h2>
          <div class="next-draw-content">
            <div class="next-period">第{{ nextPeriod }}期</div>
            <div class="next-time">开奖时间：{{ nextDrawTime }}</div>
            <div class="countdown">
              <div class="countdown-item">
                <span class="number">{{ countdown.days }}</span>
                <span class="label">天</span>
              </div>
              <div class="countdown-item">
                <span class="number">{{ countdown.hours }}</span>
                <span class="label">时</span>
              </div>
              <div class="countdown-item">
                <span class="number">{{ countdown.minutes }}</span>
                <span class="label">分</span>
              </div>
              <div class="countdown-item">
                <span class="number">{{ countdown.seconds }}</span>
                <span class="label">秒</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近5期开奖记录 -->
      <div class="history-draws">
        <h2>最近5期开奖记录</h2>
        <div class="results-table">
          <el-table :data="latestDraws" style="width: 100%" :row-key="row => row.expect" v-loading="loading"
            element-loading-text="加载中..." stripe border>
            <el-table-column prop="expect" label="期号" width="120">
              <template #default="{ row }">
                <span>第{{ row.expect }}期</span>
              </template>
            </el-table-column>

            <el-table-column prop="draw_time" label="开奖时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.draw_time, `History Draw Time ${row.expect}`) }}
              </template>
            </el-table-column>

            <el-table-column label="开奖号码及属性">
              <template #default="{ row }">
                <div class="numbers-and-attributes">
                  <div class="number-container">
                    <div v-for="(num, index) in getNumberArray(row.open_code || row.number)" :key="index"
                      class="number-wrapper">
                      <div :class="[
                        'number-ball',
                        index === getNumberArray(row.open_code || row.number).length - 1 ? 'special' : '',
                        getBallColor(parseInt(num))
                      ]">
                        {{ num }}
                      </div>
                      <div class="zodiac">
                        {{ GameRules2025.getZodiac(parseInt(num)) }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="特码属性" width="180">
              <template #default="{ row }">
                <div class="special-attributes-column">
                  <el-space direction="vertical" alignment="start" :size="5">
                    <el-tag size="small" :type="getColorType(parseInt(getSpecialNumber(row.open_code || row.number)))">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).color }}
                    </el-tag>
                    <el-tag size="small">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).zodiac }}
                    </el-tag>
                    <el-tag size="small" type="warning">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).oddEven }}
                    </el-tag>
                    <el-tag size="small" type="info">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).bigSmall }}
                    </el-tag>
                    <el-tag size="small">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).tailBigSmall }}
                    </el-tag>
                    <el-tag size="small">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).sumOddEven }}
                    </el-tag>
                    <el-tag size="small" type="success">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).animalType }}
                    </el-tag>
                    <el-tag size="small" type="danger">
                      {{ getNumberAttributes(parseInt(getSpecialNumber(row.open_code || row.number))).wuxing }}
                    </el-tag>
                  </el-space>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </ErrorBoundary>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import request from '@/utils/request'
import { format } from 'date-fns'
import GameRules2025 from '@/utils/GameRules2025'
import { ElMessage } from 'element-plus'
import ErrorHandler from '@/utils/error-handler'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

export default {
  name: 'Home',
  components: {
    ErrorBoundary
  },
  setup() {
    const latestDraws = ref([])
    const nextPeriod = ref('')
    const nextDrawTime = ref('')
    const loading = ref(false)
    const countdown = ref({
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00'
    })
    // 防抖控制变量 - 使用模块级变量保证在热更新时也能保持状态
    const FETCH_COOLDOWN = 60000 // 60秒冷却时间
    const CACHE_EXPIRY = 60000 // 缓存过期时间，60秒
    let fetchInProgress = false
    let lastFetchTime = 0
    let fetchTimer = null
    let countdownTimer = null

    // 从本地存储中获取缓存数据
    const getCache = (key) => {
      try {
        const cacheData = localStorage.getItem(key)
        if (!cacheData) return null

        const { data, timestamp } = JSON.parse(cacheData)
        const now = Date.now()

        // 检查缓存是否过期
        if (now - timestamp > CACHE_EXPIRY) {
          localStorage.removeItem(key)
          return null
        }

        return data
      } catch (error) {
        console.error('读取缓存错误:', error)
        return null
      }
    }

    // 将数据存入本地存储
    const setCache = (key, data) => {
      try {
        const cacheData = {
          data,
          timestamp: Date.now()
        }
        localStorage.setItem(key, JSON.stringify(cacheData))
      } catch (error) {
        console.error('写入缓存错误:', error)
      }
    }

    // 取消所有定时器
    const clearAllTimers = () => {
      if (fetchTimer) {
        clearTimeout(fetchTimer)
        fetchTimer = null
      }
      if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }

    const fetchData = async (force = false) => {
      // 如果不是强制刷新，尝试从缓存加载数据
      if (!force) {
        // 尝试从缓存加载数据
        const cachedLatestDraws = getCache('latestDraws')
        const cachedNextDraw = getCache('nextDraw')

        if (cachedLatestDraws && cachedNextDraw) {
          console.log('从缓存加载数据')
          latestDraws.value = cachedLatestDraws

          // 设置下一期数据
          nextDrawTime.value = new Date(cachedNextDraw.draw_time)
          nextExpect.value = cachedNextDraw.expect

          // 更新倒计时
          updateCountdown(nextDrawTime.value)
          return
        }
      }

      // 如果已经在获取数据，或者距离上次获取时间不足冷却时间且不是强制刷新，则跳过
      const now = Date.now()
      if ((!force && fetchInProgress) || (!force && now - lastFetchTime < FETCH_COOLDOWN)) {
        console.log('跳过数据获取：', fetchInProgress ? '正在进行中' : '冷却时间内')
        return
      }

      // 清除所有定时器
      clearAllTimers()

      // 设置加载状态和获取标志
      loading.value = true
      fetchInProgress = true
      lastFetchTime = now

      try {
        // 尝试从后端获取数据
        let latestRes, nextRes;
        let success = false;

        // 只使用正确的API端点
        let apiEndpoints = [
          { latest: '/api/draw/latest?limit=5&force_update=true', next: '/api/draw/next?force_update=true' },
          { latest: '/api/draw/latest?limit=5', next: '/api/draw/next' }
        ];

        for (const endpoints of apiEndpoints) {
          try {
            console.log(`尝试访问端点: ${endpoints.latest} 和 ${endpoints.next}`);
            [latestRes, nextRes] = await Promise.all([
              request.get(endpoints.latest),
              request.get(endpoints.next)
            ]);

            if (latestRes && latestRes.code === 200 && latestRes.data) {
              console.log(`端点 ${endpoints.latest} 返回数据成功`);
              success = true;
              break; // 如果成功，跳出循环
            }
          } catch (endpointError) {
            console.error(`端点 ${endpoints.latest} 访问失败:`, endpointError);
            // 继续尝试下一个端点
          }
        }

        if (!success) {
          console.error('所有API端点都访问失败');
          // 如果所有端点都失败，显示错误消息
          ElMessage.error('无法连接到后端服务，请检查网络连接或联系管理员');
          loading.value = false;
          fetchInProgress = false;
          return; // 提前返回，不继续执行
        }

        // 处理最新开奖数据
        if (latestRes && latestRes.code === 200) {
          let drawData = latestRes.data;

          if (Array.isArray(drawData)) {
            latestDraws.value = drawData;
            // 将数据存入缓存
            setCache('latestDraws', drawData);
          } else if (drawData && typeof drawData === 'object') {
            latestDraws.value = [drawData];
            // 将数据存入缓存
            setCache('latestDraws', [drawData]);
          } else {
            console.error('无法解析开奖数据：', drawData);
            // 如果没有数据，使用缓存中的数据
            const cachedData = getCache('latestDraws');
            if (cachedData) {
              latestDraws.value = cachedData;
            } else {
              latestDraws.value = [];
              ElMessage.warning('开奖数据格式不正确，请稍后再试');
            }
          }
        } else {
          console.error('获取最新开奖数据失败：', latestRes);
          // 如果没有数据，使用缓存中的数据
          const cachedData = getCache('latestDraws');
          if (cachedData) {
            latestDraws.value = cachedData;
          } else {
            ElMessage.error('获取开奖数据失败，请稍后再试');
          }
        }

        // 处理下一期开奖信息
        let nextData;
        if (nextRes && nextRes.code === 200 && nextRes.data) {
          // 因为nextRes可能是直接的数据对象，也可能包含在data属性中
          nextData = nextRes.data;

          // 检查对象中是否有expect或period属性
          if (nextData && (nextData.expect || nextData.period)) {
            nextPeriod.value = nextData.expect || nextData.period;

            // 检查对象中是否有draw_time或drawTime属性
            const drawTimeStr = nextData.draw_time || nextData.drawTime;

            if (drawTimeStr) {
              try {
                const drawTime = new Date(drawTimeStr);
                // 确认日期有效
                if (!isNaN(drawTime.getTime())) {
                  nextDrawTime.value = formatDate(drawTime);
                  // 将下一期数据存入缓存
                  setCache('nextDraw', nextData);
                  updateCountdown(drawTime);
                } else {
                  // 使用计算的下一期数据
                  nextData = calculateNextDrawTime();
                  nextPeriod.value = nextData.expect;
                  const calculatedDrawTime = new Date(nextData.draw_time);
                  nextDrawTime.value = formatDate(calculatedDrawTime);
                  setCache('nextDraw', nextData);
                  updateCountdown(calculatedDrawTime);
                }
              } catch (timeError) {
                // 使用计算的下一期数据
                nextData = calculateNextDrawTime();
                nextPeriod.value = nextData.expect;
                const calculatedDrawTime = new Date(nextData.draw_time);
                nextDrawTime.value = formatDate(calculatedDrawTime);
                setCache('nextDraw', nextData);
                updateCountdown(calculatedDrawTime);
              }
            } else {
              // 使用计算的下一期数据
              nextData = calculateNextDrawTime();
              nextPeriod.value = nextData.expect;
              const calculatedDrawTime = new Date(nextData.draw_time);
              nextDrawTime.value = formatDate(calculatedDrawTime);
              setCache('nextDraw', nextData);
              updateCountdown(calculatedDrawTime);
            }
          } else {
            // 使用计算的下一期数据
            nextData = calculateNextDrawTime();
            nextPeriod.value = nextData.expect;
            const calculatedDrawTime = new Date(nextData.draw_time);
            nextDrawTime.value = formatDate(calculatedDrawTime);
            setCache('nextDraw', nextData);
            updateCountdown(calculatedDrawTime);
          }
        } else {
          // 使用计算的下一期数据
          nextData = calculateNextDrawTime();
          nextPeriod.value = nextData.expect;
          const calculatedDrawTime = new Date(nextData.draw_time);
          nextDrawTime.value = formatDate(calculatedDrawTime);
          setCache('nextDraw', nextData);
          updateCountdown(calculatedDrawTime);
        }
      } catch (error) {
        ErrorHandler.handleApiError(error, '获取开奖数据');

        // 如果出错，使用计算的下一期数据
        const nextData = calculateNextDrawTime();
        nextPeriod.value = nextData.expect;
        const calculatedDrawTime = new Date(nextData.draw_time);
        nextDrawTime.value = formatDate(calculatedDrawTime);
        setCache('nextDraw', nextData);
        updateCountdown(calculatedDrawTime);
      } finally {
        // 关闭加载状态和获取标志
        loading.value = false;
        fetchInProgress = false;
      }
    }

    const updateCountdown = (drawTime) => {
      // 清除现有的倒计时定时器
      if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }

      const update = () => {
        const now = new Date().getTime()
        const target = drawTime.getTime()
        const diff = target - now

        if (diff <= 0) {
          console.log('Countdown finished, scheduling refresh...');
          // 清除倒计时定时器
          clearInterval(countdownTimer)
          countdownTimer = null

          // 计划延迟刷新，避免循环过快
          if (!fetchTimer) {
            fetchTimer = setTimeout(() => {
              console.log('Executing scheduled refresh');
              fetchTimer = null
              fetchData(true) // 强制刷新，忽略冷却时间
            }, 10000); // 延迟10秒，给更多缓冲时间
          }
          return
        }

        const days = Math.floor(diff / (1000 * 60 * 60 * 24))
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((diff % (1000 * 60)) / 1000)

        countdown.value = {
          days: String(days).padStart(2, '0'),
          hours: String(hours).padStart(2, '0'),
          minutes: String(minutes).padStart(2, '0'),
          seconds: String(seconds).padStart(2, '0')
        }
      }

      // 立即更新一次
      update()

      // 设置新的倒计时定时器
      countdownTimer = setInterval(update, 1000)
    }

    const formatDate = (date, context = 'Unknown') => { // 添加上下文参数以便调试
      try {
        // 更严格地检查无效值 (null, undefined, empty string)
        if (date === null || date === undefined || date === '') {
          // 可以选择性地减少日志噪音，例如只在开发模式下打印或限制频率
          // console.warn(`formatDate called with empty value in context [${context}]:`, date);
          return '---'; // 返回更清晰的占位符
        }

        const dateObj = new Date(date);
        // 检查日期对象是否有效
        if (isNaN(dateObj.getTime())) {
          console.warn(`Invalid date value passed to formatDate in context [${context}]:`, date);
          return '日期无效'; // 更具体的错误信息
        }

        // 格式化日期
        return format(dateObj, 'yyyy-MM-dd HH:mm:ss');
      } catch (error) {
        console.error(`Error formatting date in context [${context}]:`, error, 'Input value:', date);
        return '格式化错误'; // 更具体的错误信息
      }
    }

    const getBallColor = (number) => {
      if (GameRules2025.RED_NUMBERS.includes(number)) {
        return 'red'
      } else if (GameRules2025.BLUE_NUMBERS.includes(number)) {
        return 'blue'
      } else if (GameRules2025.GREEN_NUMBERS.includes(number)) {
        return 'green'
      }
      return ''
    }

    const getWuxing = (number) => {
      const attrs = GameRules2025.getNumberAttributes(number)
      return attrs.wuxing
    }

    const getNumberAttributes = (number) => {
      return GameRules2025.getNumberAttributes(number)
    }

    const getColorType = (number) => {
      const color = GameRules2025.getColor(number)
      switch (color) {
        case '红波':
          return 'danger'
        case '蓝波':
          return 'primary'
        case '绿波':
          return 'success'
        default:
          return 'info'  // 修改默认返回值为'info'而不是空字符串
      }
    }

    // 将号码转换为数组
    const getNumberArray = (number) => {
      if (!number) return []

      // 如果是字符串并且包含逗号，处理分割
      if (typeof number === 'string' && number.includes(',')) {
        // 处理包含+号的格式，如 "48,06,23,33,43,47+47"
        let numberStr = number
        if (number.includes('+')) {
          // 将+号替换为逗号，确保所有号码都被包含
          numberStr = number.replace('+', ',')
        }
        return numberStr.split(',').map(num => num.trim()).filter(num => num !== '')
      }

      // 如果是数字，转换为字符串并返回单元素数组
      return [String(number)]
    }

    // 获取特殊号码（最后一个号码）
    const getSpecialNumber = (number) => {
      const numArray = getNumberArray(number)
      if (numArray.length === 0) return null
      return numArray[numArray.length - 1]
    }

    onMounted(() => {
      // 首次加载数据
      fetchData(true)
    })

    onUnmounted(() => {
      // 清除所有定时器
      clearAllTimers()
    })

    // 同步最新数据
    const syncLatestData = async () => {
      loading.value = true;
      try {
        // 尝试从API获取最新数据
        console.log('开始从API获取数据...');

        // 首先尝试强制从API获取最新数据
        try {
          console.log('尝试强制从API获取最新开奖结果...');
          const forceUpdateResponse = await request.get('/api/draw/latest?limit=1&force_update=true');
          if (forceUpdateResponse && forceUpdateResponse.code === 200) {
            console.log('成功强制从API获取最新开奖结果');
          }
        } catch (forceUpdateError) {
          console.error('强制从API获取最新开奖结果失败:', forceUpdateError);
        }

        // 尝试不同的API端点获取最新数据
        let response;
        let apiEndpoints = [
          '/api/draw/latest?limit=5&force_update=true',
          '/draw/latest?limit=5&force_update=true',
          '/api/draw/latest?limit=5',
          '/draw/latest?limit=5',
          '/lottery/latest?limit=5',
          '/masksix/latest?limit=5'
        ];

        // 尝试每个端点
        for (const endpoint of apiEndpoints) {
          try {
            console.log(`尝试访问端点: ${endpoint}`);
            response = await request.get(endpoint);
            if (response && response.code === 200 && response.data) {
              console.log(`端点 ${endpoint} 返回数据成功`);
              break; // 如果成功，跳出循环
            }
          } catch (endpointError) {
            console.error(`端点 ${endpoint} 访问失败:`, endpointError);
            // 继续尝试下一个端点
          }
        }

        if (response && response.code === 200) {
          let drawData = response.data;
          console.log('获取到的数据:', drawData);

          if (Array.isArray(drawData) && drawData.length > 0) {
            latestDraws.value = drawData;
            setCache('latestDraws', drawData);
            ElMessage.success('数据同步成功');

            // 同时获取下一期数据
            try {
              const nextResponse = await request.get('/api/draw/next?force_update=true');
              if (nextResponse && nextResponse.code === 200 && nextResponse.data) {
                const nextData = nextResponse.data;
                nextPeriod.value = nextData.expect || nextData.period;
                const drawTime = new Date(nextData.draw_time || nextData.drawTime);
                nextDrawTime.value = formatDate(drawTime);
                setCache('nextDraw', nextData);
                updateCountdown(drawTime);
              }
            } catch (nextError) {
              console.error('获取下一期数据失败:', nextError);
              // 如果获取下一期数据失败，使用计算的下一期数据
              const nextData = calculateNextDrawTime();
              nextPeriod.value = nextData.expect;
              const calculatedDrawTime = new Date(nextData.draw_time);
              nextDrawTime.value = formatDate(calculatedDrawTime);
              setCache('nextDraw', nextData);
              updateCountdown(calculatedDrawTime);
            }
          } else if (drawData && typeof drawData === 'object') {
            latestDraws.value = [drawData];
            setCache('latestDraws', [drawData]);
            ElMessage.success('数据同步成功');
          } else {
            console.warn('后端返回的数据格式不正确:', drawData);
            ElMessage.error('后端返回的数据格式不正确，请联系管理员');
          }
        } else {
          console.error('所有API端点都访问失败，或者返回了错误状态码');
          ElMessage.error('无法连接到后端服务，请检查网络连接或联系管理员');
        }
      } catch (error) {
        console.error('同步数据失败:', error);
        ElMessage.error('同步数据失败，请稍后再试');
      } finally {
        loading.value = false;
      }
    }

    // 生成并使用模拟数据
    const generateAndUseMockData = () => {
      // 生成模拟数据
      const mockData = [];

      // 根据真实开奖结果生成模拟数据
      const realData = [
        {
          expect: "2025112",
          draw_time: "2025-04-22T21:32:32",
          open_code: "15,43,38,24,18,04,45",
          special_number: 45,
          zodiac: "鸡"
        },
        {
          expect: "2025111",
          draw_time: "2025-04-21T21:32:32",
          open_code: "02,17,13,41,28,36,45",
          special_number: 45,
          zodiac: "鸡"
        },
        {
          expect: "2025110",
          draw_time: "2025-04-20T21:32:32",
          open_code: "15,20,14,46,31,30,47",
          special_number: 47,
          zodiac: "羊"
        },
        {
          expect: "2025109",
          draw_time: "2025-04-19T21:32:32",
          open_code: "47,16,08,41,06,19,03",
          special_number: 3,
          zodiac: "兔"
        },
        {
          expect: "2025108",
          draw_time: "2025-04-18T21:32:32",
          open_code: "40,20,39,49,08,01,27",
          special_number: 27,
          zodiac: "兔"
        }
      ];

      // 处理每一条数据
      for (const item of realData) {
        // 将开奖号码字符串转换为数组
        const numbers = item.open_code.split(',').map(num => parseInt(num));

        // 特码是最后一个号码
        const specialNumber = numbers[numbers.length - 1];

        // 获取特码的属性
        const attributes = getNumberAttributes(specialNumber);

        // 添加到模拟数据中
        mockData.push({
          expect: item.expect,
          number: specialNumber,
          draw_time: item.draw_time,
          open_code: item.open_code,
          special_number: specialNumber,
          zodiac: attributes.zodiac,
          color: attributes.color,
          odd_even: attributes.oddEven,
          big_small: attributes.bigSmall,
          tail_big_small: attributes.tailBigSmall,
          sum_odd_even: attributes.sumOddEven,
          animal_type: attributes.animalType,
          wuxing: attributes.wuxing
        });
      }

      // 使用模拟数据
      latestDraws.value = mockData;
      setCache('latestDraws', mockData);

      // 设置下一期数据
      const nextData = calculateNextDrawTime();
      nextPeriod.value = nextData.expect;
      const drawTime = new Date(nextData.draw_time);
      nextDrawTime.value = formatDate(drawTime);
      setCache('nextDraw', nextData);
      updateCountdown(drawTime);

      // 显示成功消息
      ElMessage.success('数据已更新到最新');
    }

    // 计算下一期开奖时间
    const calculateNextDrawTime = () => {
      // 获取当前日期
      const now = new Date();

      // 设置下一期开奖时间（每天晚上9点半）
      const nextDraw = new Date(now);
      nextDraw.setHours(21, 32, 32, 0);

      // 如果当前时间已过今天的开奖时间，则设置为明天
      if (now > nextDraw) {
        nextDraw.setDate(nextDraw.getDate() + 1);
      }

      // 获取最新的开奖期号
      let nextPeriod = "2025112";
      if (latestDraws.value && latestDraws.value.length > 0) {
        const latestPeriod = latestDraws.value[0].expect;
        if (latestPeriod && !isNaN(parseInt(latestPeriod))) {
          nextPeriod = (parseInt(latestPeriod) + 1).toString();
        }
      } else {
        // 如果没有最新开奖数据，使用默认的下一期期号
        nextPeriod = "2025113";
      }

      return {
        expect: nextPeriod,
        draw_time: nextDraw.toISOString()
      };
    }

    return {
      latestDraws,
      nextPeriod,
      nextDrawTime,
      countdown,
      loading,
      formatDate,
      getBallColor,
      getWuxing,
      getNumberAttributes,
      getColorType,
      getNumberArray,
      getSpecialNumber,
      GameRules2025,
      syncLatestData
    }
  }
}
</script>

<style scoped>
.home {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
}

.current-draw {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.draw-section {
  flex: 1;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.latest-draw {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.next-draw {
  background: linear-gradient(135deg, #fff 0%, #f0f2f5 100%);
}

.history-draws {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5em;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f2f5;
}

.title-with-sync {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-with-sync h2 {
  margin-bottom: 0;
}

.draw-info,
.next-draw-content {
  text-align: center;
}

.period,
.next-period {
  font-size: 1.3em;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
}

.draw-time,
.next-time {
  color: #666;
  margin-bottom: 20px;
}

.numbers {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
}

.number-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.number-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.number-ball {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 1.1em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.number-ball.large {
  width: 60px;
  height: 60px;
  font-size: 1.4em;
}

.number-ball:hover {
  transform: scale(1.1);
}

.number-ball.red {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
}

.number-ball.blue {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.number-ball.green {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

.number-ball.special {
  border: 2px solid gold;
  animation: pulse 2s infinite;
  transform: scale(1.05);
}

.zodiac {
  font-size: 0.85em;
  color: #666;
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 4px;
  text-align: center;
  min-width: 32px;
  margin-top: 4px;
}

.countdown {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.countdown-item {
  background: #fff;
  border-radius: 8px;
  padding: 12px 20px;
  min-width: 70px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.countdown-item:hover {
  transform: translateY(-2px);
}

.countdown .number {
  display: block;
  font-size: 1.8em;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
}

.countdown .label {
  display: block;
  text-align: center;
  color: #666;
  margin-top: 5px;
  font-size: 0.9em;
}

.results-table {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th,
td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
}

th {
  background: #f8f9fa;
  font-weight: bold;
  color: #2c3e50;
  position: sticky;
  top: 0;
}

.attributes {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  padding-left: 20px;
}

.attribute {
  padding: 4px 8px;
  border-radius: 4px;
  background: #f5f7fa;
  font-size: 0.9em;
  white-space: nowrap;
}

.attribute.color {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: bold;
}

td.numbers-and-attributes {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  padding: 10px 15px;
}

.special-attributes {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 0;
}

.special-attributes .el-tag {
  margin: 2px;
  font-size: 0.9em;
}

.special-attributes-column {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.special-attributes-column .el-tag {
  margin: 2px 0;
  min-width: 60px;
  text-align: center;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
  }
}

@media (max-width: 768px) {
  .home {
    padding: 10px;
  }

  .current-draw {
    flex-direction: column;
    gap: 10px;
  }

  .countdown {
    flex-wrap: wrap;
    gap: 10px;
  }

  .countdown-item {
    flex: 1 1 calc(50% - 10px);
    min-width: auto;
  }

  .number-ball {
    width: 36px;
    height: 36px;
    font-size: 0.9em;
  }

  .number-ball.large {
    width: 45px;
    height: 45px;
    font-size: 1.1em;
  }

  .zodiac {
    font-size: 0.7em;
    padding: 1px 4px;
  }

  .attributes {
    gap: 4px;
  }

  .attribute {
    padding: 3px 6px;
    font-size: 0.75em;
  }

  /* 表格适配 */
  .el-table {
    font-size: 0.85em;
  }

  .el-table .el-tag {
    font-size: 0.75em;
    padding: 0 4px;
    height: 20px;
  }

  .numbers-and-attributes {
    flex-direction: column;
    align-items: flex-start;
  }

  .number-container {
    margin-bottom: 8px;
  }

  .special-attributes-column {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .special-attributes-column .el-tag {
    margin: 2px;
    min-width: auto;
  }
}
</style>