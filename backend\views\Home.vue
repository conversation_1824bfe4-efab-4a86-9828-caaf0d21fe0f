<template>
  <div class="home">
    <el-container>
      <el-header>
        <h1>智能数字竞猜预测系统</h1>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <!-- 最新开奖信息 -->
          <el-col :span="12">
            <el-card class="latest-draw">
              <template #header>
                <div class="card-header">
                  <span>最新开奖</span>
                </div>
              </template>
              <div class="numbers" v-if="latestDraw">
                <span class="number" v-for="num in latestDraw.numbers" :key="num">
                  {{ num }}
                </span>
              </div>
            </el-card>
          </el-col>
          
          <!-- 快速预测 -->
          <el-col :span="12">
            <el-card class="quick-predict">
              <template #header>
                <div class="card-header">
                  <span>快速预测</span>
                  <el-button type="primary" @click="generatePrediction">
                    生成预测
                  </el-button>
                </div>
              </template>
              <!-- 预测结果将在这里显示 -->
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { predictionApi } from '../api/prediction'

const latestDraw = ref(null)

onMounted(async () => {
  try {
    const response = await predictionApi.getHistory({ limit: 1 })
    latestDraw.value = response.data[0]
  } catch (error) {
    console.error('获取最新开奖数据失败:', error)
  }
})
</script>

<style scoped>
.home {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.numbers {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}
</style> 