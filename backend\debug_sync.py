#!/usr/bin/env python3
"""
调试数据同步问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
from app.database import SessionLocal
from app.utils.data_manager import get_data_manager

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_sync():
    """调试同步功能"""
    print("=== 开始调试数据同步功能 ===")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 获取数据管理器
        data_manager = get_data_manager(db)
        print("✅ 成功创建数据管理器")
        
        # 尝试同步最新数据
        print("🔄 开始同步最新开奖数据...")
        latest_draw = data_manager.sync_latest_draw(force_update=True)
        
        if latest_draw:
            print("✅ 同步成功!")
            print(f"期号: {latest_draw.expect}")
            print(f"开奖时间: {latest_draw.draw_time}")
            print(f"开奖号码: {latest_draw.open_code}")
            print(f"特码: {latest_draw.special_number}")
        else:
            print("❌ 同步失败!")
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    finally:
        db.close()

if __name__ == "__main__":
    debug_sync()
