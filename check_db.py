import sqlite3
import os

# 检查主数据库
db_path = 'backend/marksix.db'
if os.path.exists(db_path):
    print(f'找到数据库文件: {db_path}')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f'数据库表: {[table[0] for table in tables]}')
        
        # 检查draws表的数据
        if any('draws' in table[0].lower() for table in tables):
            cursor.execute("SELECT COUNT(*) FROM draws")
            count = cursor.fetchone()[0]
            print(f'draws表总记录数: {count}')
            
            # 查看最新的几条记录
            cursor.execute("SELECT expect, draw_time FROM draws ORDER BY draw_time DESC LIMIT 5")
            recent = cursor.fetchall()
            print(f'最新5条记录: {recent}')
            
            # 按年份统计
            cursor.execute("SELECT substr(expect, 1, 4) as year, COUNT(*) FROM draws GROUP BY substr(expect, 1, 4) ORDER BY year DESC")
            year_stats = cursor.fetchall()
            print(f'按年份统计: {year_stats}')
        
        conn.close()
    except Exception as e:
        print(f'访问数据库失败: {e}')
else:
    print('未找到主数据库文件')
