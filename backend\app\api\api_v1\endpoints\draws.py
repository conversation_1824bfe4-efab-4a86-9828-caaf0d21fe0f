from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.session import get_db
from app.models.draw import Draw
from app.schemas.draw import DrawCreate, DrawResponse

router = APIRouter()


@router.get("/", response_model=List[DrawResponse])
def get_draws(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取开奖记录列表"""
    draws = db.query(Draw).offset(skip).limit(limit).all()
    return draws


@router.get("/latest", response_model=DrawResponse)
def get_latest_draw(db: Session = Depends(get_db)):
    """获取最新一期开奖记录"""
    draw = db.query(Draw).order_by(Draw.draw_time.desc()).first()
    if not draw:
        raise HTTPException(status_code=404, detail="No draws found")
    return draw


@router.get("/{draw_id}", response_model=DrawResponse)
def get_draw(draw_id: int, db: Session = Depends(get_db)):
    """获取指定期号的开奖记录"""
    draw = db.query(Draw).filter(Draw.id == draw_id).first()
    if not draw:
        raise HTTPException(status_code=404, detail="Draw not found")
    return draw
