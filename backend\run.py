import uvicorn
import argparse
import logging
from app.main import app  # 使用绝对导入

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 修改为DEBUG级别以显示更多日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='启动后端服务')
    parser.add_argument('--port', type=int, default=8000, help='服务端口号')  # 修改为8001端口
    args = parser.parse_args()

    try:
        # 初始化测试数据
        from init_test_data import init_test_data
        init_test_data()
        logger.info("Test data initialized successfully")

        # 初始化预测数据
        from init_prediction_data import init_prediction_data
        init_prediction_data()
        logger.info("Prediction data initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing test data: {str(e)}")
        logger.info("Continuing with application startup...")

    # 启动应用
    logger.info(f"Starting FastAPI application on port {args.port}")
    uvicorn.run(app, host="0.0.0.0", port=args.port)
