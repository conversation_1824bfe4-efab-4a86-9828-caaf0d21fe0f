"""
高级预测API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
import json

from ..database import get_db
from ..models.draw import Draw
from ..models.prediction import Prediction
from ..schemas.prediction import (
    PredictionRequest,
    PredictionResponse,
    TrainingRequest,
    TrainingResponse
)
from ..ml.advanced_prediction_service import AdvancedPredictionService
from ..ml.advanced_prediction_framework import AdvancedPredictionFramework

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(prefix="/api/advanced-prediction", tags=["advanced_prediction"])

# 创建预测服务实例
prediction_service = AdvancedPredictionService()

# 创建高级预测框架实例
advanced_framework = AdvancedPredictionFramework()


@router.post("/predict", response_model=PredictionResponse)
async def predict(
    request: PredictionRequest = Body(...),
    db: Session = Depends(get_db)
):
    """生成高级预测"""
    try:
        logger.info(f"接收到高级预测请求: {request}")

        # 获取历史数据
        historical_data = []
        if request.use_historical_data:
            # 从数据库获取历史数据
            draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(100).all()
            if draws:
                historical_data = [draw.special_number for draw in draws if draw.special_number]
                historical_data.reverse()  # 按时间顺序排列
                logger.info(f"从数据库获取到 {len(historical_data)} 条历史数据")

        # 如果提供了自定义数据，使用自定义数据
        if request.custom_data and len(request.custom_data) > 0:
            logger.info(f"使用用户提供的 {len(request.custom_data)} 条自定义数据")
            historical_data = request.custom_data + historical_data

        # 生成预测
        prediction_result = prediction_service.predict(
            historical_data=historical_data,
            range_size=request.range_size or 30
        )

        # 保存预测结果到数据库
        try:
            # 创建预测记录
            # 创建预测记录 - 移除不存在的字段
            prediction = Prediction(
                expect=request.expect or f"{datetime.now().strftime('%Y%m%d%H%M%S')}",  # 使用用户提供的期号或当前时间
                prediction_time=datetime.now(),
                special_numbers_5=prediction_result.get("special_numbers_5", []),
                special_numbers_10=prediction_result.get("special_numbers_10", []),
                special_numbers_15=prediction_result.get("special_numbers_15", []),
                special_numbers_20=prediction_result.get("special_numbers_20", []),
                special_numbers_30=prediction_result.get("special_numbers_30", []),
                attribute_predictions=prediction_result.get("attribute_predictions", {}),
                zodiac_3=prediction_result.get("zodiac_3", []),
                zodiac_5=prediction_result.get("zodiac_5", []),
                zodiac_7=prediction_result.get("zodiac_7", []),
                confidence_scores=prediction_result.get("confidence_scores", {}),
                strategy=prediction_result.get("strategy", "")
                # 移除不存在的字段: model_name, parameters, status
            )

            # 记录预测参数到日志
            logger.info(f"预测参数: use_historical_data={request.use_historical_data}, "
                       f"range_size={request.range_size}, "
                       f"custom_data_length={len(request.custom_data) if request.custom_data else 0}")
            db.add(prediction)
            db.commit()
            logger.info("预测结果已保存到数据库")
        except Exception as e:
            logger.error(f"保存预测结果失败: {str(e)}")
            db.rollback()

        # 将期号添加到预测结果中
        prediction_result["expect"] = request.expect or f"{datetime.now().strftime('%Y%m%d%H%M%S')}"

        return {
            "status": "success",
            "prediction": prediction_result,
            "message": "高级预测生成成功"
        }
    except Exception as e:
        logger.error(f"生成高级预测失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成高级预测失败: {str(e)}")


@router.post("/train", response_model=TrainingResponse)
async def train_model(
    request: TrainingRequest = Body(...),
    db: Session = Depends(get_db)
):
    """训练高级预测模型"""
    try:
        logger.info(f"接收到模型训练请求: {request}")

        # 获取历史数据
        historical_data = []
        if request.use_historical_data:
            # 从数据库获取历史数据
            draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(200).all()
            if draws:
                historical_data = [draw.special_number for draw in draws if draw.special_number]
                historical_data.reverse()  # 按时间顺序排列
                logger.info(f"从数据库获取到 {len(historical_data)} 条历史数据")

        # 如果提供了自定义数据，使用自定义数据
        if request.custom_data and len(request.custom_data) > 0:
            logger.info(f"使用用户提供的 {len(request.custom_data)} 条自定义数据")
            historical_data = request.custom_data + historical_data

        # 训练模型
        training_result = prediction_service.train_models(
            historical_data=historical_data,
            params=request.parameters or {}
        )

        return {
            "status": "success",
            "training_result": training_result,
            "message": "模型训练成功"
        }
    except Exception as e:
        logger.error(f"训练模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"训练模型失败: {str(e)}")


@router.get("/status")
async def get_model_status():
    """获取模型状态"""
    try:
        # 检查模型是否已加载
        models_loaded = any(prediction_service.models.values())

        return {
            "status": "success",
            "models_loaded": models_loaded,
            "model_weights": prediction_service.model_weights,
            "device": str(prediction_service.device)
        }
    except Exception as e:
        logger.error(f"获取模型状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")
