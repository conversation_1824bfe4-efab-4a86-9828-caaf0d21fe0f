from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from ..database import get_db
from ..models.draw import Draw
from ..models.prediction import Prediction, ModelTrainingHistory, BacktestResult
from ..ml.enhanced_prediction_service import EnhancedPredictionService
from ..utils.enhanced_statistics import EnhancedStatistics

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v2",
    tags=["enhanced_prediction"],
    responses={404: {"description": "Not found"}},
)

# 创建服务实例
prediction_service = EnhancedPredictionService()
statistics_service = EnhancedStatistics()


@router.get("/prediction/latest")
async def get_latest_prediction(db: Session = Depends(get_db)):
    """获取最新预测结果"""
    try:
        # 获取最新的预测记录
        latest_prediction = db.query(Prediction).order_by(Prediction.prediction_time.desc()).first()
        
        if not latest_prediction:
            # 如果没有预测记录，生成新的预测
            return await generate_prediction(db=db)
        
        # 检查预测是否过期（超过24小时）
        if datetime.now() - latest_prediction.prediction_time > timedelta(hours=24):
            return await generate_prediction(db=db)
        
        return {
            "status": "success",
            "data": {
                "id": latest_prediction.id,
                "expect": latest_prediction.expect,
                "prediction_time": latest_prediction.prediction_time,
                "special_numbers_5": latest_prediction.special_numbers_5,
                "special_numbers_10": latest_prediction.special_numbers_10,
                "special_numbers_15": latest_prediction.special_numbers_15,
                "special_numbers_20": latest_prediction.special_numbers_20,
                "special_numbers_30": latest_prediction.special_numbers_30,
                "attribute_predictions": latest_prediction.attribute_predictions,
                "zodiac_3": latest_prediction.zodiac_3,
                "zodiac_5": latest_prediction.zodiac_5,
                "zodiac_7": latest_prediction.zodiac_7,
                "strategy": latest_prediction.strategy,
                "confidence_scores": latest_prediction.confidence_scores,
                "actual_result": latest_prediction.actual_result,
                "accuracy": latest_prediction.accuracy
            }
        }
    
    except Exception as e:
        logger.error(f"Error getting latest prediction: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取预测失败: {str(e)}")


@router.post("/prediction/generate")
async def generate_prediction(db: Session = Depends(get_db)):
    """生成新的预测"""
    try:
        # 获取最近的历史数据
        recent_draws = db.query(Draw).order_by(Draw.draw_time.desc()).limit(50).all()
        
        if not recent_draws or len(recent_draws) < 10:
            raise HTTPException(status_code=400, detail="历史数据不足，无法生成预测")
        
        # 按时间排序（从早到晚）
        sorted_draws = sorted(recent_draws, key=lambda x: x.draw_time)
        
        # 提取特码序列
        special_numbers = [draw.special_number for draw in sorted_draws if draw.special_number]
        
        # 生成预测
        prediction_result = prediction_service.predict(special_numbers)
        
        if "error" in prediction_result:
            raise HTTPException(status_code=500, detail=prediction_result["error"])
        
        return {
            "status": "success",
            "data": prediction_result
        }
    
    except HTTPException as he:
        raise he
    
    except Exception as e:
        logger.error(f"Error generating prediction: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成预测失败: {str(e)}")


@router.post("/prediction/train")
async def train_models(db: Session = Depends(get_db)):
    """训练预测模型"""
    try:
        # 获取历史数据
        draws = db.query(Draw).order_by(Draw.draw_time).all()
        
        if not draws or len(draws) < 50:
            raise HTTPException(status_code=400, detail="历史数据不足，无法训练模型")
        
        # 提取特码序列
        special_numbers = [draw.special_number for draw in draws if draw.special_number]
        
        # 训练模型
        training_result = prediction_service.train_models(special_numbers)
        
        if training_result["status"] == "failed":
            raise HTTPException(status_code=500, detail=training_result["error"])
        
        return {
            "status": "success",
            "data": training_result["metrics"]
        }
    
    except HTTPException as he:
        raise he
    
    except Exception as e:
        logger.error(f"Error training models: {str(e)}")
        raise HTTPException(status_code=500, detail=f"训练模型失败: {str(e)}")


@router.get("/statistics/enhanced")
async def get_enhanced_statistics(db: Session = Depends(get_db)):
    """获取增强版统计数据"""
    try:
        # 获取所有历史数据
        draws = db.query(Draw).all()
        
        if not draws:
            raise HTTPException(status_code=404, detail="没有找到历史数据")
        
        # 计算统计数据
        statistics = statistics_service.calculate_statistics(draws)
        
        return {
            "status": "success",
            "data": statistics
        }
    
    except Exception as e:
        logger.error(f"Error calculating enhanced statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"计算统计数据失败: {str(e)}")


@router.get("/statistics/missing")
async def get_missing_values(db: Session = Depends(get_db)):
    """获取遗漏值统计"""
    try:
        # 获取所有历史数据
        draws = db.query(Draw).order_by(Draw.draw_time).all()
        
        if not draws:
            raise HTTPException(status_code=404, detail="没有找到历史数据")
        
        # 计算遗漏值
        missing_values = statistics_service.calculate_missing_values(draws)
        
        return {
            "status": "success",
            "data": missing_values
        }
    
    except Exception as e:
        logger.error(f"Error calculating missing values: {str(e)}")
        raise HTTPException(status_code=500, detail=f"计算遗漏值失败: {str(e)}")


@router.get("/statistics/probability")
async def get_probability_distribution(
    window_size: int = Query(100, description="使用最近多少期数据计算概率"),
    db: Session = Depends(get_db)
):
    """获取概率分布"""
    try:
        # 获取历史数据
        draws = db.query(Draw).order_by(Draw.draw_time).all()
        
        if not draws:
            raise HTTPException(status_code=404, detail="没有找到历史数据")
        
        if len(draws) < window_size:
            window_size = len(draws)
        
        # 计算概率分布
        probabilities = statistics_service.calculate_probability_distribution(draws, window_size)
        
        return {
            "status": "success",
            "data": {
                "probabilities": probabilities,
                "window_size": window_size
            }
        }
    
    except Exception as e:
        logger.error(f"Error calculating probability distribution: {str(e)}")
        raise HTTPException(status_code=500, detail=f"计算概率分布失败: {str(e)}")


@router.post("/prediction/evaluate")
async def evaluate_predictions(db: Session = Depends(get_db)):
    """评估预测准确度"""
    try:
        # 获取有实际结果的预测记录
        predictions = db.query(Prediction).filter(Prediction.actual_result.isnot(None)).all()
        
        if not predictions:
            raise HTTPException(status_code=404, detail="没有找到可评估的预测记录")
        
        # 提取预测和实际结果
        prediction_data = [
            {
                "special_numbers_5": p.special_numbers_5,
                "special_numbers_10": p.special_numbers_10,
                "special_numbers_20": p.special_numbers_20,
                "attribute_predictions": p.attribute_predictions
            } for p in predictions
        ]
        actual_results = [p.actual_result for p in predictions]
        
        # 评估预测准确度
        evaluation = statistics_service.evaluate_prediction_accuracy(prediction_data, actual_results)
        
        # 保存回测结果
        backtest = BacktestResult(
            start_date=predictions[0].prediction_time.date(),
            end_date=predictions[-1].prediction_time.date(),
            execution_time=datetime.now(),
            total_predictions=len(predictions),
            hits=int(evaluation["hit_rate_top20"] * len(predictions)),
            average_accuracy=float(evaluation["overall_accuracy"]),
            roi=float(evaluation["hit_rate_top5"] * 10 - 1),  # 简单ROI计算
            model_performance=evaluation
        )
        
        db.add(backtest)
        db.commit()
        
        return {
            "status": "success",
            "data": evaluation
        }
    
    except Exception as e:
        logger.error(f"Error evaluating predictions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"评估预测失败: {str(e)}")