<template>
  <div class="dashboard">
    <el-container>
      <el-header>
        <h1>智能数字竞猜预测系统</h1>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <!-- 预测结果展示 -->
          <el-col :span="12">
            <el-card class="prediction-card">
              <template #header>
                <div class="card-header">
                  <span>预测结果</span>
                  <el-button type="primary" @click="getPrediction">
                    开始预测
                  </el-button>
                </div>
              </template>
              
              <div v-if="predictionResult">
                <h3>核心号码</h3>
                <el-tag 
                  v-for="num in predictionResult.numbers['5码核心']"
                  :key="num"
                  type="success"
                  class="number-tag"
                >
                  {{ num }}
                </el-tag>
                
                <h3>属性分析</h3>
                <el-descriptions :column="2" border>
                  <el-descriptions-item 
                    v-for="(value, key) in predictionResult.attributes"
                    :key="key"
                    :label="key"
                  >
                    {{ value }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </el-col>
          
          <!-- 历史数据分析 -->
          <el-col :span="12">
            <el-card class="analysis-card">
              <template #header>
                <div class="card-header">
                  <span>历史数据分析</span>
                </div>
              </template>
              
              <div ref="chartContainer" style="height: 400px"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage } from 'element-plus'

export default {
  name: 'PredictionDashboard',
  
  setup() {
    const predictionResult = ref(null)
    const chart = ref(null)
    
    const getPrediction = async () => {
      try {
        const response = await axios.post('/api/predict', {
          history: await getHistoricalData()
        })
        predictionResult.value = response.data
        ElMessage.success('预测完成')
      } catch (error) {
        ElMessage.error('预测失败: ' + error.message)
      }
    }
    
    onMounted(() => {
      initChart()
    })
    
    return {
      predictionResult,
      getPrediction
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.number-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 